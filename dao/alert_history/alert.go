package alert_history

import (
	"context"
	"encoding/json"
	"fmt"
	"gopkg.in/errgo.v2/fmt/errors"
	"strconv"
	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type ListAlertReq struct {
	SvcID    string
	UserName string
}

type AlertHistoryDAO struct {
}

func (a AlertHistoryDAO) Create(ctx context.Context, alert *models.AlertsHistoryPO) error {
	return clients.InfluxdbCli.WriteWithTime(alert)
}

func (a AlertHistoryDAO) ListBySvcID(ctx context.Context, param ListAlertReq) ([]*models.AlertsHistoryPO, error) {
	alertPOs := make([]*models.AlertsHistoryPO, 0)
	req := &influxdb.QueryInfluxReq{
		Measurement: models.AlertHistoryTable,
	}
	if param.SvcID != "" {
		req.TagFilter = &influxdb.TagFilter{
			models.AlertTagServiceID: []string{param.SvcID},
		}
	}
	if param.UserName != "" {
		req.TagLikeFilter = &influxdb.TagLikeFilter{
			models.AlertTagReceivers: []string{param.UserName},
		}
	}
	sql := req.Sql()
	res, err := clients.InfluxdbCli.QueryInflux(sql)
	if err != nil {
		return nil, err
	}
	if len(res.Results) == 0 {
		stdlog.Infof("not alerts found")
		return alertPOs, nil
	}
	// 仅用到了单条查询语句
	alertPOs, err = models.ParseAlertsHistory(res.Results[0])
	if err != nil {
		return nil, err
	}
	return alertPOs, nil

}

func (a AlertHistoryDAO) DelByIDs(ctx context.Context, IDs []string) error {
	for _, id := range IDs {
		sql := fmt.Sprintf("DELETE FROM %s WHERE \"time\"=%s;", models.AlertHistoryTable, id)
		_, err := clients.InfluxdbCli.QueryInflux(sql)
		if err != nil {
			return nil
		}
	}
	return nil
}

func (a AlertHistoryDAO) QueryByID(ctx context.Context, ID string) (*models.AlertsHistoryPO, error) {
	req := &influxdb.QueryInfluxReq{
		Measurement: models.AlertHistoryTable,
		TagFilter: &influxdb.TagFilter{
			models.AlertTagID: []string{ID},
		},
	}
	res, err := clients.InfluxdbCli.QueryInflux(req.Sql())
	if err != nil {
		return nil, err
	}
	if len(res.Results) == 0 {
		return nil, errors.Newf("record not found by id :%v", ID)
	}
	// 仅用到了单条查询语句
	alerts, err := models.ParseAlertsHistory(res.Results[0])
	if err != nil {
		return nil, err
	}
	return alerts[0], nil
}

func (a AlertHistoryDAO) UpdateReadStateByID(ctx context.Context, ID string, state bool) error {
	alert, err := a.QueryByID(ctx, ID)
	if err != nil {
		return err
	}
	alert.HasBeenRead = strconv.FormatBool(state)

	if err := a.Create(ctx, alert); err != nil {
		return err
	}
	return nil
}

func (a AlertHistoryDAO) UpdateAlertingReceiversBySvcID(ctx context.Context, svcID string, receivers []string) error {
	alerts, err := a.ListBySvcID(ctx, ListAlertReq{
		SvcID: svcID,
	})
	bytes, err := json.Marshal(receivers)
	if err != nil {
		return err
	}
	for _, alert := range alerts {
		alert.Receivers = string(bytes)
		if err := a.DelByIDs(ctx, []string{alert.ID}); err != nil {
			return err
		}
		if err := a.Create(ctx, alert); err != nil {
			return err
		}
	}
	return nil
}
