package dao

import (
	"gorm.io/gorm"
	"transwarp.io/aip/llmops-common/pkg/expense"
)

type ExpenseRulesDao struct {
	db *gorm.DB
}

func NewExpenseRuleDao(db *gorm.DB) *ExpenseRulesDao {
	return &ExpenseRulesDao{db: db}
}

func (d ExpenseRulesDao) FindList(expenseRule *ExpenseRule) ([]*ExpenseRule, error) {
	var list []*ExpenseRule
	err := d.Clone().Where(expenseRule).Find(&list).Error
	return list, err
}

func (d ExpenseRulesDao) GetRuleByType(expenseRuleType expense.ExpenseRuleType) (*ExpenseRule, error) {
	rule := &ExpenseRule{}
	err := d.Clone().Where("type = ?", expenseRuleType).First(rule).Error
	return rule, err
}

func (d ExpenseRulesDao) GetRuleByTypes(expenseRuleTypes []string) ([]*ExpenseRule, error) {
	rules := []*ExpenseRule{}
	err := d.<PERSON><PERSON>().Find(&rules, "type in ?", expenseRuleTypes).Error
	return rules, err
}

func (d *ExpenseRulesDao) FindExpenseRuleById(id uint) (*ExpenseRule, error) {
	var r ExpenseRule
	err := d.Clone().Unscoped().Where("id = ?", id).Find(&r).Error
	if err != nil {
		return nil, err
	}
	return &r, nil
}

func (d ExpenseRulesDao) SaveExpenseRule(expenseRule *ExpenseRule) error {
	return d.Clone().Transaction(func(tx *gorm.DB) error { return tx.Save(expenseRule).Error })
}

func (d ExpenseRulesDao) Clone() *gorm.DB {
	return d.db.Session(&gorm.Session{NewDB: true})
}

type ExpenseRule struct {
	gorm.Model
	Name        string                  `json:"name" gorm:"type:varchar(200);"`
	NameLocals  map[string]string       `json:"-" gorm:"type:json;serializer:json"` // 国际化翻译 json: {en:"..."}
	Description string                  `json:"description" gorm:"type:text"`
	DescLocals  map[string]string       `json:"-" gorm:"type:json;serializer:json"` // json: {en:"..."}
	Price       float64                 `json:"price" gorm:"type:decimal(10,6);"`
	Unit        string                  `json:"unit" gorm:"type:varchar(50);"`
	Type        expense.ExpenseRuleType `json:"type" gorm:"type:varchar(50);"`
	CpuCore     int                     `json:"cpuCore" gorm:"type:int(11);"`
	CpuModelId  uint                    `json:"cpuModelId" gorm:"type:int(11);"`
	Memory      int                     `json:"memory" gorm:"type:int(11);"`
	Storage     int                     `json:"storage" gorm:"type:int(11);"`
	GpuNum      float64                 `json:"gpuNum" gorm:"type:decimal(10,2);"`
	GpuModelId  uint                    `json:"gpuModelId"`
	Token       int                     `json:"token" gorm:"type:int(11);"`
	Status      expense.RuleStatus      `json:"status" gorm:"type:tinyint(1);default:1"`
	CreatedUser string                  `json:"createdUser" gorm:"type:varchar(50);"`
	UpdatedUser string                  `json:"updatedUser" gorm:"type:varchar(50);"`
	CPUArch     string                  `json:"cpuArch" gorm:"type:varchar(50);"`

	CpuModel *CpuModel `json:"cpuModel" gorm:"-"`
	GpuModel *GpuModel `json:"gpuModel" gorm:"-"`
}
