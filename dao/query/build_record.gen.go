// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/aiot/csm-backend/models"
)

func newBuildRecord(db *gorm.DB, opts ...gen.DOOption) buildRecord {
	_buildRecord := buildRecord{}

	_buildRecord.buildRecordDo.UseDB(db, opts...)
	_buildRecord.buildRecordDo.UseModel(&models.BuildRecord{})

	tableName := _buildRecord.buildRecordDo.TableName()
	_buildRecord.ALL = field.NewAsterisk(tableName)
	_buildRecord.ID = field.NewString(tableName, "id")
	_buildRecord.Name = field.NewString(tableName, "name")
	_buildRecord.InstanceID = field.NewString(tableName, "instance_id")
	_buildRecord.BuilderIp = field.NewString(tableName, "builder_ip")
	_buildRecord.Status = field.NewString(tableName, "status")
	_buildRecord.Creator = field.NewString(tableName, "creator")
	_buildRecord.CreateAt = field.NewTime(tableName, "create_at")
	_buildRecord.FinishAt = field.NewTime(tableName, "finish_at")
	_buildRecord.ProjectID = field.NewString(tableName, "project_id")

	_buildRecord.fillFieldMap()

	return _buildRecord
}

type buildRecord struct {
	buildRecordDo

	ALL        field.Asterisk
	ID         field.String
	Name       field.String
	InstanceID field.String
	BuilderIp  field.String
	Status     field.String
	Creator    field.String
	CreateAt   field.Time
	FinishAt   field.Time
	ProjectID  field.String

	fieldMap map[string]field.Expr
}

func (b buildRecord) Table(newTableName string) *buildRecord {
	b.buildRecordDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b buildRecord) As(alias string) *buildRecord {
	b.buildRecordDo.DO = *(b.buildRecordDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *buildRecord) updateTableName(table string) *buildRecord {
	b.ALL = field.NewAsterisk(table)
	b.ID = field.NewString(table, "id")
	b.Name = field.NewString(table, "name")
	b.InstanceID = field.NewString(table, "instance_id")
	b.BuilderIp = field.NewString(table, "builder_ip")
	b.Status = field.NewString(table, "status")
	b.Creator = field.NewString(table, "creator")
	b.CreateAt = field.NewTime(table, "create_at")
	b.FinishAt = field.NewTime(table, "finish_at")
	b.ProjectID = field.NewString(table, "project_id")

	b.fillFieldMap()

	return b
}

func (b *buildRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *buildRecord) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 9)
	b.fieldMap["id"] = b.ID
	b.fieldMap["name"] = b.Name
	b.fieldMap["instance_id"] = b.InstanceID
	b.fieldMap["builder_ip"] = b.BuilderIp
	b.fieldMap["status"] = b.Status
	b.fieldMap["creator"] = b.Creator
	b.fieldMap["create_at"] = b.CreateAt
	b.fieldMap["finish_at"] = b.FinishAt
	b.fieldMap["project_id"] = b.ProjectID
}

func (b buildRecord) clone(db *gorm.DB) buildRecord {
	b.buildRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b buildRecord) replaceDB(db *gorm.DB) buildRecord {
	b.buildRecordDo.ReplaceDB(db)
	return b
}

type buildRecordDo struct{ gen.DO }

type IBuildRecordDo interface {
	gen.SubQuery
	Debug() IBuildRecordDo
	WithContext(ctx context.Context) IBuildRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IBuildRecordDo
	WriteDB() IBuildRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IBuildRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IBuildRecordDo
	Not(conds ...gen.Condition) IBuildRecordDo
	Or(conds ...gen.Condition) IBuildRecordDo
	Select(conds ...field.Expr) IBuildRecordDo
	Where(conds ...gen.Condition) IBuildRecordDo
	Order(conds ...field.Expr) IBuildRecordDo
	Distinct(cols ...field.Expr) IBuildRecordDo
	Omit(cols ...field.Expr) IBuildRecordDo
	Join(table schema.Tabler, on ...field.Expr) IBuildRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IBuildRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IBuildRecordDo
	Group(cols ...field.Expr) IBuildRecordDo
	Having(conds ...gen.Condition) IBuildRecordDo
	Limit(limit int) IBuildRecordDo
	Offset(offset int) IBuildRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IBuildRecordDo
	Unscoped() IBuildRecordDo
	Create(values ...*models.BuildRecord) error
	CreateInBatches(values []*models.BuildRecord, batchSize int) error
	Save(values ...*models.BuildRecord) error
	First() (*models.BuildRecord, error)
	Take() (*models.BuildRecord, error)
	Last() (*models.BuildRecord, error)
	Find() ([]*models.BuildRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.BuildRecord, err error)
	FindInBatches(result *[]*models.BuildRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.BuildRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IBuildRecordDo
	Assign(attrs ...field.AssignExpr) IBuildRecordDo
	Joins(fields ...field.RelationField) IBuildRecordDo
	Preload(fields ...field.RelationField) IBuildRecordDo
	FirstOrInit() (*models.BuildRecord, error)
	FirstOrCreate() (*models.BuildRecord, error)
	FindByPage(offset int, limit int) (result []*models.BuildRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IBuildRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (b buildRecordDo) Debug() IBuildRecordDo {
	return b.withDO(b.DO.Debug())
}

func (b buildRecordDo) WithContext(ctx context.Context) IBuildRecordDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b buildRecordDo) ReadDB() IBuildRecordDo {
	return b.Clauses(dbresolver.Read)
}

func (b buildRecordDo) WriteDB() IBuildRecordDo {
	return b.Clauses(dbresolver.Write)
}

func (b buildRecordDo) Session(config *gorm.Session) IBuildRecordDo {
	return b.withDO(b.DO.Session(config))
}

func (b buildRecordDo) Clauses(conds ...clause.Expression) IBuildRecordDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b buildRecordDo) Returning(value interface{}, columns ...string) IBuildRecordDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b buildRecordDo) Not(conds ...gen.Condition) IBuildRecordDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b buildRecordDo) Or(conds ...gen.Condition) IBuildRecordDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b buildRecordDo) Select(conds ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b buildRecordDo) Where(conds ...gen.Condition) IBuildRecordDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b buildRecordDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) IBuildRecordDo {
	return b.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (b buildRecordDo) Order(conds ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b buildRecordDo) Distinct(cols ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b buildRecordDo) Omit(cols ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b buildRecordDo) Join(table schema.Tabler, on ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b buildRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b buildRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b buildRecordDo) Group(cols ...field.Expr) IBuildRecordDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b buildRecordDo) Having(conds ...gen.Condition) IBuildRecordDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b buildRecordDo) Limit(limit int) IBuildRecordDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b buildRecordDo) Offset(offset int) IBuildRecordDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b buildRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IBuildRecordDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b buildRecordDo) Unscoped() IBuildRecordDo {
	return b.withDO(b.DO.Unscoped())
}

func (b buildRecordDo) Create(values ...*models.BuildRecord) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b buildRecordDo) CreateInBatches(values []*models.BuildRecord, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b buildRecordDo) Save(values ...*models.BuildRecord) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b buildRecordDo) First() (*models.BuildRecord, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.BuildRecord), nil
	}
}

func (b buildRecordDo) Take() (*models.BuildRecord, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.BuildRecord), nil
	}
}

func (b buildRecordDo) Last() (*models.BuildRecord, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.BuildRecord), nil
	}
}

func (b buildRecordDo) Find() ([]*models.BuildRecord, error) {
	result, err := b.DO.Find()
	return result.([]*models.BuildRecord), err
}

func (b buildRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.BuildRecord, err error) {
	buf := make([]*models.BuildRecord, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b buildRecordDo) FindInBatches(result *[]*models.BuildRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b buildRecordDo) Attrs(attrs ...field.AssignExpr) IBuildRecordDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b buildRecordDo) Assign(attrs ...field.AssignExpr) IBuildRecordDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b buildRecordDo) Joins(fields ...field.RelationField) IBuildRecordDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b buildRecordDo) Preload(fields ...field.RelationField) IBuildRecordDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b buildRecordDo) FirstOrInit() (*models.BuildRecord, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.BuildRecord), nil
	}
}

func (b buildRecordDo) FirstOrCreate() (*models.BuildRecord, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.BuildRecord), nil
	}
}

func (b buildRecordDo) FindByPage(offset int, limit int) (result []*models.BuildRecord, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b buildRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b buildRecordDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b buildRecordDo) Delete(models ...*models.BuildRecord) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *buildRecordDo) withDO(do gen.Dao) *buildRecordDo {
	b.DO = *do.(*gen.DO)
	return b
}
