// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/aiot/cvat-backend/models"
)

func newSampleDatasetImageRelation(db *gorm.DB, opts ...gen.DOOption) sampleDatasetImageRelation {
	_sampleDatasetImageRelation := sampleDatasetImageRelation{}

	_sampleDatasetImageRelation.sampleDatasetImageRelationDo.UseDB(db, opts...)
	_sampleDatasetImageRelation.sampleDatasetImageRelationDo.UseModel(&models.SampleDatasetImageRelation{})

	tableName := _sampleDatasetImageRelation.sampleDatasetImageRelationDo.TableName()
	_sampleDatasetImageRelation.ALL = field.NewAsterisk(tableName)
	_sampleDatasetImageRelation.SampleImageID = field.NewString(tableName, "sample_image_id")
	_sampleDatasetImageRelation.SampleImageDatasetID = field.NewInt32(tableName, "sample_image_dataset_id")

	_sampleDatasetImageRelation.fillFieldMap()

	return _sampleDatasetImageRelation
}

type sampleDatasetImageRelation struct {
	sampleDatasetImageRelationDo

	ALL                  field.Asterisk
	SampleImageID        field.String
	SampleImageDatasetID field.Int32

	fieldMap map[string]field.Expr
}

func (s sampleDatasetImageRelation) Table(newTableName string) *sampleDatasetImageRelation {
	s.sampleDatasetImageRelationDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sampleDatasetImageRelation) As(alias string) *sampleDatasetImageRelation {
	s.sampleDatasetImageRelationDo.DO = *(s.sampleDatasetImageRelationDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sampleDatasetImageRelation) updateTableName(table string) *sampleDatasetImageRelation {
	s.ALL = field.NewAsterisk(table)
	s.SampleImageID = field.NewString(table, "sample_image_id")
	s.SampleImageDatasetID = field.NewInt32(table, "sample_image_dataset_id")

	s.fillFieldMap()

	return s
}

func (s *sampleDatasetImageRelation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sampleDatasetImageRelation) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 2)
	s.fieldMap["sample_image_id"] = s.SampleImageID
	s.fieldMap["sample_image_dataset_id"] = s.SampleImageDatasetID
}

func (s sampleDatasetImageRelation) clone(db *gorm.DB) sampleDatasetImageRelation {
	s.sampleDatasetImageRelationDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sampleDatasetImageRelation) replaceDB(db *gorm.DB) sampleDatasetImageRelation {
	s.sampleDatasetImageRelationDo.ReplaceDB(db)
	return s
}

type sampleDatasetImageRelationDo struct{ gen.DO }

type ISampleDatasetImageRelationDo interface {
	gen.SubQuery
	Debug() ISampleDatasetImageRelationDo
	WithContext(ctx context.Context) ISampleDatasetImageRelationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISampleDatasetImageRelationDo
	WriteDB() ISampleDatasetImageRelationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISampleDatasetImageRelationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISampleDatasetImageRelationDo
	Not(conds ...gen.Condition) ISampleDatasetImageRelationDo
	Or(conds ...gen.Condition) ISampleDatasetImageRelationDo
	Select(conds ...field.Expr) ISampleDatasetImageRelationDo
	Where(conds ...gen.Condition) ISampleDatasetImageRelationDo
	Order(conds ...field.Expr) ISampleDatasetImageRelationDo
	Distinct(cols ...field.Expr) ISampleDatasetImageRelationDo
	Omit(cols ...field.Expr) ISampleDatasetImageRelationDo
	Join(table schema.Tabler, on ...field.Expr) ISampleDatasetImageRelationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISampleDatasetImageRelationDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISampleDatasetImageRelationDo
	Group(cols ...field.Expr) ISampleDatasetImageRelationDo
	Having(conds ...gen.Condition) ISampleDatasetImageRelationDo
	Limit(limit int) ISampleDatasetImageRelationDo
	Offset(offset int) ISampleDatasetImageRelationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISampleDatasetImageRelationDo
	Unscoped() ISampleDatasetImageRelationDo
	Create(values ...*models.SampleDatasetImageRelation) error
	CreateInBatches(values []*models.SampleDatasetImageRelation, batchSize int) error
	Save(values ...*models.SampleDatasetImageRelation) error
	First() (*models.SampleDatasetImageRelation, error)
	Take() (*models.SampleDatasetImageRelation, error)
	Last() (*models.SampleDatasetImageRelation, error)
	Find() ([]*models.SampleDatasetImageRelation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.SampleDatasetImageRelation, err error)
	FindInBatches(result *[]*models.SampleDatasetImageRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.SampleDatasetImageRelation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISampleDatasetImageRelationDo
	Assign(attrs ...field.AssignExpr) ISampleDatasetImageRelationDo
	Joins(fields ...field.RelationField) ISampleDatasetImageRelationDo
	Preload(fields ...field.RelationField) ISampleDatasetImageRelationDo
	FirstOrInit() (*models.SampleDatasetImageRelation, error)
	FirstOrCreate() (*models.SampleDatasetImageRelation, error)
	FindByPage(offset int, limit int) (result []*models.SampleDatasetImageRelation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISampleDatasetImageRelationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s sampleDatasetImageRelationDo) Debug() ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Debug())
}

func (s sampleDatasetImageRelationDo) WithContext(ctx context.Context) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sampleDatasetImageRelationDo) ReadDB() ISampleDatasetImageRelationDo {
	return s.Clauses(dbresolver.Read)
}

func (s sampleDatasetImageRelationDo) WriteDB() ISampleDatasetImageRelationDo {
	return s.Clauses(dbresolver.Write)
}

func (s sampleDatasetImageRelationDo) Session(config *gorm.Session) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Session(config))
}

func (s sampleDatasetImageRelationDo) Clauses(conds ...clause.Expression) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sampleDatasetImageRelationDo) Returning(value interface{}, columns ...string) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sampleDatasetImageRelationDo) Not(conds ...gen.Condition) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sampleDatasetImageRelationDo) Or(conds ...gen.Condition) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sampleDatasetImageRelationDo) Select(conds ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sampleDatasetImageRelationDo) Where(conds ...gen.Condition) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sampleDatasetImageRelationDo) Order(conds ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sampleDatasetImageRelationDo) Distinct(cols ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sampleDatasetImageRelationDo) Omit(cols ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sampleDatasetImageRelationDo) Join(table schema.Tabler, on ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sampleDatasetImageRelationDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sampleDatasetImageRelationDo) RightJoin(table schema.Tabler, on ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sampleDatasetImageRelationDo) Group(cols ...field.Expr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sampleDatasetImageRelationDo) Having(conds ...gen.Condition) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sampleDatasetImageRelationDo) Limit(limit int) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sampleDatasetImageRelationDo) Offset(offset int) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sampleDatasetImageRelationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sampleDatasetImageRelationDo) Unscoped() ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sampleDatasetImageRelationDo) Create(values ...*models.SampleDatasetImageRelation) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sampleDatasetImageRelationDo) CreateInBatches(values []*models.SampleDatasetImageRelation, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sampleDatasetImageRelationDo) Save(values ...*models.SampleDatasetImageRelation) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sampleDatasetImageRelationDo) First() (*models.SampleDatasetImageRelation, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.SampleDatasetImageRelation), nil
	}
}

func (s sampleDatasetImageRelationDo) Take() (*models.SampleDatasetImageRelation, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.SampleDatasetImageRelation), nil
	}
}

func (s sampleDatasetImageRelationDo) Last() (*models.SampleDatasetImageRelation, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.SampleDatasetImageRelation), nil
	}
}

func (s sampleDatasetImageRelationDo) Find() ([]*models.SampleDatasetImageRelation, error) {
	result, err := s.DO.Find()
	return result.([]*models.SampleDatasetImageRelation), err
}

func (s sampleDatasetImageRelationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.SampleDatasetImageRelation, err error) {
	buf := make([]*models.SampleDatasetImageRelation, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sampleDatasetImageRelationDo) FindInBatches(result *[]*models.SampleDatasetImageRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sampleDatasetImageRelationDo) Attrs(attrs ...field.AssignExpr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sampleDatasetImageRelationDo) Assign(attrs ...field.AssignExpr) ISampleDatasetImageRelationDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sampleDatasetImageRelationDo) Joins(fields ...field.RelationField) ISampleDatasetImageRelationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sampleDatasetImageRelationDo) Preload(fields ...field.RelationField) ISampleDatasetImageRelationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sampleDatasetImageRelationDo) FirstOrInit() (*models.SampleDatasetImageRelation, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.SampleDatasetImageRelation), nil
	}
}

func (s sampleDatasetImageRelationDo) FirstOrCreate() (*models.SampleDatasetImageRelation, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.SampleDatasetImageRelation), nil
	}
}

func (s sampleDatasetImageRelationDo) FindByPage(offset int, limit int) (result []*models.SampleDatasetImageRelation, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sampleDatasetImageRelationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sampleDatasetImageRelationDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sampleDatasetImageRelationDo) Delete(models ...*models.SampleDatasetImageRelation) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sampleDatasetImageRelationDo) withDO(do gen.Dao) *sampleDatasetImageRelationDo {
	s.DO = *do.(*gen.DO)
	return s
}
