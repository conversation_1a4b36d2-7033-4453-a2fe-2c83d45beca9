// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/aiot/cvat-backend/models"
)

func newAnnotatedObjLabel(db *gorm.DB, opts ...gen.DOOption) annotatedObjLabel {
	_annotatedObjLabel := annotatedObjLabel{}

	_annotatedObjLabel.annotatedObjLabelDo.UseDB(db, opts...)
	_annotatedObjLabel.annotatedObjLabelDo.UseModel(&models.AnnotatedObjLabel{})

	tableName := _annotatedObjLabel.annotatedObjLabelDo.TableName()
	_annotatedObjLabel.ALL = field.NewAsterisk(tableName)
	_annotatedObjLabel.ID = field.NewInt32(tableName, "id")
	_annotatedObjLabel.LabelPosition = field.NewField(tableName, "label_position")
	_annotatedObjLabel.Annotator = field.NewString(tableName, "annotator")
	_annotatedObjLabel.AnnotationSetObjID = field.NewInt32(tableName, "annotation_set_obj_id")
	_annotatedObjLabel.Confidence = field.NewFloat64(tableName, "confidence")
	_annotatedObjLabel.ParentID = field.NewInt32(tableName, "parent_id")
	_annotatedObjLabel.LabelID = field.NewInt32(tableName, "label_id")
	_annotatedObjLabel.Label = annotatedObjLabelBelongsToLabel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Label", "models.Label"),
		AnnotationTasks: struct {
			field.RelationField
			ImageSets struct {
				field.RelationField
				TextFiles struct {
					field.RelationField
				}
				TextVersions struct {
					field.RelationField
					TextFiles struct {
						field.RelationField
					}
				}
				Images struct {
					field.RelationField
					Datasets struct {
						field.RelationField
					}
				}
			}
			Labels struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Label.AnnotationTasks", "models.AnnotationTask"),
			ImageSets: struct {
				field.RelationField
				TextFiles struct {
					field.RelationField
				}
				TextVersions struct {
					field.RelationField
					TextFiles struct {
						field.RelationField
					}
				}
				Images struct {
					field.RelationField
					Datasets struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("Label.AnnotationTasks.ImageSets", "models.SampleImageDataset"),
				TextFiles: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Label.AnnotationTasks.ImageSets.TextFiles", "models.SampleText"),
				},
				TextVersions: struct {
					field.RelationField
					TextFiles struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Label.AnnotationTasks.ImageSets.TextVersions", "models.SampleTextVersion"),
					TextFiles: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Label.AnnotationTasks.ImageSets.TextVersions.TextFiles", "models.SampleTextFile"),
					},
				},
				Images: struct {
					field.RelationField
					Datasets struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Label.AnnotationTasks.ImageSets.Images", "models.SampleImage"),
					Datasets: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Label.AnnotationTasks.ImageSets.Images.Datasets", "models.SampleImageDataset"),
					},
				},
			},
			Labels: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Label.AnnotationTasks.Labels", "models.Label"),
			},
		},
	}

	_annotatedObjLabel.fillFieldMap()

	return _annotatedObjLabel
}

type annotatedObjLabel struct {
	annotatedObjLabelDo

	ALL                field.Asterisk
	ID                 field.Int32
	LabelPosition      field.Field
	Annotator          field.String
	AnnotationSetObjID field.Int32
	Confidence         field.Float64
	ParentID           field.Int32
	LabelID            field.Int32
	Label              annotatedObjLabelBelongsToLabel

	fieldMap map[string]field.Expr
}

func (a annotatedObjLabel) Table(newTableName string) *annotatedObjLabel {
	a.annotatedObjLabelDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a annotatedObjLabel) As(alias string) *annotatedObjLabel {
	a.annotatedObjLabelDo.DO = *(a.annotatedObjLabelDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *annotatedObjLabel) updateTableName(table string) *annotatedObjLabel {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.LabelPosition = field.NewField(table, "label_position")
	a.Annotator = field.NewString(table, "annotator")
	a.AnnotationSetObjID = field.NewInt32(table, "annotation_set_obj_id")
	a.Confidence = field.NewFloat64(table, "confidence")
	a.ParentID = field.NewInt32(table, "parent_id")
	a.LabelID = field.NewInt32(table, "label_id")

	a.fillFieldMap()

	return a
}

func (a *annotatedObjLabel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *annotatedObjLabel) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["label_position"] = a.LabelPosition
	a.fieldMap["annotator"] = a.Annotator
	a.fieldMap["annotation_set_obj_id"] = a.AnnotationSetObjID
	a.fieldMap["confidence"] = a.Confidence
	a.fieldMap["parent_id"] = a.ParentID
	a.fieldMap["label_id"] = a.LabelID

}

func (a annotatedObjLabel) clone(db *gorm.DB) annotatedObjLabel {
	a.annotatedObjLabelDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a annotatedObjLabel) replaceDB(db *gorm.DB) annotatedObjLabel {
	a.annotatedObjLabelDo.ReplaceDB(db)
	return a
}

type annotatedObjLabelBelongsToLabel struct {
	db *gorm.DB

	field.RelationField

	AnnotationTasks struct {
		field.RelationField
		ImageSets struct {
			field.RelationField
			TextFiles struct {
				field.RelationField
			}
			TextVersions struct {
				field.RelationField
				TextFiles struct {
					field.RelationField
				}
			}
			Images struct {
				field.RelationField
				Datasets struct {
					field.RelationField
				}
			}
		}
		Labels struct {
			field.RelationField
		}
	}
}

func (a annotatedObjLabelBelongsToLabel) Where(conds ...field.Expr) *annotatedObjLabelBelongsToLabel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a annotatedObjLabelBelongsToLabel) WithContext(ctx context.Context) *annotatedObjLabelBelongsToLabel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a annotatedObjLabelBelongsToLabel) Session(session *gorm.Session) *annotatedObjLabelBelongsToLabel {
	a.db = a.db.Session(session)
	return &a
}

func (a annotatedObjLabelBelongsToLabel) Model(m *models.AnnotatedObjLabel) *annotatedObjLabelBelongsToLabelTx {
	return &annotatedObjLabelBelongsToLabelTx{a.db.Model(m).Association(a.Name())}
}

type annotatedObjLabelBelongsToLabelTx struct{ tx *gorm.Association }

func (a annotatedObjLabelBelongsToLabelTx) Find() (result *models.Label, err error) {
	return result, a.tx.Find(&result)
}

func (a annotatedObjLabelBelongsToLabelTx) Append(values ...*models.Label) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a annotatedObjLabelBelongsToLabelTx) Replace(values ...*models.Label) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a annotatedObjLabelBelongsToLabelTx) Delete(values ...*models.Label) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a annotatedObjLabelBelongsToLabelTx) Clear() error {
	return a.tx.Clear()
}

func (a annotatedObjLabelBelongsToLabelTx) Count() int64 {
	return a.tx.Count()
}

type annotatedObjLabelDo struct{ gen.DO }

type IAnnotatedObjLabelDo interface {
	gen.SubQuery
	Debug() IAnnotatedObjLabelDo
	WithContext(ctx context.Context) IAnnotatedObjLabelDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAnnotatedObjLabelDo
	WriteDB() IAnnotatedObjLabelDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAnnotatedObjLabelDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAnnotatedObjLabelDo
	Not(conds ...gen.Condition) IAnnotatedObjLabelDo
	Or(conds ...gen.Condition) IAnnotatedObjLabelDo
	Select(conds ...field.Expr) IAnnotatedObjLabelDo
	Where(conds ...gen.Condition) IAnnotatedObjLabelDo
	Order(conds ...field.Expr) IAnnotatedObjLabelDo
	Distinct(cols ...field.Expr) IAnnotatedObjLabelDo
	Omit(cols ...field.Expr) IAnnotatedObjLabelDo
	Join(table schema.Tabler, on ...field.Expr) IAnnotatedObjLabelDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAnnotatedObjLabelDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAnnotatedObjLabelDo
	Group(cols ...field.Expr) IAnnotatedObjLabelDo
	Having(conds ...gen.Condition) IAnnotatedObjLabelDo
	Limit(limit int) IAnnotatedObjLabelDo
	Offset(offset int) IAnnotatedObjLabelDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnotatedObjLabelDo
	Unscoped() IAnnotatedObjLabelDo
	Create(values ...*models.AnnotatedObjLabel) error
	CreateInBatches(values []*models.AnnotatedObjLabel, batchSize int) error
	Save(values ...*models.AnnotatedObjLabel) error
	First() (*models.AnnotatedObjLabel, error)
	Take() (*models.AnnotatedObjLabel, error)
	Last() (*models.AnnotatedObjLabel, error)
	Find() ([]*models.AnnotatedObjLabel, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.AnnotatedObjLabel, err error)
	FindInBatches(result *[]*models.AnnotatedObjLabel, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.AnnotatedObjLabel) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAnnotatedObjLabelDo
	Assign(attrs ...field.AssignExpr) IAnnotatedObjLabelDo
	Joins(fields ...field.RelationField) IAnnotatedObjLabelDo
	Preload(fields ...field.RelationField) IAnnotatedObjLabelDo
	FirstOrInit() (*models.AnnotatedObjLabel, error)
	FirstOrCreate() (*models.AnnotatedObjLabel, error)
	FindByPage(offset int, limit int) (result []*models.AnnotatedObjLabel, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAnnotatedObjLabelDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a annotatedObjLabelDo) Debug() IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Debug())
}

func (a annotatedObjLabelDo) WithContext(ctx context.Context) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a annotatedObjLabelDo) ReadDB() IAnnotatedObjLabelDo {
	return a.Clauses(dbresolver.Read)
}

func (a annotatedObjLabelDo) WriteDB() IAnnotatedObjLabelDo {
	return a.Clauses(dbresolver.Write)
}

func (a annotatedObjLabelDo) Session(config *gorm.Session) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Session(config))
}

func (a annotatedObjLabelDo) Clauses(conds ...clause.Expression) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a annotatedObjLabelDo) Returning(value interface{}, columns ...string) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a annotatedObjLabelDo) Not(conds ...gen.Condition) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a annotatedObjLabelDo) Or(conds ...gen.Condition) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a annotatedObjLabelDo) Select(conds ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a annotatedObjLabelDo) Where(conds ...gen.Condition) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a annotatedObjLabelDo) Order(conds ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a annotatedObjLabelDo) Distinct(cols ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a annotatedObjLabelDo) Omit(cols ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a annotatedObjLabelDo) Join(table schema.Tabler, on ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a annotatedObjLabelDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a annotatedObjLabelDo) RightJoin(table schema.Tabler, on ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a annotatedObjLabelDo) Group(cols ...field.Expr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a annotatedObjLabelDo) Having(conds ...gen.Condition) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a annotatedObjLabelDo) Limit(limit int) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a annotatedObjLabelDo) Offset(offset int) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a annotatedObjLabelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a annotatedObjLabelDo) Unscoped() IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Unscoped())
}

func (a annotatedObjLabelDo) Create(values ...*models.AnnotatedObjLabel) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a annotatedObjLabelDo) CreateInBatches(values []*models.AnnotatedObjLabel, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a annotatedObjLabelDo) Save(values ...*models.AnnotatedObjLabel) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a annotatedObjLabelDo) First() (*models.AnnotatedObjLabel, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.AnnotatedObjLabel), nil
	}
}

func (a annotatedObjLabelDo) Take() (*models.AnnotatedObjLabel, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.AnnotatedObjLabel), nil
	}
}

func (a annotatedObjLabelDo) Last() (*models.AnnotatedObjLabel, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.AnnotatedObjLabel), nil
	}
}

func (a annotatedObjLabelDo) Find() ([]*models.AnnotatedObjLabel, error) {
	result, err := a.DO.Find()
	return result.([]*models.AnnotatedObjLabel), err
}

func (a annotatedObjLabelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.AnnotatedObjLabel, err error) {
	buf := make([]*models.AnnotatedObjLabel, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a annotatedObjLabelDo) FindInBatches(result *[]*models.AnnotatedObjLabel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a annotatedObjLabelDo) Attrs(attrs ...field.AssignExpr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a annotatedObjLabelDo) Assign(attrs ...field.AssignExpr) IAnnotatedObjLabelDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a annotatedObjLabelDo) Joins(fields ...field.RelationField) IAnnotatedObjLabelDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a annotatedObjLabelDo) Preload(fields ...field.RelationField) IAnnotatedObjLabelDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a annotatedObjLabelDo) FirstOrInit() (*models.AnnotatedObjLabel, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.AnnotatedObjLabel), nil
	}
}

func (a annotatedObjLabelDo) FirstOrCreate() (*models.AnnotatedObjLabel, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.AnnotatedObjLabel), nil
	}
}

func (a annotatedObjLabelDo) FindByPage(offset int, limit int) (result []*models.AnnotatedObjLabel, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a annotatedObjLabelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a annotatedObjLabelDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a annotatedObjLabelDo) Delete(models ...*models.AnnotatedObjLabel) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *annotatedObjLabelDo) withDO(do gen.Dao) *annotatedObjLabelDo {
	a.DO = *do.(*gen.DO)
	return a
}
