// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/aiot/cvat-backend/models"
)

func newCorpusOperatorTaskMapping(db *gorm.DB, opts ...gen.DOOption) corpusOperatorTaskMapping {
	_corpusOperatorTaskMapping := corpusOperatorTaskMapping{}

	_corpusOperatorTaskMapping.corpusOperatorTaskMappingDo.UseDB(db, opts...)
	_corpusOperatorTaskMapping.corpusOperatorTaskMappingDo.UseModel(&models.CorpusOperatorTaskMapping{})

	tableName := _corpusOperatorTaskMapping.corpusOperatorTaskMappingDo.TableName()
	_corpusOperatorTaskMapping.ALL = field.NewAsterisk(tableName)
	_corpusOperatorTaskMapping.ID = field.NewInt32(tableName, "id")
	_corpusOperatorTaskMapping.OperatorID = field.NewInt32(tableName, "operator_id")
	_corpusOperatorTaskMapping.TaskID = field.NewInt32(tableName, "task_id")
	_corpusOperatorTaskMapping.Phase = field.NewInt32(tableName, "phase")

	_corpusOperatorTaskMapping.fillFieldMap()

	return _corpusOperatorTaskMapping
}

type corpusOperatorTaskMapping struct {
	corpusOperatorTaskMappingDo

	ALL        field.Asterisk
	ID         field.Int32
	OperatorID field.Int32
	TaskID     field.Int32
	Phase      field.Int32

	fieldMap map[string]field.Expr
}

func (c corpusOperatorTaskMapping) Table(newTableName string) *corpusOperatorTaskMapping {
	c.corpusOperatorTaskMappingDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c corpusOperatorTaskMapping) As(alias string) *corpusOperatorTaskMapping {
	c.corpusOperatorTaskMappingDo.DO = *(c.corpusOperatorTaskMappingDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *corpusOperatorTaskMapping) updateTableName(table string) *corpusOperatorTaskMapping {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.OperatorID = field.NewInt32(table, "operator_id")
	c.TaskID = field.NewInt32(table, "task_id")
	c.Phase = field.NewInt32(table, "phase")

	c.fillFieldMap()

	return c
}

func (c *corpusOperatorTaskMapping) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *corpusOperatorTaskMapping) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 4)
	c.fieldMap["id"] = c.ID
	c.fieldMap["operator_id"] = c.OperatorID
	c.fieldMap["task_id"] = c.TaskID
	c.fieldMap["phase"] = c.Phase
}

func (c corpusOperatorTaskMapping) clone(db *gorm.DB) corpusOperatorTaskMapping {
	c.corpusOperatorTaskMappingDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c corpusOperatorTaskMapping) replaceDB(db *gorm.DB) corpusOperatorTaskMapping {
	c.corpusOperatorTaskMappingDo.ReplaceDB(db)
	return c
}

type corpusOperatorTaskMappingDo struct{ gen.DO }

type ICorpusOperatorTaskMappingDo interface {
	gen.SubQuery
	Debug() ICorpusOperatorTaskMappingDo
	WithContext(ctx context.Context) ICorpusOperatorTaskMappingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICorpusOperatorTaskMappingDo
	WriteDB() ICorpusOperatorTaskMappingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICorpusOperatorTaskMappingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICorpusOperatorTaskMappingDo
	Not(conds ...gen.Condition) ICorpusOperatorTaskMappingDo
	Or(conds ...gen.Condition) ICorpusOperatorTaskMappingDo
	Select(conds ...field.Expr) ICorpusOperatorTaskMappingDo
	Where(conds ...gen.Condition) ICorpusOperatorTaskMappingDo
	Order(conds ...field.Expr) ICorpusOperatorTaskMappingDo
	Distinct(cols ...field.Expr) ICorpusOperatorTaskMappingDo
	Omit(cols ...field.Expr) ICorpusOperatorTaskMappingDo
	Join(table schema.Tabler, on ...field.Expr) ICorpusOperatorTaskMappingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICorpusOperatorTaskMappingDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICorpusOperatorTaskMappingDo
	Group(cols ...field.Expr) ICorpusOperatorTaskMappingDo
	Having(conds ...gen.Condition) ICorpusOperatorTaskMappingDo
	Limit(limit int) ICorpusOperatorTaskMappingDo
	Offset(offset int) ICorpusOperatorTaskMappingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICorpusOperatorTaskMappingDo
	Unscoped() ICorpusOperatorTaskMappingDo
	Create(values ...*models.CorpusOperatorTaskMapping) error
	CreateInBatches(values []*models.CorpusOperatorTaskMapping, batchSize int) error
	Save(values ...*models.CorpusOperatorTaskMapping) error
	First() (*models.CorpusOperatorTaskMapping, error)
	Take() (*models.CorpusOperatorTaskMapping, error)
	Last() (*models.CorpusOperatorTaskMapping, error)
	Find() ([]*models.CorpusOperatorTaskMapping, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.CorpusOperatorTaskMapping, err error)
	FindInBatches(result *[]*models.CorpusOperatorTaskMapping, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.CorpusOperatorTaskMapping) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICorpusOperatorTaskMappingDo
	Assign(attrs ...field.AssignExpr) ICorpusOperatorTaskMappingDo
	Joins(fields ...field.RelationField) ICorpusOperatorTaskMappingDo
	Preload(fields ...field.RelationField) ICorpusOperatorTaskMappingDo
	FirstOrInit() (*models.CorpusOperatorTaskMapping, error)
	FirstOrCreate() (*models.CorpusOperatorTaskMapping, error)
	FindByPage(offset int, limit int) (result []*models.CorpusOperatorTaskMapping, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICorpusOperatorTaskMappingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c corpusOperatorTaskMappingDo) Debug() ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Debug())
}

func (c corpusOperatorTaskMappingDo) WithContext(ctx context.Context) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c corpusOperatorTaskMappingDo) ReadDB() ICorpusOperatorTaskMappingDo {
	return c.Clauses(dbresolver.Read)
}

func (c corpusOperatorTaskMappingDo) WriteDB() ICorpusOperatorTaskMappingDo {
	return c.Clauses(dbresolver.Write)
}

func (c corpusOperatorTaskMappingDo) Session(config *gorm.Session) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Session(config))
}

func (c corpusOperatorTaskMappingDo) Clauses(conds ...clause.Expression) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c corpusOperatorTaskMappingDo) Returning(value interface{}, columns ...string) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c corpusOperatorTaskMappingDo) Not(conds ...gen.Condition) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c corpusOperatorTaskMappingDo) Or(conds ...gen.Condition) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c corpusOperatorTaskMappingDo) Select(conds ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c corpusOperatorTaskMappingDo) Where(conds ...gen.Condition) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c corpusOperatorTaskMappingDo) Order(conds ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c corpusOperatorTaskMappingDo) Distinct(cols ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c corpusOperatorTaskMappingDo) Omit(cols ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c corpusOperatorTaskMappingDo) Join(table schema.Tabler, on ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c corpusOperatorTaskMappingDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c corpusOperatorTaskMappingDo) RightJoin(table schema.Tabler, on ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c corpusOperatorTaskMappingDo) Group(cols ...field.Expr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c corpusOperatorTaskMappingDo) Having(conds ...gen.Condition) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c corpusOperatorTaskMappingDo) Limit(limit int) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c corpusOperatorTaskMappingDo) Offset(offset int) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c corpusOperatorTaskMappingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c corpusOperatorTaskMappingDo) Unscoped() ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Unscoped())
}

func (c corpusOperatorTaskMappingDo) Create(values ...*models.CorpusOperatorTaskMapping) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c corpusOperatorTaskMappingDo) CreateInBatches(values []*models.CorpusOperatorTaskMapping, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c corpusOperatorTaskMappingDo) Save(values ...*models.CorpusOperatorTaskMapping) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c corpusOperatorTaskMappingDo) First() (*models.CorpusOperatorTaskMapping, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.CorpusOperatorTaskMapping), nil
	}
}

func (c corpusOperatorTaskMappingDo) Take() (*models.CorpusOperatorTaskMapping, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.CorpusOperatorTaskMapping), nil
	}
}

func (c corpusOperatorTaskMappingDo) Last() (*models.CorpusOperatorTaskMapping, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.CorpusOperatorTaskMapping), nil
	}
}

func (c corpusOperatorTaskMappingDo) Find() ([]*models.CorpusOperatorTaskMapping, error) {
	result, err := c.DO.Find()
	return result.([]*models.CorpusOperatorTaskMapping), err
}

func (c corpusOperatorTaskMappingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.CorpusOperatorTaskMapping, err error) {
	buf := make([]*models.CorpusOperatorTaskMapping, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c corpusOperatorTaskMappingDo) FindInBatches(result *[]*models.CorpusOperatorTaskMapping, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c corpusOperatorTaskMappingDo) Attrs(attrs ...field.AssignExpr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c corpusOperatorTaskMappingDo) Assign(attrs ...field.AssignExpr) ICorpusOperatorTaskMappingDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c corpusOperatorTaskMappingDo) Joins(fields ...field.RelationField) ICorpusOperatorTaskMappingDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c corpusOperatorTaskMappingDo) Preload(fields ...field.RelationField) ICorpusOperatorTaskMappingDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c corpusOperatorTaskMappingDo) FirstOrInit() (*models.CorpusOperatorTaskMapping, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.CorpusOperatorTaskMapping), nil
	}
}

func (c corpusOperatorTaskMappingDo) FirstOrCreate() (*models.CorpusOperatorTaskMapping, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.CorpusOperatorTaskMapping), nil
	}
}

func (c corpusOperatorTaskMappingDo) FindByPage(offset int, limit int) (result []*models.CorpusOperatorTaskMapping, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c corpusOperatorTaskMappingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c corpusOperatorTaskMappingDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c corpusOperatorTaskMappingDo) Delete(models ...*models.CorpusOperatorTaskMapping) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *corpusOperatorTaskMappingDo) withDO(do gen.Dao) *corpusOperatorTaskMappingDo {
	c.DO = *do.(*gen.DO)
	return c
}
