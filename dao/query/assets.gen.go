// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/aiot/cvat-backend/models"
)

func newAssets(db *gorm.DB, opts ...gen.DOOption) assets {
	_assets := assets{}

	_assets.assetsDo.UseDB(db, opts...)
	_assets.assetsDo.UseModel(&models.Assets{})

	tableName := _assets.assetsDo.TableName()
	_assets.ALL = field.NewAsterisk(tableName)
	_assets.ID = field.NewInt32(tableName, "id")

	_assets.fillFieldMap()

	return _assets
}

type assets struct {
	assetsDo

	ALL field.Asterisk
	ID  field.Int32

	fieldMap map[string]field.Expr
}

func (a assets) Table(newTableName string) *assets {
	a.assetsDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a assets) As(alias string) *assets {
	a.assetsDo.DO = *(a.assetsDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *assets) updateTableName(table string) *assets {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")

	a.fillFieldMap()

	return a
}

func (a *assets) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *assets) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 1)
	a.fieldMap["id"] = a.ID
}

func (a assets) clone(db *gorm.DB) assets {
	a.assetsDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a assets) replaceDB(db *gorm.DB) assets {
	a.assetsDo.ReplaceDB(db)
	return a
}

type assetsDo struct{ gen.DO }

type IAssetsDo interface {
	gen.SubQuery
	Debug() IAssetsDo
	WithContext(ctx context.Context) IAssetsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAssetsDo
	WriteDB() IAssetsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAssetsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAssetsDo
	Not(conds ...gen.Condition) IAssetsDo
	Or(conds ...gen.Condition) IAssetsDo
	Select(conds ...field.Expr) IAssetsDo
	Where(conds ...gen.Condition) IAssetsDo
	Order(conds ...field.Expr) IAssetsDo
	Distinct(cols ...field.Expr) IAssetsDo
	Omit(cols ...field.Expr) IAssetsDo
	Join(table schema.Tabler, on ...field.Expr) IAssetsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAssetsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAssetsDo
	Group(cols ...field.Expr) IAssetsDo
	Having(conds ...gen.Condition) IAssetsDo
	Limit(limit int) IAssetsDo
	Offset(offset int) IAssetsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAssetsDo
	Unscoped() IAssetsDo
	Create(values ...*models.Assets) error
	CreateInBatches(values []*models.Assets, batchSize int) error
	Save(values ...*models.Assets) error
	First() (*models.Assets, error)
	Take() (*models.Assets, error)
	Last() (*models.Assets, error)
	Find() ([]*models.Assets, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Assets, err error)
	FindInBatches(result *[]*models.Assets, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Assets) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAssetsDo
	Assign(attrs ...field.AssignExpr) IAssetsDo
	Joins(fields ...field.RelationField) IAssetsDo
	Preload(fields ...field.RelationField) IAssetsDo
	FirstOrInit() (*models.Assets, error)
	FirstOrCreate() (*models.Assets, error)
	FindByPage(offset int, limit int) (result []*models.Assets, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAssetsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a assetsDo) Debug() IAssetsDo {
	return a.withDO(a.DO.Debug())
}

func (a assetsDo) WithContext(ctx context.Context) IAssetsDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a assetsDo) ReadDB() IAssetsDo {
	return a.Clauses(dbresolver.Read)
}

func (a assetsDo) WriteDB() IAssetsDo {
	return a.Clauses(dbresolver.Write)
}

func (a assetsDo) Session(config *gorm.Session) IAssetsDo {
	return a.withDO(a.DO.Session(config))
}

func (a assetsDo) Clauses(conds ...clause.Expression) IAssetsDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a assetsDo) Returning(value interface{}, columns ...string) IAssetsDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a assetsDo) Not(conds ...gen.Condition) IAssetsDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a assetsDo) Or(conds ...gen.Condition) IAssetsDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a assetsDo) Select(conds ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a assetsDo) Where(conds ...gen.Condition) IAssetsDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a assetsDo) Order(conds ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a assetsDo) Distinct(cols ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a assetsDo) Omit(cols ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a assetsDo) Join(table schema.Tabler, on ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a assetsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a assetsDo) RightJoin(table schema.Tabler, on ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a assetsDo) Group(cols ...field.Expr) IAssetsDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a assetsDo) Having(conds ...gen.Condition) IAssetsDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a assetsDo) Limit(limit int) IAssetsDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a assetsDo) Offset(offset int) IAssetsDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a assetsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAssetsDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a assetsDo) Unscoped() IAssetsDo {
	return a.withDO(a.DO.Unscoped())
}

func (a assetsDo) Create(values ...*models.Assets) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a assetsDo) CreateInBatches(values []*models.Assets, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a assetsDo) Save(values ...*models.Assets) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a assetsDo) First() (*models.Assets, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Assets), nil
	}
}

func (a assetsDo) Take() (*models.Assets, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Assets), nil
	}
}

func (a assetsDo) Last() (*models.Assets, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Assets), nil
	}
}

func (a assetsDo) Find() ([]*models.Assets, error) {
	result, err := a.DO.Find()
	return result.([]*models.Assets), err
}

func (a assetsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Assets, err error) {
	buf := make([]*models.Assets, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a assetsDo) FindInBatches(result *[]*models.Assets, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a assetsDo) Attrs(attrs ...field.AssignExpr) IAssetsDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a assetsDo) Assign(attrs ...field.AssignExpr) IAssetsDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a assetsDo) Joins(fields ...field.RelationField) IAssetsDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a assetsDo) Preload(fields ...field.RelationField) IAssetsDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a assetsDo) FirstOrInit() (*models.Assets, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Assets), nil
	}
}

func (a assetsDo) FirstOrCreate() (*models.Assets, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Assets), nil
	}
}

func (a assetsDo) FindByPage(offset int, limit int) (result []*models.Assets, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a assetsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a assetsDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a assetsDo) Delete(models ...*models.Assets) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *assetsDo) withDO(do gen.Dao) *assetsDo {
	a.DO = *do.(*gen.DO)
	return a
}
