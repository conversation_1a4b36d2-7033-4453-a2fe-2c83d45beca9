// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/serving/dao/models"
)

func newMlopsServiceContainerInfo(db *gorm.DB, opts ...gen.DOOption) mlopsServiceContainerInfo {
	_mlopsServiceContainerInfo := mlopsServiceContainerInfo{}

	_mlopsServiceContainerInfo.mlopsServiceContainerInfoDo.UseDB(db, opts...)
	_mlopsServiceContainerInfo.mlopsServiceContainerInfoDo.UseModel(&models.MlopsServiceContainerInfo{})

	tableName := _mlopsServiceContainerInfo.mlopsServiceContainerInfoDo.TableName()
	_mlopsServiceContainerInfo.ALL = field.NewAsterisk(tableName)
	_mlopsServiceContainerInfo.ID = field.NewString(tableName, "id")
	_mlopsServiceContainerInfo.ProjectID = field.NewString(tableName, "project_id")
	_mlopsServiceContainerInfo.ServiceID = field.NewString(tableName, "service_id")
	_mlopsServiceContainerInfo.ServiceVersionID = field.NewString(tableName, "service_version_id")
	_mlopsServiceContainerInfo.ContainerType = field.NewInt32(tableName, "container_type")
	_mlopsServiceContainerInfo.Image = field.NewString(tableName, "image")
	_mlopsServiceContainerInfo.ImageType = field.NewInt32(tableName, "image_type")
	_mlopsServiceContainerInfo.ResourceLimit = field.NewString(tableName, "resource_limit")
	_mlopsServiceContainerInfo.Envs = field.NewString(tableName, "envs")
	_mlopsServiceContainerInfo.CmDs = field.NewString(tableName, "cm_ds")
	_mlopsServiceContainerInfo.MountPaths = field.NewString(tableName, "mount_paths")
	_mlopsServiceContainerInfo.ReadinessProbe = field.NewString(tableName, "readiness_probe")
	_mlopsServiceContainerInfo.LivenessProbe = field.NewString(tableName, "liveness_probe")
	_mlopsServiceContainerInfo.ResourceID = field.NewInt32(tableName, "resource_id")
	_mlopsServiceContainerInfo.CreateAt = field.NewTime(tableName, "create_at")
	_mlopsServiceContainerInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_mlopsServiceContainerInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_mlopsServiceContainerInfo.ResourceRule = field.NewString(tableName, "resource_rule")
	_mlopsServiceContainerInfo.ResourceGroups = field.NewString(tableName, "resource_groups")
	_mlopsServiceContainerInfo.GpuUtilizationPolicy = field.NewInt32(tableName, "gpu_utilization_policy")

	_mlopsServiceContainerInfo.fillFieldMap()

	return _mlopsServiceContainerInfo
}

type mlopsServiceContainerInfo struct {
	mlopsServiceContainerInfoDo

	ALL                  field.Asterisk
	ID                   field.String // ID
	ProjectID            field.String // 项目ID
	ServiceID            field.String // 服务ID
	ServiceVersionID     field.String // 服务本本ID
	ContainerType        field.Int32  // 容器类型，0主容器，1seldon容器
	Image                field.String // 镜像地址
	ImageType            field.Int32  // 镜像类型，0自定义镜像，1平台内镜像
	ResourceLimit        field.String // 资源限制，json格式
	Envs                 field.String // 环境变量，map格式
	CmDs                 field.String // 启动命令，数组格式
	MountPaths           field.String // 挂载配置，json array格式
	ReadinessProbe       field.String // 就绪探针，json格式
	LivenessProbe        field.String // 存活探针，json格式
	ResourceID           field.Int32  // 规格实例 id(为空则表示选择了高级配置)
	CreateAt             field.Time   // 创建时间
	UpdatedAt            field.Time   // 更新时间
	DeletedAt            field.Field  // 删除时间
	ResourceRule         field.String // 规格实例json，json array格式
	ResourceGroups       field.String // 资源组列表，数组格式
	GpuUtilizationPolicy field.Int32  // 算力限制策略，0智能限制，1强制限制，2不限制

	fieldMap map[string]field.Expr
}

func (m mlopsServiceContainerInfo) Table(newTableName string) *mlopsServiceContainerInfo {
	m.mlopsServiceContainerInfoDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mlopsServiceContainerInfo) As(alias string) *mlopsServiceContainerInfo {
	m.mlopsServiceContainerInfoDo.DO = *(m.mlopsServiceContainerInfoDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mlopsServiceContainerInfo) updateTableName(table string) *mlopsServiceContainerInfo {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.ProjectID = field.NewString(table, "project_id")
	m.ServiceID = field.NewString(table, "service_id")
	m.ServiceVersionID = field.NewString(table, "service_version_id")
	m.ContainerType = field.NewInt32(table, "container_type")
	m.Image = field.NewString(table, "image")
	m.ImageType = field.NewInt32(table, "image_type")
	m.ResourceLimit = field.NewString(table, "resource_limit")
	m.Envs = field.NewString(table, "envs")
	m.CmDs = field.NewString(table, "cm_ds")
	m.MountPaths = field.NewString(table, "mount_paths")
	m.ReadinessProbe = field.NewString(table, "readiness_probe")
	m.LivenessProbe = field.NewString(table, "liveness_probe")
	m.ResourceID = field.NewInt32(table, "resource_id")
	m.CreateAt = field.NewTime(table, "create_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")
	m.ResourceRule = field.NewString(table, "resource_rule")
	m.ResourceGroups = field.NewString(table, "resource_groups")
	m.GpuUtilizationPolicy = field.NewInt32(table, "gpu_utilization_policy")

	m.fillFieldMap()

	return m
}

func (m *mlopsServiceContainerInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mlopsServiceContainerInfo) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 20)
	m.fieldMap["id"] = m.ID
	m.fieldMap["project_id"] = m.ProjectID
	m.fieldMap["service_id"] = m.ServiceID
	m.fieldMap["service_version_id"] = m.ServiceVersionID
	m.fieldMap["container_type"] = m.ContainerType
	m.fieldMap["image"] = m.Image
	m.fieldMap["image_type"] = m.ImageType
	m.fieldMap["resource_limit"] = m.ResourceLimit
	m.fieldMap["envs"] = m.Envs
	m.fieldMap["cm_ds"] = m.CmDs
	m.fieldMap["mount_paths"] = m.MountPaths
	m.fieldMap["readiness_probe"] = m.ReadinessProbe
	m.fieldMap["liveness_probe"] = m.LivenessProbe
	m.fieldMap["resource_id"] = m.ResourceID
	m.fieldMap["create_at"] = m.CreateAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
	m.fieldMap["resource_rule"] = m.ResourceRule
	m.fieldMap["resource_groups"] = m.ResourceGroups
	m.fieldMap["gpu_utilization_policy"] = m.GpuUtilizationPolicy
}

func (m mlopsServiceContainerInfo) clone(db *gorm.DB) mlopsServiceContainerInfo {
	m.mlopsServiceContainerInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mlopsServiceContainerInfo) replaceDB(db *gorm.DB) mlopsServiceContainerInfo {
	m.mlopsServiceContainerInfoDo.ReplaceDB(db)
	return m
}

type mlopsServiceContainerInfoDo struct{ gen.DO }

type IMlopsServiceContainerInfoDo interface {
	gen.SubQuery
	Debug() IMlopsServiceContainerInfoDo
	WithContext(ctx context.Context) IMlopsServiceContainerInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMlopsServiceContainerInfoDo
	WriteDB() IMlopsServiceContainerInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMlopsServiceContainerInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMlopsServiceContainerInfoDo
	Not(conds ...gen.Condition) IMlopsServiceContainerInfoDo
	Or(conds ...gen.Condition) IMlopsServiceContainerInfoDo
	Select(conds ...field.Expr) IMlopsServiceContainerInfoDo
	Where(conds ...gen.Condition) IMlopsServiceContainerInfoDo
	Order(conds ...field.Expr) IMlopsServiceContainerInfoDo
	Distinct(cols ...field.Expr) IMlopsServiceContainerInfoDo
	Omit(cols ...field.Expr) IMlopsServiceContainerInfoDo
	Join(table schema.Tabler, on ...field.Expr) IMlopsServiceContainerInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceContainerInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceContainerInfoDo
	Group(cols ...field.Expr) IMlopsServiceContainerInfoDo
	Having(conds ...gen.Condition) IMlopsServiceContainerInfoDo
	Limit(limit int) IMlopsServiceContainerInfoDo
	Offset(offset int) IMlopsServiceContainerInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMlopsServiceContainerInfoDo
	Unscoped() IMlopsServiceContainerInfoDo
	Create(values ...*models.MlopsServiceContainerInfo) error
	CreateInBatches(values []*models.MlopsServiceContainerInfo, batchSize int) error
	Save(values ...*models.MlopsServiceContainerInfo) error
	First() (*models.MlopsServiceContainerInfo, error)
	Take() (*models.MlopsServiceContainerInfo, error)
	Last() (*models.MlopsServiceContainerInfo, error)
	Find() ([]*models.MlopsServiceContainerInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.MlopsServiceContainerInfo, err error)
	FindInBatches(result *[]*models.MlopsServiceContainerInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.MlopsServiceContainerInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMlopsServiceContainerInfoDo
	Assign(attrs ...field.AssignExpr) IMlopsServiceContainerInfoDo
	Joins(fields ...field.RelationField) IMlopsServiceContainerInfoDo
	Preload(fields ...field.RelationField) IMlopsServiceContainerInfoDo
	FirstOrInit() (*models.MlopsServiceContainerInfo, error)
	FirstOrCreate() (*models.MlopsServiceContainerInfo, error)
	FindByPage(offset int, limit int) (result []*models.MlopsServiceContainerInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMlopsServiceContainerInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mlopsServiceContainerInfoDo) Debug() IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Debug())
}

func (m mlopsServiceContainerInfoDo) WithContext(ctx context.Context) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mlopsServiceContainerInfoDo) ReadDB() IMlopsServiceContainerInfoDo {
	return m.Clauses(dbresolver.Read)
}

func (m mlopsServiceContainerInfoDo) WriteDB() IMlopsServiceContainerInfoDo {
	return m.Clauses(dbresolver.Write)
}

func (m mlopsServiceContainerInfoDo) Session(config *gorm.Session) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Session(config))
}

func (m mlopsServiceContainerInfoDo) Clauses(conds ...clause.Expression) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mlopsServiceContainerInfoDo) Returning(value interface{}, columns ...string) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mlopsServiceContainerInfoDo) Not(conds ...gen.Condition) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mlopsServiceContainerInfoDo) Or(conds ...gen.Condition) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mlopsServiceContainerInfoDo) Select(conds ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mlopsServiceContainerInfoDo) Where(conds ...gen.Condition) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mlopsServiceContainerInfoDo) Order(conds ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mlopsServiceContainerInfoDo) Distinct(cols ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mlopsServiceContainerInfoDo) Omit(cols ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mlopsServiceContainerInfoDo) Join(table schema.Tabler, on ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mlopsServiceContainerInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mlopsServiceContainerInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mlopsServiceContainerInfoDo) Group(cols ...field.Expr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mlopsServiceContainerInfoDo) Having(conds ...gen.Condition) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mlopsServiceContainerInfoDo) Limit(limit int) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mlopsServiceContainerInfoDo) Offset(offset int) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mlopsServiceContainerInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mlopsServiceContainerInfoDo) Unscoped() IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mlopsServiceContainerInfoDo) Create(values ...*models.MlopsServiceContainerInfo) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mlopsServiceContainerInfoDo) CreateInBatches(values []*models.MlopsServiceContainerInfo, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mlopsServiceContainerInfoDo) Save(values ...*models.MlopsServiceContainerInfo) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mlopsServiceContainerInfoDo) First() (*models.MlopsServiceContainerInfo, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceContainerInfo), nil
	}
}

func (m mlopsServiceContainerInfoDo) Take() (*models.MlopsServiceContainerInfo, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceContainerInfo), nil
	}
}

func (m mlopsServiceContainerInfoDo) Last() (*models.MlopsServiceContainerInfo, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceContainerInfo), nil
	}
}

func (m mlopsServiceContainerInfoDo) Find() ([]*models.MlopsServiceContainerInfo, error) {
	result, err := m.DO.Find()
	return result.([]*models.MlopsServiceContainerInfo), err
}

func (m mlopsServiceContainerInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.MlopsServiceContainerInfo, err error) {
	buf := make([]*models.MlopsServiceContainerInfo, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mlopsServiceContainerInfoDo) FindInBatches(result *[]*models.MlopsServiceContainerInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mlopsServiceContainerInfoDo) Attrs(attrs ...field.AssignExpr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mlopsServiceContainerInfoDo) Assign(attrs ...field.AssignExpr) IMlopsServiceContainerInfoDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mlopsServiceContainerInfoDo) Joins(fields ...field.RelationField) IMlopsServiceContainerInfoDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mlopsServiceContainerInfoDo) Preload(fields ...field.RelationField) IMlopsServiceContainerInfoDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mlopsServiceContainerInfoDo) FirstOrInit() (*models.MlopsServiceContainerInfo, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceContainerInfo), nil
	}
}

func (m mlopsServiceContainerInfoDo) FirstOrCreate() (*models.MlopsServiceContainerInfo, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceContainerInfo), nil
	}
}

func (m mlopsServiceContainerInfoDo) FindByPage(offset int, limit int) (result []*models.MlopsServiceContainerInfo, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mlopsServiceContainerInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mlopsServiceContainerInfoDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mlopsServiceContainerInfoDo) Delete(models ...*models.MlopsServiceContainerInfo) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mlopsServiceContainerInfoDo) withDO(do gen.Dao) *mlopsServiceContainerInfoDo {
	m.DO = *do.(*gen.DO)
	return m
}
