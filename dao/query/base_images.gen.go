// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/serving/dao/models"
)

func newBaseImage(db *gorm.DB, opts ...gen.DOOption) baseImage {
	_baseImage := baseImage{}

	_baseImage.baseImageDo.UseDB(db, opts...)
	_baseImage.baseImageDo.UseModel(&models.BaseImage{})

	tableName := _baseImage.baseImageDo.TableName()
	_baseImage.ALL = field.NewAsterisk(tableName)
	_baseImage.ID = field.NewString(tableName, "id")
	_baseImage.Image = field.NewString(tableName, "image")
	_baseImage.Creator = field.NewString(tableName, "creator")
	_baseImage.Name = field.NewString(tableName, "name")
	_baseImage.CreateTime = field.NewInt64(tableName, "create_time")
	_baseImage.UpdateTime = field.NewInt64(tableName, "update_time")

	_baseImage.fillFieldMap()

	return _baseImage
}

type baseImage struct {
	baseImageDo

	ALL        field.Asterisk
	ID         field.String
	Image      field.String
	Creator    field.String
	Name       field.String
	CreateTime field.Int64
	UpdateTime field.Int64

	fieldMap map[string]field.Expr
}

func (b baseImage) Table(newTableName string) *baseImage {
	b.baseImageDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b baseImage) As(alias string) *baseImage {
	b.baseImageDo.DO = *(b.baseImageDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *baseImage) updateTableName(table string) *baseImage {
	b.ALL = field.NewAsterisk(table)
	b.ID = field.NewString(table, "id")
	b.Image = field.NewString(table, "image")
	b.Creator = field.NewString(table, "creator")
	b.Name = field.NewString(table, "name")
	b.CreateTime = field.NewInt64(table, "create_time")
	b.UpdateTime = field.NewInt64(table, "update_time")

	b.fillFieldMap()

	return b
}

func (b *baseImage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *baseImage) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 6)
	b.fieldMap["id"] = b.ID
	b.fieldMap["image"] = b.Image
	b.fieldMap["creator"] = b.Creator
	b.fieldMap["name"] = b.Name
	b.fieldMap["create_time"] = b.CreateTime
	b.fieldMap["update_time"] = b.UpdateTime
}

func (b baseImage) clone(db *gorm.DB) baseImage {
	b.baseImageDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b baseImage) replaceDB(db *gorm.DB) baseImage {
	b.baseImageDo.ReplaceDB(db)
	return b
}

type baseImageDo struct{ gen.DO }

type IBaseImageDo interface {
	gen.SubQuery
	Debug() IBaseImageDo
	WithContext(ctx context.Context) IBaseImageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IBaseImageDo
	WriteDB() IBaseImageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IBaseImageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IBaseImageDo
	Not(conds ...gen.Condition) IBaseImageDo
	Or(conds ...gen.Condition) IBaseImageDo
	Select(conds ...field.Expr) IBaseImageDo
	Where(conds ...gen.Condition) IBaseImageDo
	Order(conds ...field.Expr) IBaseImageDo
	Distinct(cols ...field.Expr) IBaseImageDo
	Omit(cols ...field.Expr) IBaseImageDo
	Join(table schema.Tabler, on ...field.Expr) IBaseImageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IBaseImageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IBaseImageDo
	Group(cols ...field.Expr) IBaseImageDo
	Having(conds ...gen.Condition) IBaseImageDo
	Limit(limit int) IBaseImageDo
	Offset(offset int) IBaseImageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IBaseImageDo
	Unscoped() IBaseImageDo
	Create(values ...*models.BaseImage) error
	CreateInBatches(values []*models.BaseImage, batchSize int) error
	Save(values ...*models.BaseImage) error
	First() (*models.BaseImage, error)
	Take() (*models.BaseImage, error)
	Last() (*models.BaseImage, error)
	Find() ([]*models.BaseImage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.BaseImage, err error)
	FindInBatches(result *[]*models.BaseImage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.BaseImage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IBaseImageDo
	Assign(attrs ...field.AssignExpr) IBaseImageDo
	Joins(fields ...field.RelationField) IBaseImageDo
	Preload(fields ...field.RelationField) IBaseImageDo
	FirstOrInit() (*models.BaseImage, error)
	FirstOrCreate() (*models.BaseImage, error)
	FindByPage(offset int, limit int) (result []*models.BaseImage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IBaseImageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (b baseImageDo) Debug() IBaseImageDo {
	return b.withDO(b.DO.Debug())
}

func (b baseImageDo) WithContext(ctx context.Context) IBaseImageDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b baseImageDo) ReadDB() IBaseImageDo {
	return b.Clauses(dbresolver.Read)
}

func (b baseImageDo) WriteDB() IBaseImageDo {
	return b.Clauses(dbresolver.Write)
}

func (b baseImageDo) Session(config *gorm.Session) IBaseImageDo {
	return b.withDO(b.DO.Session(config))
}

func (b baseImageDo) Clauses(conds ...clause.Expression) IBaseImageDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b baseImageDo) Returning(value interface{}, columns ...string) IBaseImageDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b baseImageDo) Not(conds ...gen.Condition) IBaseImageDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b baseImageDo) Or(conds ...gen.Condition) IBaseImageDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b baseImageDo) Select(conds ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b baseImageDo) Where(conds ...gen.Condition) IBaseImageDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b baseImageDo) Order(conds ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b baseImageDo) Distinct(cols ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b baseImageDo) Omit(cols ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b baseImageDo) Join(table schema.Tabler, on ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b baseImageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b baseImageDo) RightJoin(table schema.Tabler, on ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b baseImageDo) Group(cols ...field.Expr) IBaseImageDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b baseImageDo) Having(conds ...gen.Condition) IBaseImageDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b baseImageDo) Limit(limit int) IBaseImageDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b baseImageDo) Offset(offset int) IBaseImageDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b baseImageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IBaseImageDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b baseImageDo) Unscoped() IBaseImageDo {
	return b.withDO(b.DO.Unscoped())
}

func (b baseImageDo) Create(values ...*models.BaseImage) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b baseImageDo) CreateInBatches(values []*models.BaseImage, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b baseImageDo) Save(values ...*models.BaseImage) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b baseImageDo) First() (*models.BaseImage, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.BaseImage), nil
	}
}

func (b baseImageDo) Take() (*models.BaseImage, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.BaseImage), nil
	}
}

func (b baseImageDo) Last() (*models.BaseImage, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.BaseImage), nil
	}
}

func (b baseImageDo) Find() ([]*models.BaseImage, error) {
	result, err := b.DO.Find()
	return result.([]*models.BaseImage), err
}

func (b baseImageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.BaseImage, err error) {
	buf := make([]*models.BaseImage, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b baseImageDo) FindInBatches(result *[]*models.BaseImage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b baseImageDo) Attrs(attrs ...field.AssignExpr) IBaseImageDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b baseImageDo) Assign(attrs ...field.AssignExpr) IBaseImageDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b baseImageDo) Joins(fields ...field.RelationField) IBaseImageDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b baseImageDo) Preload(fields ...field.RelationField) IBaseImageDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b baseImageDo) FirstOrInit() (*models.BaseImage, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.BaseImage), nil
	}
}

func (b baseImageDo) FirstOrCreate() (*models.BaseImage, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.BaseImage), nil
	}
}

func (b baseImageDo) FindByPage(offset int, limit int) (result []*models.BaseImage, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b baseImageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b baseImageDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b baseImageDo) Delete(models ...*models.BaseImage) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *baseImageDo) withDO(do gen.Dao) *baseImageDo {
	b.DO = *do.(*gen.DO)
	return b
}
