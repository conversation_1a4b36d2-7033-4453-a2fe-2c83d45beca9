// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/serving/dao/models"
)

func newMlopsServiceInfo(db *gorm.DB, opts ...gen.DOOption) mlopsServiceInfo {
	_mlopsServiceInfo := mlopsServiceInfo{}

	_mlopsServiceInfo.mlopsServiceInfoDo.UseDB(db, opts...)
	_mlopsServiceInfo.mlopsServiceInfoDo.UseModel(&models.MlopsServiceInfo{})

	tableName := _mlopsServiceInfo.mlopsServiceInfoDo.TableName()
	_mlopsServiceInfo.ALL = field.NewAsterisk(tableName)
	_mlopsServiceInfo.ID = field.NewString(tableName, "id")
	_mlopsServiceInfo.ProjectID = field.NewString(tableName, "project_id")
	_mlopsServiceInfo.Name = field.NewString(tableName, "name")
	_mlopsServiceInfo.Desc = field.NewString(tableName, "desc")
	_mlopsServiceInfo.SourceType = field.NewInt32(tableName, "source_type")
	_mlopsServiceInfo.SourceMeta = field.NewString(tableName, "source_meta")
	_mlopsServiceInfo.Creator = field.NewString(tableName, "creator")
	_mlopsServiceInfo.Cluster = field.NewString(tableName, "cluster")
	_mlopsServiceInfo.Async = field.NewInt32(tableName, "async")
	_mlopsServiceInfo.DeployStrategy = field.NewInt32(tableName, "deploy_strategy")
	_mlopsServiceInfo.DeployCfg = field.NewString(tableName, "deploy_cfg")
	_mlopsServiceInfo.Apis = field.NewString(tableName, "apis")
	_mlopsServiceInfo.VirtualSvcURL = field.NewString(tableName, "virtual_svc_url")
	_mlopsServiceInfo.Share = field.NewInt32(tableName, "share")
	_mlopsServiceInfo.CreateAt = field.NewTime(tableName, "create_at")
	_mlopsServiceInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_mlopsServiceInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_mlopsServiceInfo.Labels = field.NewString(tableName, "labels")
	_mlopsServiceInfo.ShareUsers = field.NewString(tableName, "share_users")
	_mlopsServiceInfo.ShareGroups = field.NewString(tableName, "share_groups")
	_mlopsServiceInfo.EnableSafetyCfg = field.NewInt32(tableName, "enable_safety_cfg")
	_mlopsServiceInfo.GuardrailsID = field.NewString(tableName, "guardrails_id")
	_mlopsServiceInfo.BillingType = field.NewString(tableName, "billing_type")
	_mlopsServiceInfo.PricePerThousandTokens = field.NewFloat32(tableName, "price_per_thousand_tokens")
	_mlopsServiceInfo.PricePerRequest = field.NewFloat32(tableName, "price_per_request")
	_mlopsServiceInfo.LimitTime = field.NewInt64(tableName, "limit_time")
	_mlopsServiceInfo.ApprovalStateInfo = field.NewString(tableName, "approval_state_info")
	_mlopsServiceInfo.IsRollUpdate = field.NewBool(tableName, "is_roll_update")
	_mlopsServiceInfo.TotalRateQPS = field.NewFloat64(tableName, "total_rate_qps")
	_mlopsServiceInfo.ToApprovalTime = field.NewInt64(tableName, "to_approval_time")
	_mlopsServiceInfo.IsTextGeneration = field.NewBool(tableName, "is_text_generation")
	_mlopsServiceInfo.Updater = field.NewString(tableName, "updater")
	_mlopsServiceInfo.Yaml = field.NewString(tableName, "yaml")
	_mlopsServiceInfo.EditModel = field.NewString(tableName, "edit_model")
	_mlopsServiceInfo.EnableAnonymousCall = field.NewBool(tableName, "enable_anonymous_call")
	_mlopsServiceInfo.OnlineTime = field.NewInt64(tableName, "online_time")
	_mlopsServiceInfo.Endpoints = field.NewField(tableName, "endpoints")
	_mlopsServiceInfo.PermissionCfg = field.NewString(tableName, "permission_cfg")

	_mlopsServiceInfo.fillFieldMap()

	return _mlopsServiceInfo
}

type mlopsServiceInfo struct {
	mlopsServiceInfoDo

	ALL                    field.Asterisk
	ID                     field.String
	ProjectID              field.String  // 项目ID
	Name                   field.String  // 服务名
	Desc                   field.String  // 服务描述
	SourceType             field.Int32   // 资源类型，0未知，1模型仓库，2应用仓库，3Vlab镜像，11自定义镜像
	SourceMeta             field.String  // 来源详情，json格式
	Creator                field.String  // 创建人
	Cluster                field.String  // 集群名
	Async                  field.Int32   // 是否开启异步调用，0未开启，1开启
	DeployStrategy         field.Int32   // 部署策略，0独立部署，1灰度发布，2冠军挑战
	DeployCfg              field.String  // 部署配置，json格式
	Apis                   field.String  // api列表，json array格式
	VirtualSvcURL          field.String  // virtual service url
	Share                  field.Int32   // 是够共享至资产市场，0不共享，1共享
	CreateAt               field.Time    // 创建时间
	UpdatedAt              field.Time    // 更新时间
	DeletedAt              field.Field   // 删除时间
	Labels                 field.String  // label map，json map格式
	ShareUsers             field.String  // share users，json string array格式
	ShareGroups            field.String  // share groups，json string array格式
	EnableSafetyCfg        field.Int32   // 是否开启安全围栏，0未开启，1开启
	GuardrailsID           field.String  // 安全围栏id
	BillingType            field.String  // 计费类型， BY_TOKEN or BY_REQUEST
	PricePerThousandTokens field.Float32 // 按token计费标准，单位元每千tokens
	PricePerRequest        field.Float32 // 按次计费标准，单位元每次
	LimitTime              field.Int64   // 服务最大运行时间
	ApprovalStateInfo      field.String  // 审批状态
	IsRollUpdate           field.Bool    // 是否滚动更新
	TotalRateQPS           field.Float64 // 所有服务版本下的限流qps
	ToApprovalTime         field.Int64   // 待审批的服务，对用户不可见
	IsTextGeneration       field.Bool    // 是否是文本类生成模型
	Updater                field.String  // 更新者
	Yaml                   field.String  // yaml配置
	EditModel              field.String  // 编辑模式，可选common/yaml
	EnableAnonymousCall    field.Bool    // 是否滚动更新
	OnlineTime             field.Int64   // 服务上线时间
	Endpoints              field.Field   // endpoint是apis的替换，具体结构见llmops-common
	PermissionCfg          field.String  // 服务权限配置

	fieldMap map[string]field.Expr
}

func (m mlopsServiceInfo) Table(newTableName string) *mlopsServiceInfo {
	m.mlopsServiceInfoDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mlopsServiceInfo) As(alias string) *mlopsServiceInfo {
	m.mlopsServiceInfoDo.DO = *(m.mlopsServiceInfoDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mlopsServiceInfo) updateTableName(table string) *mlopsServiceInfo {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.ProjectID = field.NewString(table, "project_id")
	m.Name = field.NewString(table, "name")
	m.Desc = field.NewString(table, "desc")
	m.SourceType = field.NewInt32(table, "source_type")
	m.SourceMeta = field.NewString(table, "source_meta")
	m.Creator = field.NewString(table, "creator")
	m.Cluster = field.NewString(table, "cluster")
	m.Async = field.NewInt32(table, "async")
	m.DeployStrategy = field.NewInt32(table, "deploy_strategy")
	m.DeployCfg = field.NewString(table, "deploy_cfg")
	m.Apis = field.NewString(table, "apis")
	m.VirtualSvcURL = field.NewString(table, "virtual_svc_url")
	m.Share = field.NewInt32(table, "share")
	m.CreateAt = field.NewTime(table, "create_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")
	m.Labels = field.NewString(table, "labels")
	m.ShareUsers = field.NewString(table, "share_users")
	m.ShareGroups = field.NewString(table, "share_groups")
	m.EnableSafetyCfg = field.NewInt32(table, "enable_safety_cfg")
	m.GuardrailsID = field.NewString(table, "guardrails_id")
	m.BillingType = field.NewString(table, "billing_type")
	m.PricePerThousandTokens = field.NewFloat32(table, "price_per_thousand_tokens")
	m.PricePerRequest = field.NewFloat32(table, "price_per_request")
	m.LimitTime = field.NewInt64(table, "limit_time")
	m.ApprovalStateInfo = field.NewString(table, "approval_state_info")
	m.IsRollUpdate = field.NewBool(table, "is_roll_update")
	m.TotalRateQPS = field.NewFloat64(table, "total_rate_qps")
	m.ToApprovalTime = field.NewInt64(table, "to_approval_time")
	m.IsTextGeneration = field.NewBool(table, "is_text_generation")
	m.Updater = field.NewString(table, "updater")
	m.Yaml = field.NewString(table, "yaml")
	m.EditModel = field.NewString(table, "edit_model")
	m.EnableAnonymousCall = field.NewBool(table, "enable_anonymous_call")
	m.OnlineTime = field.NewInt64(table, "online_time")
	m.Endpoints = field.NewField(table, "endpoints")
	m.PermissionCfg = field.NewString(table, "permission_cfg")

	m.fillFieldMap()

	return m
}

func (m *mlopsServiceInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mlopsServiceInfo) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 38)
	m.fieldMap["id"] = m.ID
	m.fieldMap["project_id"] = m.ProjectID
	m.fieldMap["name"] = m.Name
	m.fieldMap["desc"] = m.Desc
	m.fieldMap["source_type"] = m.SourceType
	m.fieldMap["source_meta"] = m.SourceMeta
	m.fieldMap["creator"] = m.Creator
	m.fieldMap["cluster"] = m.Cluster
	m.fieldMap["async"] = m.Async
	m.fieldMap["deploy_strategy"] = m.DeployStrategy
	m.fieldMap["deploy_cfg"] = m.DeployCfg
	m.fieldMap["apis"] = m.Apis
	m.fieldMap["virtual_svc_url"] = m.VirtualSvcURL
	m.fieldMap["share"] = m.Share
	m.fieldMap["create_at"] = m.CreateAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
	m.fieldMap["labels"] = m.Labels
	m.fieldMap["share_users"] = m.ShareUsers
	m.fieldMap["share_groups"] = m.ShareGroups
	m.fieldMap["enable_safety_cfg"] = m.EnableSafetyCfg
	m.fieldMap["guardrails_id"] = m.GuardrailsID
	m.fieldMap["billing_type"] = m.BillingType
	m.fieldMap["price_per_thousand_tokens"] = m.PricePerThousandTokens
	m.fieldMap["price_per_request"] = m.PricePerRequest
	m.fieldMap["limit_time"] = m.LimitTime
	m.fieldMap["approval_state_info"] = m.ApprovalStateInfo
	m.fieldMap["is_roll_update"] = m.IsRollUpdate
	m.fieldMap["total_rate_qps"] = m.TotalRateQPS
	m.fieldMap["to_approval_time"] = m.ToApprovalTime
	m.fieldMap["is_text_generation"] = m.IsTextGeneration
	m.fieldMap["updater"] = m.Updater
	m.fieldMap["yaml"] = m.Yaml
	m.fieldMap["edit_model"] = m.EditModel
	m.fieldMap["enable_anonymous_call"] = m.EnableAnonymousCall
	m.fieldMap["online_time"] = m.OnlineTime
	m.fieldMap["endpoints"] = m.Endpoints
	m.fieldMap["permission_cfg"] = m.PermissionCfg
}

func (m mlopsServiceInfo) clone(db *gorm.DB) mlopsServiceInfo {
	m.mlopsServiceInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mlopsServiceInfo) replaceDB(db *gorm.DB) mlopsServiceInfo {
	m.mlopsServiceInfoDo.ReplaceDB(db)
	return m
}

type mlopsServiceInfoDo struct{ gen.DO }

type IMlopsServiceInfoDo interface {
	gen.SubQuery
	Debug() IMlopsServiceInfoDo
	WithContext(ctx context.Context) IMlopsServiceInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMlopsServiceInfoDo
	WriteDB() IMlopsServiceInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMlopsServiceInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMlopsServiceInfoDo
	Not(conds ...gen.Condition) IMlopsServiceInfoDo
	Or(conds ...gen.Condition) IMlopsServiceInfoDo
	Select(conds ...field.Expr) IMlopsServiceInfoDo
	Where(conds ...gen.Condition) IMlopsServiceInfoDo
	Order(conds ...field.Expr) IMlopsServiceInfoDo
	Distinct(cols ...field.Expr) IMlopsServiceInfoDo
	Omit(cols ...field.Expr) IMlopsServiceInfoDo
	Join(table schema.Tabler, on ...field.Expr) IMlopsServiceInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceInfoDo
	Group(cols ...field.Expr) IMlopsServiceInfoDo
	Having(conds ...gen.Condition) IMlopsServiceInfoDo
	Limit(limit int) IMlopsServiceInfoDo
	Offset(offset int) IMlopsServiceInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMlopsServiceInfoDo
	Unscoped() IMlopsServiceInfoDo
	Create(values ...*models.MlopsServiceInfo) error
	CreateInBatches(values []*models.MlopsServiceInfo, batchSize int) error
	Save(values ...*models.MlopsServiceInfo) error
	First() (*models.MlopsServiceInfo, error)
	Take() (*models.MlopsServiceInfo, error)
	Last() (*models.MlopsServiceInfo, error)
	Find() ([]*models.MlopsServiceInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.MlopsServiceInfo, err error)
	FindInBatches(result *[]*models.MlopsServiceInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.MlopsServiceInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMlopsServiceInfoDo
	Assign(attrs ...field.AssignExpr) IMlopsServiceInfoDo
	Joins(fields ...field.RelationField) IMlopsServiceInfoDo
	Preload(fields ...field.RelationField) IMlopsServiceInfoDo
	FirstOrInit() (*models.MlopsServiceInfo, error)
	FirstOrCreate() (*models.MlopsServiceInfo, error)
	FindByPage(offset int, limit int) (result []*models.MlopsServiceInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMlopsServiceInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mlopsServiceInfoDo) Debug() IMlopsServiceInfoDo {
	return m.withDO(m.DO.Debug())
}

func (m mlopsServiceInfoDo) WithContext(ctx context.Context) IMlopsServiceInfoDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mlopsServiceInfoDo) ReadDB() IMlopsServiceInfoDo {
	return m.Clauses(dbresolver.Read)
}

func (m mlopsServiceInfoDo) WriteDB() IMlopsServiceInfoDo {
	return m.Clauses(dbresolver.Write)
}

func (m mlopsServiceInfoDo) Session(config *gorm.Session) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Session(config))
}

func (m mlopsServiceInfoDo) Clauses(conds ...clause.Expression) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mlopsServiceInfoDo) Returning(value interface{}, columns ...string) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mlopsServiceInfoDo) Not(conds ...gen.Condition) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mlopsServiceInfoDo) Or(conds ...gen.Condition) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mlopsServiceInfoDo) Select(conds ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mlopsServiceInfoDo) Where(conds ...gen.Condition) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mlopsServiceInfoDo) Order(conds ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mlopsServiceInfoDo) Distinct(cols ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mlopsServiceInfoDo) Omit(cols ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mlopsServiceInfoDo) Join(table schema.Tabler, on ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mlopsServiceInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mlopsServiceInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mlopsServiceInfoDo) Group(cols ...field.Expr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mlopsServiceInfoDo) Having(conds ...gen.Condition) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mlopsServiceInfoDo) Limit(limit int) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mlopsServiceInfoDo) Offset(offset int) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mlopsServiceInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mlopsServiceInfoDo) Unscoped() IMlopsServiceInfoDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mlopsServiceInfoDo) Create(values ...*models.MlopsServiceInfo) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mlopsServiceInfoDo) CreateInBatches(values []*models.MlopsServiceInfo, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mlopsServiceInfoDo) Save(values ...*models.MlopsServiceInfo) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mlopsServiceInfoDo) First() (*models.MlopsServiceInfo, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceInfo), nil
	}
}

func (m mlopsServiceInfoDo) Take() (*models.MlopsServiceInfo, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceInfo), nil
	}
}

func (m mlopsServiceInfoDo) Last() (*models.MlopsServiceInfo, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceInfo), nil
	}
}

func (m mlopsServiceInfoDo) Find() ([]*models.MlopsServiceInfo, error) {
	result, err := m.DO.Find()
	return result.([]*models.MlopsServiceInfo), err
}

func (m mlopsServiceInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.MlopsServiceInfo, err error) {
	buf := make([]*models.MlopsServiceInfo, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mlopsServiceInfoDo) FindInBatches(result *[]*models.MlopsServiceInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mlopsServiceInfoDo) Attrs(attrs ...field.AssignExpr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mlopsServiceInfoDo) Assign(attrs ...field.AssignExpr) IMlopsServiceInfoDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mlopsServiceInfoDo) Joins(fields ...field.RelationField) IMlopsServiceInfoDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mlopsServiceInfoDo) Preload(fields ...field.RelationField) IMlopsServiceInfoDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mlopsServiceInfoDo) FirstOrInit() (*models.MlopsServiceInfo, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceInfo), nil
	}
}

func (m mlopsServiceInfoDo) FirstOrCreate() (*models.MlopsServiceInfo, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.MlopsServiceInfo), nil
	}
}

func (m mlopsServiceInfoDo) FindByPage(offset int, limit int) (result []*models.MlopsServiceInfo, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mlopsServiceInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mlopsServiceInfoDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mlopsServiceInfoDo) Delete(models ...*models.MlopsServiceInfo) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mlopsServiceInfoDo) withDO(do gen.Dao) *mlopsServiceInfoDo {
	m.DO = *do.(*gen.DO)
	return m
}
