// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

func newLlmBasicConfig(db *gorm.DB, opts ...gen.DOOption) llmBasicConfig {
	_llmBasicConfig := llmBasicConfig{}

	_llmBasicConfig.llmBasicConfigDo.UseDB(db, opts...)
	_llmBasicConfig.llmBasicConfigDo.UseModel(&generated.LlmBasicConfig{})

	tableName := _llmBasicConfig.llmBasicConfigDo.TableName()
	_llmBasicConfig.ALL = field.NewAsterisk(tableName)
	_llmBasicConfig.ID = field.NewString(tableName, "id")
	_llmBasicConfig.ProjectID = field.NewString(tableName, "project_id")
	_llmBasicConfig.LlmModelSvc = field.NewString(tableName, "llm_model_svc")
	_llmBasicConfig.EmbeddingSvc = field.NewString(tableName, "embedding_svc")
	_llmBasicConfig.ImageGenSvc = field.NewString(tableName, "image_gen_svc")
	_llmBasicConfig.ReRankSvc = field.NewString(tableName, "re_rank_svc")
	_llmBasicConfig.ImageUnderstandSvc = field.NewString(tableName, "image_understand_svc")
	_llmBasicConfig.OcrSvc = field.NewString(tableName, "ocr_svc")

	_llmBasicConfig.fillFieldMap()

	return _llmBasicConfig
}

type llmBasicConfig struct {
	llmBasicConfigDo

	ALL                field.Asterisk
	ID                 field.String
	ProjectID          field.String
	LlmModelSvc        field.String
	EmbeddingSvc       field.String
	ImageGenSvc        field.String
	ReRankSvc          field.String
	ImageUnderstandSvc field.String
	OcrSvc             field.String

	fieldMap map[string]field.Expr
}

func (l llmBasicConfig) Table(newTableName string) *llmBasicConfig {
	l.llmBasicConfigDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l llmBasicConfig) As(alias string) *llmBasicConfig {
	l.llmBasicConfigDo.DO = *(l.llmBasicConfigDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *llmBasicConfig) updateTableName(table string) *llmBasicConfig {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewString(table, "id")
	l.ProjectID = field.NewString(table, "project_id")
	l.LlmModelSvc = field.NewString(table, "llm_model_svc")
	l.EmbeddingSvc = field.NewString(table, "embedding_svc")
	l.ImageGenSvc = field.NewString(table, "image_gen_svc")
	l.ReRankSvc = field.NewString(table, "re_rank_svc")
	l.ImageUnderstandSvc = field.NewString(table, "image_understand_svc")
	l.OcrSvc = field.NewString(table, "ocr_svc")

	l.fillFieldMap()

	return l
}

func (l *llmBasicConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *llmBasicConfig) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 8)
	l.fieldMap["id"] = l.ID
	l.fieldMap["project_id"] = l.ProjectID
	l.fieldMap["llm_model_svc"] = l.LlmModelSvc
	l.fieldMap["embedding_svc"] = l.EmbeddingSvc
	l.fieldMap["image_gen_svc"] = l.ImageGenSvc
	l.fieldMap["re_rank_svc"] = l.ReRankSvc
	l.fieldMap["image_understand_svc"] = l.ImageUnderstandSvc
	l.fieldMap["ocr_svc"] = l.OcrSvc
}

func (l llmBasicConfig) clone(db *gorm.DB) llmBasicConfig {
	l.llmBasicConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l llmBasicConfig) replaceDB(db *gorm.DB) llmBasicConfig {
	l.llmBasicConfigDo.ReplaceDB(db)
	return l
}

type llmBasicConfigDo struct{ gen.DO }

type ILlmBasicConfigDo interface {
	gen.SubQuery
	Debug() ILlmBasicConfigDo
	WithContext(ctx context.Context) ILlmBasicConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ILlmBasicConfigDo
	WriteDB() ILlmBasicConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ILlmBasicConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ILlmBasicConfigDo
	Not(conds ...gen.Condition) ILlmBasicConfigDo
	Or(conds ...gen.Condition) ILlmBasicConfigDo
	Select(conds ...field.Expr) ILlmBasicConfigDo
	Where(conds ...gen.Condition) ILlmBasicConfigDo
	Order(conds ...field.Expr) ILlmBasicConfigDo
	Distinct(cols ...field.Expr) ILlmBasicConfigDo
	Omit(cols ...field.Expr) ILlmBasicConfigDo
	Join(table schema.Tabler, on ...field.Expr) ILlmBasicConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ILlmBasicConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) ILlmBasicConfigDo
	Group(cols ...field.Expr) ILlmBasicConfigDo
	Having(conds ...gen.Condition) ILlmBasicConfigDo
	Limit(limit int) ILlmBasicConfigDo
	Offset(offset int) ILlmBasicConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ILlmBasicConfigDo
	Unscoped() ILlmBasicConfigDo
	Create(values ...*generated.LlmBasicConfig) error
	CreateInBatches(values []*generated.LlmBasicConfig, batchSize int) error
	Save(values ...*generated.LlmBasicConfig) error
	First() (*generated.LlmBasicConfig, error)
	Take() (*generated.LlmBasicConfig, error)
	Last() (*generated.LlmBasicConfig, error)
	Find() ([]*generated.LlmBasicConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.LlmBasicConfig, err error)
	FindInBatches(result *[]*generated.LlmBasicConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*generated.LlmBasicConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ILlmBasicConfigDo
	Assign(attrs ...field.AssignExpr) ILlmBasicConfigDo
	Joins(fields ...field.RelationField) ILlmBasicConfigDo
	Preload(fields ...field.RelationField) ILlmBasicConfigDo
	FirstOrInit() (*generated.LlmBasicConfig, error)
	FirstOrCreate() (*generated.LlmBasicConfig, error)
	FindByPage(offset int, limit int) (result []*generated.LlmBasicConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ILlmBasicConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (l llmBasicConfigDo) Debug() ILlmBasicConfigDo {
	return l.withDO(l.DO.Debug())
}

func (l llmBasicConfigDo) WithContext(ctx context.Context) ILlmBasicConfigDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l llmBasicConfigDo) ReadDB() ILlmBasicConfigDo {
	return l.Clauses(dbresolver.Read)
}

func (l llmBasicConfigDo) WriteDB() ILlmBasicConfigDo {
	return l.Clauses(dbresolver.Write)
}

func (l llmBasicConfigDo) Session(config *gorm.Session) ILlmBasicConfigDo {
	return l.withDO(l.DO.Session(config))
}

func (l llmBasicConfigDo) Clauses(conds ...clause.Expression) ILlmBasicConfigDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l llmBasicConfigDo) Returning(value interface{}, columns ...string) ILlmBasicConfigDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l llmBasicConfigDo) Not(conds ...gen.Condition) ILlmBasicConfigDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l llmBasicConfigDo) Or(conds ...gen.Condition) ILlmBasicConfigDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l llmBasicConfigDo) Select(conds ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l llmBasicConfigDo) Where(conds ...gen.Condition) ILlmBasicConfigDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l llmBasicConfigDo) Order(conds ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l llmBasicConfigDo) Distinct(cols ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l llmBasicConfigDo) Omit(cols ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l llmBasicConfigDo) Join(table schema.Tabler, on ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l llmBasicConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l llmBasicConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l llmBasicConfigDo) Group(cols ...field.Expr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l llmBasicConfigDo) Having(conds ...gen.Condition) ILlmBasicConfigDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l llmBasicConfigDo) Limit(limit int) ILlmBasicConfigDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l llmBasicConfigDo) Offset(offset int) ILlmBasicConfigDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l llmBasicConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ILlmBasicConfigDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l llmBasicConfigDo) Unscoped() ILlmBasicConfigDo {
	return l.withDO(l.DO.Unscoped())
}

func (l llmBasicConfigDo) Create(values ...*generated.LlmBasicConfig) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l llmBasicConfigDo) CreateInBatches(values []*generated.LlmBasicConfig, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l llmBasicConfigDo) Save(values ...*generated.LlmBasicConfig) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l llmBasicConfigDo) First() (*generated.LlmBasicConfig, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*generated.LlmBasicConfig), nil
	}
}

func (l llmBasicConfigDo) Take() (*generated.LlmBasicConfig, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*generated.LlmBasicConfig), nil
	}
}

func (l llmBasicConfigDo) Last() (*generated.LlmBasicConfig, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*generated.LlmBasicConfig), nil
	}
}

func (l llmBasicConfigDo) Find() ([]*generated.LlmBasicConfig, error) {
	result, err := l.DO.Find()
	return result.([]*generated.LlmBasicConfig), err
}

func (l llmBasicConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*generated.LlmBasicConfig, err error) {
	buf := make([]*generated.LlmBasicConfig, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l llmBasicConfigDo) FindInBatches(result *[]*generated.LlmBasicConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l llmBasicConfigDo) Attrs(attrs ...field.AssignExpr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l llmBasicConfigDo) Assign(attrs ...field.AssignExpr) ILlmBasicConfigDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l llmBasicConfigDo) Joins(fields ...field.RelationField) ILlmBasicConfigDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l llmBasicConfigDo) Preload(fields ...field.RelationField) ILlmBasicConfigDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l llmBasicConfigDo) FirstOrInit() (*generated.LlmBasicConfig, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*generated.LlmBasicConfig), nil
	}
}

func (l llmBasicConfigDo) FirstOrCreate() (*generated.LlmBasicConfig, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*generated.LlmBasicConfig), nil
	}
}

func (l llmBasicConfigDo) FindByPage(offset int, limit int) (result []*generated.LlmBasicConfig, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l llmBasicConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l llmBasicConfigDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l llmBasicConfigDo) Delete(models ...*generated.LlmBasicConfig) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *llmBasicConfigDo) withDO(do gen.Dao) *llmBasicConfigDo {
	l.DO = *do.(*gen.DO)
	return l
}
