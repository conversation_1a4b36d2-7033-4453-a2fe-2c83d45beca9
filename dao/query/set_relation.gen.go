// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/applied-ai/aiot/cvat-backend/models"
)

func newTaskRelation(db *gorm.DB, opts ...gen.DOOption) taskRelation {
	_taskRelation := taskRelation{}

	_taskRelation.taskRelationDo.UseDB(db, opts...)
	_taskRelation.taskRelationDo.UseModel(&models.TaskRelation{})

	tableName := _taskRelation.taskRelationDo.TableName()
	_taskRelation.ALL = field.NewAsterisk(tableName)
	_taskRelation.SampleImageDatasetID = field.NewInt32(tableName, "sample_image_dataset_id")
	_taskRelation.AnnotationTaskID = field.NewInt32(tableName, "annotation_task_id")

	_taskRelation.fillFieldMap()

	return _taskRelation
}

type taskRelation struct {
	taskRelationDo

	ALL                  field.Asterisk
	SampleImageDatasetID field.Int32
	AnnotationTaskID     field.Int32

	fieldMap map[string]field.Expr
}

func (t taskRelation) Table(newTableName string) *taskRelation {
	t.taskRelationDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t taskRelation) As(alias string) *taskRelation {
	t.taskRelationDo.DO = *(t.taskRelationDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *taskRelation) updateTableName(table string) *taskRelation {
	t.ALL = field.NewAsterisk(table)
	t.SampleImageDatasetID = field.NewInt32(table, "sample_image_dataset_id")
	t.AnnotationTaskID = field.NewInt32(table, "annotation_task_id")

	t.fillFieldMap()

	return t
}

func (t *taskRelation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *taskRelation) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 2)
	t.fieldMap["sample_image_dataset_id"] = t.SampleImageDatasetID
	t.fieldMap["annotation_task_id"] = t.AnnotationTaskID
}

func (t taskRelation) clone(db *gorm.DB) taskRelation {
	t.taskRelationDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t taskRelation) replaceDB(db *gorm.DB) taskRelation {
	t.taskRelationDo.ReplaceDB(db)
	return t
}

type taskRelationDo struct{ gen.DO }

type ITaskRelationDo interface {
	gen.SubQuery
	Debug() ITaskRelationDo
	WithContext(ctx context.Context) ITaskRelationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITaskRelationDo
	WriteDB() ITaskRelationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITaskRelationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITaskRelationDo
	Not(conds ...gen.Condition) ITaskRelationDo
	Or(conds ...gen.Condition) ITaskRelationDo
	Select(conds ...field.Expr) ITaskRelationDo
	Where(conds ...gen.Condition) ITaskRelationDo
	Order(conds ...field.Expr) ITaskRelationDo
	Distinct(cols ...field.Expr) ITaskRelationDo
	Omit(cols ...field.Expr) ITaskRelationDo
	Join(table schema.Tabler, on ...field.Expr) ITaskRelationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITaskRelationDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITaskRelationDo
	Group(cols ...field.Expr) ITaskRelationDo
	Having(conds ...gen.Condition) ITaskRelationDo
	Limit(limit int) ITaskRelationDo
	Offset(offset int) ITaskRelationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITaskRelationDo
	Unscoped() ITaskRelationDo
	Create(values ...*models.TaskRelation) error
	CreateInBatches(values []*models.TaskRelation, batchSize int) error
	Save(values ...*models.TaskRelation) error
	First() (*models.TaskRelation, error)
	Take() (*models.TaskRelation, error)
	Last() (*models.TaskRelation, error)
	Find() ([]*models.TaskRelation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.TaskRelation, err error)
	FindInBatches(result *[]*models.TaskRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.TaskRelation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITaskRelationDo
	Assign(attrs ...field.AssignExpr) ITaskRelationDo
	Joins(fields ...field.RelationField) ITaskRelationDo
	Preload(fields ...field.RelationField) ITaskRelationDo
	FirstOrInit() (*models.TaskRelation, error)
	FirstOrCreate() (*models.TaskRelation, error)
	FindByPage(offset int, limit int) (result []*models.TaskRelation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITaskRelationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t taskRelationDo) Debug() ITaskRelationDo {
	return t.withDO(t.DO.Debug())
}

func (t taskRelationDo) WithContext(ctx context.Context) ITaskRelationDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t taskRelationDo) ReadDB() ITaskRelationDo {
	return t.Clauses(dbresolver.Read)
}

func (t taskRelationDo) WriteDB() ITaskRelationDo {
	return t.Clauses(dbresolver.Write)
}

func (t taskRelationDo) Session(config *gorm.Session) ITaskRelationDo {
	return t.withDO(t.DO.Session(config))
}

func (t taskRelationDo) Clauses(conds ...clause.Expression) ITaskRelationDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t taskRelationDo) Returning(value interface{}, columns ...string) ITaskRelationDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t taskRelationDo) Not(conds ...gen.Condition) ITaskRelationDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t taskRelationDo) Or(conds ...gen.Condition) ITaskRelationDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t taskRelationDo) Select(conds ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t taskRelationDo) Where(conds ...gen.Condition) ITaskRelationDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t taskRelationDo) Order(conds ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t taskRelationDo) Distinct(cols ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t taskRelationDo) Omit(cols ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t taskRelationDo) Join(table schema.Tabler, on ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t taskRelationDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t taskRelationDo) RightJoin(table schema.Tabler, on ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t taskRelationDo) Group(cols ...field.Expr) ITaskRelationDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t taskRelationDo) Having(conds ...gen.Condition) ITaskRelationDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t taskRelationDo) Limit(limit int) ITaskRelationDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t taskRelationDo) Offset(offset int) ITaskRelationDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t taskRelationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITaskRelationDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t taskRelationDo) Unscoped() ITaskRelationDo {
	return t.withDO(t.DO.Unscoped())
}

func (t taskRelationDo) Create(values ...*models.TaskRelation) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t taskRelationDo) CreateInBatches(values []*models.TaskRelation, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t taskRelationDo) Save(values ...*models.TaskRelation) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t taskRelationDo) First() (*models.TaskRelation, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.TaskRelation), nil
	}
}

func (t taskRelationDo) Take() (*models.TaskRelation, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.TaskRelation), nil
	}
}

func (t taskRelationDo) Last() (*models.TaskRelation, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.TaskRelation), nil
	}
}

func (t taskRelationDo) Find() ([]*models.TaskRelation, error) {
	result, err := t.DO.Find()
	return result.([]*models.TaskRelation), err
}

func (t taskRelationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.TaskRelation, err error) {
	buf := make([]*models.TaskRelation, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t taskRelationDo) FindInBatches(result *[]*models.TaskRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t taskRelationDo) Attrs(attrs ...field.AssignExpr) ITaskRelationDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t taskRelationDo) Assign(attrs ...field.AssignExpr) ITaskRelationDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t taskRelationDo) Joins(fields ...field.RelationField) ITaskRelationDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t taskRelationDo) Preload(fields ...field.RelationField) ITaskRelationDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t taskRelationDo) FirstOrInit() (*models.TaskRelation, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.TaskRelation), nil
	}
}

func (t taskRelationDo) FirstOrCreate() (*models.TaskRelation, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.TaskRelation), nil
	}
}

func (t taskRelationDo) FindByPage(offset int, limit int) (result []*models.TaskRelation, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t taskRelationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t taskRelationDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t taskRelationDo) Delete(models ...*models.TaskRelation) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *taskRelationDo) withDO(do gen.Dao) *taskRelationDo {
	t.DO = *do.(*gen.DO)
	return t
}
