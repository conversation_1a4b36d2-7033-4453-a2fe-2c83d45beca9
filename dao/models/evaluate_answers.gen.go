// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameEvaluateAnswer = "evaluate_answers"

// EvaluateAnswer mapped from table <evaluate_answers>
type EvaluateAnswer struct {
	ID         string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                                   // ID
	RequestID  string    `gorm:"column:request_id;not null;comment:XRequestId" json:"request_id"`                             // XRequestId
	Question   string    `gorm:"column:question;not null;comment:Question" json:"question"`                                   // Question
	Answer     string    `gorm:"column:answer;not null;comment:Answer" json:"answer"`                                         // Answer
	Rating     int32     `gorm:"column:rating;not null;comment:1为点赞，-1为点踩" json:"rating"`                                     // 1为点赞，-1为点踩
	CreateTime time.Time `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                                 // 创建时间
	CreateUser string    `gorm:"column:create_user;not null;comment:createUser" json:"create_user"`                           // createUser
	SourceType int32     `gorm:"column:source_type;not null;comment:资源类型，0未知，1模型仓库，2应用仓库，3Vlab镜像，11自定义镜像" json:"source_type"` // 资源类型，0未知，1模型仓库，2应用仓库，3Vlab镜像，11自定义镜像
	SourceID   string    `gorm:"column:source_id;not null;comment:来源ID" json:"source_id"`                                     // 来源ID
}

// TableName EvaluateAnswer's table name
func (*EvaluateAnswer) TableName() string {
	return TableNameEvaluateAnswer
}
