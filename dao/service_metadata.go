package dao

const (
	ServiceModelTypeModel      = 0
	ServiceModelTypeApplyChain = 1
)

type VisitLog struct {
	XRequestId             string  `json:"xRequestId"`
	StartTime              int64   `json:"startTime"`
	Method                 string  `json:"method"`
	Path                   string  `json:"path"`
	ResponseCode           string  `json:"responseCode"`
	BytesReceived          int64   `json:"bytesReceived"`
	Duration               int64   `json:"duration"`
	XForwardedFor          string  `json:"xForwardedFor"`
	UserAgent              string  `json:"userAgent"`
	RequestHost            string  `json:"requestHost"`
	RequestMime            string  `json:"requestMime"`
	UpstreamHost           string  `json:"upstreamHost"`
	Server                 string  `json:"server"`
	RequestBody            string  `json:"requestBody"`
	ResponseBody           string  `json:"responseBody"`
	ProjectId              string  `json:"projectId"`
	Username               string  `json:"userName"`
	UserProjectId          string  `json:"user_project_id"`
	ApiKey                 string  `json:"apikey"`
	ServiceId              string  `json:"serviceId"`
	ServiceName            string  `json:"serviceName"`
	ServiceVersion         string  `json:"version"`
	RefEntityId            string  `json:"refEntityId"`
	RefType                string  `json:"refType"`
	PricePerThousandTokens float32 `json:"pricePerThousandTokens"`
	PricePerRequest        float32 `json:"pricePerRequest"`
	PromptTokens           int     `json:"prompt_tokens"`
	CompletionTokens       int     `json:"completion_tokens"`
	TotalTokens            int     `json:"total_tokens"`
	FirstTokenTime         int64   `json:"first_token_time"`
	PodName                string  `json:"pod_name"`
	Namespace              string  `json:"namespace"`
	CntName                string  `json:"cnt_name"`
	FinalPod               string  `json:"final_pod"`
}

type ModelPredictOutputV2 struct {
	Parameters ModelPredictModelV2Parameter `json:"parameters"`
	Outputs    []ModelPredictOutputs        `json:"outputs"`
}

type ModelPredictModelV2Parameter struct {
	FeatureNames string            `json:"feature_names"`
	RequestPath  map[string]string `json:"RequestPath"`
	Tags         map[string]string `json:"tags"`
}
type ModelPredictOutputs struct {
	Name       string            `json:"name"`
	Data       []interface{}     `json:"data"`
	DataType   string            `json:"dataType"`
	Shape      []int             `json:"shape"`
	Parameters map[string]string `json:"parameters"`
}

const CreateViewSqlOncluster = "\nCREATE MATERIALIZED VIEW visit_logs_view ON CLUSTER sophon_cluster " +
	"\n\tENGINE = ReplicatedAggregatingMergeTree('/clickhouse/views/%s/visit_logs_view','{replica}')" +
	"\n\tPARTITION by toYYYYMM(toDateTime(start_time))" +
	"\n\tORDER BY (start_time)" +
	"\nAS SELECT x_request_id," +
	"\n\tstart_time," +
	"\n\tservice_id," +
	"\n\t`method`," +
	"\n\t`path`," +
	"\n\tresponse_code," +
	"\n\tbytes_received," +
	"\n\tduration," +
	"\n\tx_forwarded_for," +
	"\n\tuser_agent," +
	"\n\trequest_host," +
	"\n\trequest_mime," +
	"\n\tupstream_host," +
	"\n\tserver," +
	"\n\trequest_body," +
	"\n\tgroupArrayState(response_body) as response_body," +
	"\n\tservice_version" +
	"\n\tFROM %s.visit_logs GROUP BY x_request_id," +
	"\n\tstart_time," +
	"\n\tservice_id," +
	"\n\t`method`," +
	"\n\t`path`," +
	"\n\tresponse_code," +
	"\n\tbytes_received," +
	"\n\tduration," +
	"\n\tx_forwarded_for," +
	"\n\tuser_agent," +
	"\n\trequest_host," +
	"\n\trequest_mime," +
	"\n\tupstream_host," +
	"\n\tserver," +
	"\n\trequest_body," +
	"\n\tproject_id," +
	"\n\tservice_version"

const CreateViewSql = "\nCREATE MATERIALIZED VIEW visit_logs_view " +
	"\n\tENGINE = AggregatingMergeTree" +
	"\n\tPARTITION BY toYYYYMM(toDateTime(start_time))" +
	"\n\tORDER BY (start_time)" +
	"\nAS SELECT x_request_id," +
	"\n\tstart_time," +
	"\n\tservice_id," +
	"\n\t`method`," +
	"\n\t`path`," +
	"\n\tresponse_code," +
	"\n\tbytes_received," +
	"\n\tduration," +
	"\n\tx_forwarded_for," +
	"\n\tuser_agent," +
	"\n\trequest_host," +
	"\n\trequest_mime," +
	"\n\tupstream_host," +
	"\n\tserver," +
	"\n\trequest_body," +
	"\n\tgroupArrayState(response_body) as response_body," +
	"\n\tservice_version" +
	"\n\tFROM %s.visit_logs GROUP BY x_request_id," +
	"\n\tstart_time," +
	"\n\tservice_id," +
	"\n\t`method`," +
	"\n\t`path`," +
	"\n\tresponse_code," +
	"\n\tbytes_received," +
	"\n\tduration," +
	"\n\tx_forwarded_for," +
	"\n\tuser_agent," +
	"\n\trequest_host," +
	"\n\trequest_mime," +
	"\n\tupstream_host," +
	"\n\tserver," +
	"\n\trequest_body," +
	"\n\tproject_id," +
	"\n\tservice_version"
