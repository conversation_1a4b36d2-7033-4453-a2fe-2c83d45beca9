package main

import (
	"fmt"

	"gorm.io/driver/clickhouse"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"transwarp.io/mlops/serving/dao"
)

/**
本脚本用于从数据库生成模型结构
https://gorm.io/zh_CN/gen/database_to_structs.html
*/

var mysqldb *gorm.DB
var ckickhouseDb *gorm.DB

func init() {
	var err error
	mysqldb, err = gorm.Open(mysql.Open("root:Warp!CV@2022#@(172.17.120.207:31907)/metastore_serving_llmops?charset=utf8mb4&parseTime=True&loc=Local"))
	if err != nil {
		panic(err)
	}
	//err = mysqldb.AutoMigrate(dao.MlopsServiceInfo{})
	//if err != nil {
	//	panic(err)
	//}
	//err = mysqldb.AutoMigrate(dao.DefaultConfig{})
	//if err != nil {
	//	panic(err)
	//}
	ckickhouseDb, err = gorm.Open(clickhouse.Open("tcp://172.17.120.203:32129?database=metastore_serving_llmops&username=sophonuser&password=password"))
	if err != nil {
		panic(err)
	}
	//
	err = ckickhouseDb.Set("gorm:table_options", fmt.Sprintf("engine=%s primary key (id) order by (id, %s)  "+
		"partition by %s SETTINGS index_granularity = 8192",
		"MergeTree", "create_time", "toYYYYMM(toDateTime(create_time))")).
		AutoMigrate(dao.EvaluateAnswer{})
	if err != nil {
		//logger.Error("can not create table on cluster, cause:", err.Error())
		return
	}
}

func main() {
	g := gen.NewGenerator(gen.Config{
		ModelPkgPath: "dao/models",
		OutPath:      "dao/query",
		Mode:         gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})
	g.UseDB(mysqldb)
	deletedFieldType := gen.FieldType("deleted_at", "soft_delete.DeletedAt")
	autoUpdateDisabledTag := gen.FieldGORMTag("updated_at", func(tag field.GormTag) field.GormTag {
		// 禁用gorm的更新时间，使用数据库的[ON UPDATE CURRENT_TIMESTAMP]方法
		return tag.Set("autoUpdateTime", "false")
	})
	g.ApplyBasic(
		g.GenerateModel("mlops_service_infos",
			gen.FieldType("async", "*int32"),
			gen.FieldType("deploy_strategy", "*int32"),
			gen.FieldType("share", "*int32"),
			gen.FieldType("enable_safety_cfg", "*int32"),
			gen.FieldType("limit_time", "*int64"),
			gen.FieldType("limit_time_db", "*int64"),
			gen.FieldType("is_roll_update", "*bool"),
			gen.FieldType("to_approval_time", "*int64"),
			gen.FieldType("enable_anonymous_call", "*bool"),
			gen.FieldType("endpoints", "datatypes.JSONType[*Endpoints]"),
			gen.FieldType("online_time", "*int64"),
			deletedFieldType, autoUpdateDisabledTag),
		g.GenerateModel("mlops_service_version_infos",
			gen.FieldType("unlimited_gpu", "*int32"),
			gen.FieldType("enable_gpu", "*int32"),
			gen.FieldType("host_network", "*int32"),
			gen.FieldType("is_power_rule", "*bool"),
			deletedFieldType, autoUpdateDisabledTag),
		g.GenerateModel("mlops_service_container_infos",
			gen.FieldType("container_type", "*int32"),
			gen.FieldType("image_type", "*int32"),
			gen.FieldType("resource_id", "*int32"),
			deletedFieldType, autoUpdateDisabledTag),
		g.GenerateModel("default_configs"),
	)
	g.Execute()

	g.UseDB(ckickhouseDb)
	g.ApplyBasic(
		g.GenerateModel("evaluate_answers"),
		g.GenerateModel("visit_logs"),
	)
	g.Execute()

}

type TokenLog struct {
	Id             string `json:"id"`
	XRequestId     string `json:"X-Request-ID"`
	Timestamp      int64  `json:"timestamp"`
	InputTokenNum  int64  `json:"input_token_num"`
	OutputTokenNum int64  `json:"output_token_num"`
}
