package dao

import (
	"fmt"
	"gorm.io/gorm"
)

type Token<PERSON>og<PERSON><PERSON> struct {
	*CommonDAO
}

func NewTokenLogDAO(dao *CommonDAO) *TokenLogDAO {
	dao.entity = &TokenLogDAO{}
	return &TokenLogDAO{CommonDAO: dao}
}

type VisitTokenLog struct {
	Time                int64
	TotalInputTokenNum  int64
	TotalOutputTokenNum int64
}

func (dao *TokenLogDAO) CountTokenLogsGroupByStep(serviceId string, start int64, end int64, step int64) (*[]VisitTokenLog, error) {
	visitTokenLog := &[]VisitTokenLog{}
	err := dao.db.Session(&gorm.Session{}).Raw(fmt.Sprintf("SELECT "+
		"((start_time - %d) DIV %d) * %d + %d AS time, "+
		"SUM(prompt_tokens) AS total_input_token_num, "+
		"SUM(completion_tokens) AS total_output_token_num "+
		"FROM (SELECT * FROM visit_logs "+
		"WHERE service_id = ? "+
		"AND start_time > ? "+
		"AND start_time < ? ) "+
		"GROUP BY time "+
		"ORDER BY time;", start, step, step, start), serviceId, start, end).Scan(visitTokenLog).Error
	if err != nil {
		return nil, err
	}
	return visitTokenLog, nil
}

type FirstTokenTimeRecord struct {
	Time              int64
	ServiceVersion    string
	FirstTokenTimeAvg int64
}

func (dao *TokenLogDAO) AvgGroupByStep(serviceId string, start int64, end int64, step int64) (*[]FirstTokenTimeRecord, error) {
	records := &[]FirstTokenTimeRecord{}
	err := dao.db.Session(&gorm.Session{}).Raw(fmt.Sprintf("SELECT "+
		"((start_time - %d) DIV %d) * %d + %d AS time, "+
		"service_version, "+
		"CAST (COALESCE(AVG(CASE WHEN first_token_time != 0 THEN first_token_time END), 0) AS INT) AS first_token_time_avg "+
		"FROM (SELECT * FROM visit_logs "+
		"WHERE service_id = ? "+
		"AND start_time > ? "+
		"AND start_time < ? ) "+
		"GROUP BY service_version, time "+
		"ORDER BY service_version, time;", start, step, step, start), serviceId, start, end).Scan(records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (dao *TokenLogDAO) AvgGroupByStepInProject(projectId string, start int64, end int64, step int64) (*[]FirstTokenTimeRecord, error) {
	records := &[]FirstTokenTimeRecord{}
	var err error
	err = dao.db.Session(&gorm.Session{}).Raw(fmt.Sprintf("SELECT "+
		"((start_time - %d) DIV %d) * %d + %d AS time, "+
		"CAST (COALESCE(AVG(CASE WHEN first_token_time != 0 THEN first_token_time END), 0) AS INT) AS first_token_time_avg "+
		"FROM (SELECT * FROM visit_logs "+
		"WHERE project_id = ? "+
		"AND start_time > ? "+
		"AND start_time < ? ) "+
		"GROUP BY time "+
		"ORDER BY time;", start, step, step, start), projectId, start, end).Scan(records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

type DurationRecord struct {
	Time        int64
	DurationAvg float64
}

func (dao *TokenLogDAO) AvgDurationGroupByStep(serviceIds []string, start int64, end int64, step int64) (*[]DurationRecord, error) {
	records := &[]DurationRecord{}
	err := dao.db.Session(&gorm.Session{}).Raw(fmt.Sprintf("SELECT "+
		"((start_time - %d) DIV %d) * %d + %d AS time, "+
		"COALESCE(AVG(CASE WHEN duration != 0 THEN duration END), 0) AS duration_avg "+
		"FROM (SELECT * FROM visit_logs "+
		"WHERE service_id in (?) "+
		"AND start_time > ? "+
		"AND start_time < ? ) "+
		"GROUP BY time "+
		"ORDER BY time;", start, step, step, start), serviceIds, start, end).Scan(records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

type CountRecord struct {
	Time  int64
	Count int64
}

func (dao *TokenLogDAO) CountGroupByStep(serviceIds []string, start int64, end int64, step int64) (*[]CountRecord, error) {
	records := &[]CountRecord{}
	err := dao.db.Session(&gorm.Session{}).Raw(fmt.Sprintf("SELECT "+
		"((start_time - %d) DIV %d) * %d + %d AS time, "+
		"count(*) AS count "+
		"FROM (SELECT * FROM visit_logs "+
		"WHERE service_id in (?) "+
		"AND start_time > ? "+
		"AND start_time < ? ) "+
		"GROUP BY time "+
		"ORDER BY time;", start, step, step, start), serviceIds, start, end).Scan(records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}
