package dao

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"k8s.io/klog"
	"os"
	"strconv"
	"transwarp.io/mlops/mlops-std/conf"
	"transwarp.io/mlops/mlops-std/database"
)

const (
	DefaultWriteTimeout string = "20"
	DefaultReadTimeout  string = "10"
	CLUSTERNAME                = "ON CLUSTER sophon_cluster"
	VisitTableName             = "visit_logs"
	TokenLogName               = "token_logs"
)

var Table string
var CkDb *gorm.DB

func InitClickhouse() error {
	var driver string
	var dbName string

	if v := os.Getenv("DATASOURCE_DRIVER"); v != "" {
		driver = v
	} else {
		klog.Error("you must set driver of clickhouse!")
		return errors.New("ENV parma is miss")
	}

	var host string
	if v := os.Getenv("DATASOURCE_HOST"); v != "" {
		host = v
	} else {
		klog.Error("you must set host of clickhouse!")
		return errors.New("ENV parma is miss")
	}

	var port string
	if v := os.Getenv("DATASOURCE_PORT"); v != "" {
		port = v
	} else {
		klog.Error("you must set port of clickhouse!")
		return errors.New("ENV parma is miss")
	}

	var user string
	if v := os.Getenv("DATASOURCE_USER"); v != "" {
		user = v
	} else {
		klog.Error("you must set user of clickhouse!")
		return errors.New("ENV parma is miss")
	}

	var password string
	password = os.Getenv("DATASOURCE_PASSWORD")

	if v := os.Getenv("DATASOURCE_DATABASE"); v != "" {
		dbName = v
	} else {
		klog.Error("you must set database of clickhouse!")
		return errors.New("ENV parma is miss")
	}

	var writeTimeout string
	if v := os.Getenv("DATASOURCE_WRITE_TIMEOUT"); v != "" {
		writeTimeout = v
	} else {
		klog.Infof("you set the default write_timeout: %s", DefaultWriteTimeout)
		writeTimeout = DefaultWriteTimeout
	}

	var readTimeout string
	if v := os.Getenv("DATASOURCE_READ_TIMEOUT"); v != "" {
		readTimeout = v
	} else {
		klog.Infof("you set the default read_timeout: %s", DefaultReadTimeout)
		readTimeout = DefaultReadTimeout
	}

	onCluster := true
	if v := os.Getenv("DATASOURCE_ON_CLUSTER"); v != "" {
		var err error
		onCluster, err = strconv.ParseBool(v)
		if err != nil {
			klog.Errorf("can not convert parameter DATASOURCE_ON_CLUSTER to bool : %s", err)
		}
	} else {
		klog.Info("create tables(visit_logs) on local")
	}

	if v := os.Getenv("DATASOURCE_TABLE"); v != "" {
		Table = v
	} else {
		klog.Error("you must set Table of clickhouse!")
		return errors.New("ENV parma is miss")
	}

	config := conf.Datasource{}
	config.Driver = driver
	config.Host = host
	config.Port = port
	config.DBName = dbName
	config.Username = user
	config.Password = password
	config.WriteTimeout, _ = strconv.Atoi(writeTimeout)
	config.ReadTimeout, _ = strconv.Atoi(readTimeout)
	config.OnCluster = onCluster
	klog.Infof("host:%s, port:%s, user:%s", host, port, user)
	CkDb = database.CreateDB(config)
	if config.Driver == database.ClickHouse {
		if onCluster {
			klog.Info("create tables on cluster")
			CkDb.Exec(fmt.Sprintf("create database %s on cluster sophon_cluster", config.DBName))

			CkDb.Set("gorm:table_cluster_options", CLUSTERNAME).Set("gorm:table_options", fmt.Sprintf("engine=ReplicatedReplacingMergeTree('/clickhouse/tables/%s/%s','{replica}') primary key (start_time) order by start_time  partition by toYYYYMM(toDateTime(start_time)) SETTINGS index_granularity = 8192", config.DBName, VisitTableName)).AutoMigrate(&VisitLog{})

		} else {
			klog.Info("create tables on local")
			CkDb.Exec(fmt.Sprintf("create database %s", config.DBName))

			CkDb.Set("gorm:table_options", "engine=MergeTree primary key (start_time) order by start_time  partition by toYYYYMM(toDateTime(start_time)) SETTINGS index_granularity = 8192").AutoMigrate(&VisitLog{})

		}

	} else {
		err := CkDb.AutoMigrate(&VisitLog{})
		if err != nil {
			return err
		}
	}

	var ttl string
	if v := os.Getenv("CH_TTL_MONTH"); v != "" {
		ttl = v
	}

	if len(ttl) != 0 {

	}

	return nil
}
