# gocv/opencv:4.7.0
FROM --platform=${BUILDPLATFORM} ***********/aip/base/golang-builder:1.23-ubuntu24.04 AS build

ARG PACKAGE=transwarp.io/applied-ai/aiot/csm-backend
ARG BUILD_VERSION=0.0.0
ARG BUILD_TIME=99999999
ARG CI_COMMIT_SHA=00000000
ARG ARCH=arm64

WORKDIR /build

COPY csm-backend csm-backend
COPY vision-std vision-std
COPY llmops-common llmops-common

ENV GOPROXY=http://*************:1111,https://goproxy.cn/,https://goproxy.io/,https://mirrors.aliyun.com/goproxy,direct
ENV GO111MODULE=on
ENV GOSUMDB=off
ENV GOOS=linux
ENV GOARCH=${ARCH}

RUN (export PATH=$PATH:/go/bin && cd csm-backend && \
    go mod tidy && \
    go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
    -o csm-backend ./main.go)


# gocv/opencv:4.7.0-ubuntu-18.04
#FROM ***********/aip/base/gocv-opencv:4.7.0-ubuntu-18.04
# FROM gocv/opencv:4.7.0-ubuntu-20.04
FROM ***********/aip/base/ubuntu-runner:24.04

WORKDIR /csm

COPY --from=build /build/vision-std/license/bin/verifier /usr/local/bin/verifier
COPY --from=build /build/csm-backend/public public
COPY --from=build /build/csm-backend/etc etc
COPY --from=build /build/csm-backend/bin bin
COPY --from=build /build/csm-backend/csm-backend csm-backend
COPY --from=build /build/csm-backend/bin/boot.sh /bin/boot.sh

RUN sed -i "s/archive.ubuntu.com/mirrors.aliyun.com/g" /etc/apt/sources.list && \
    sed -i "s/security.ubuntu.com/mirrors.aliyun.com/g" /etc/apt/sources.list

RUN (apt-get update && apt-get install -y zip && apt-get install -y python3)

RUN (apt-get update && apt-get install -y libgpgme-dev libassuan-dev libbtrfs-dev libdevmapper-dev skopeo)
RUN chmod +x /bin/boot.sh

EXPOSE 80

# 暂时用./csm-backend
# /bin/boot.sh会报错[bad interpreter: No such file or directory]， 可能是包含了win特有的换行符，需要dos2unix命令来转换
CMD ["./csm-backend"]
