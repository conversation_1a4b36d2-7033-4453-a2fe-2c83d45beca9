options(warn=-1)
library(SparkR)
# SophonR to get dataSet by id
sophonR.getDataByID <- function(spark, pid, dataId) {
  data <- SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "getDataFrameByID", spark, pid ,dataId)
  SparkR:::dataFrame(data)
}

# SophonR to get usr's dataSet by name
sophonR.getUsrDataByName <- function(spark, pid, dataName) {
  data <- SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "getUserDataFrameByName", spark, pid ,dataName)
  SparkR:::dataFrame(data)
}

# SophonR to get sample dataSet by name
sophonR.getSampleDataByName <- function(spark, dataName) {
  data <- SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "getSampleDataFrameByName", spark, dataName)
  SparkR:::dataFrame(data)
}

# sophonR to get hdfs file InputStream
SophonR.getHdfsFileInputStream <- function(spark, projectId, datasetId){
  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "getHdfsFileInputStream", spark, projectId, datasetId)
}

# SophonR to read next line according to hdfs fileSystem InputStream
SophonR.hdfsReadLine <- function(fsInputStream){
  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "hdfsReadLine", fsInputStream)
}

# sophonR close hdfs inputStream
SophonR.closeHdfsInputStream <- function(fsInputStream) {
  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "closeHdfsInputStream", fsInputStream)
}

#SophonR.writeToInceptor <- function(dataFrame, url, usrName, password, tableName, isOverwrite, tableType = "text", isRemote = FALSE, namenodes = "", webHdfsPort = 50070) {
#  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "writeToInceptor", dataFrame@sdf, spark, url, usrName, password, tableName, isOverwrite, tableType, isRemote, namenodes, webHdfsPort)
#}

#SophonR.writeToInceptor3 <- function(dataFrame, url, usrName, password, tableName, isOverwrite, tableType, isRemote, namenodes, webHdfsPort) {
#  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "writeToInceptor", dataFrame@sdf, spark, url, usrName, password, tableName, isOverwrite, tableType, isRemote, namenodes, webHdfsPort)
#}

#SophonR.writeToInceptor4 <- function(dataFrame, url, usrName, password, tableName, isOverwrite) {
#  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "writeToInceptor", dataFrame@sdf, spark, url, usrName, password, tableName, isOverwrite, "text", FALSE, "", 50070)
#}

SophonR.writeToInceptorV2 <- function(dataFrame, url, usrName, password, tableName, isOverwrite, tableType, isRemote, namenodes, webHdfsPort) {
  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "writeToInceptor", dataFrame@sdf, spark, url, usrName, password, tableName, isOverwrite, tableType, isRemote, namenodes, webHdfsPort)
}

SophonR.writeToInceptor <- function(dataFrame, url, usrName, password, tableName, isOverwrite) {
  SparkR:::callJStatic("io.transwarp.sophon.engine.SophonNotebook", "writeToInceptor", dataFrame@sdf, spark, url, usrName, password, tableName, isOverwrite)
}
