import uuid

from py4j.java_gateway import java_import
from pyspark.ml.common import _java2py
from pyspark.sql import DataFrame


class EntryPoint(object):
    # {model name in java -> model package in python}
    pysophon_models = {'DiscoverPCAModel': 'DiscoverPCA.DiscoverPCAModel',
                       'DiscoverSVDModel': 'DiscoverSVD.DiscoverSVDModel',
                       'ItemCFModel': 'ItemCF.ItemCFModel',
                       'KNNClassificationModel': 'KNNClassifier.KNNClassificationModel',
                       'LDAModel': 'LinearDiscriminantAnalysis.LinearDiscriminantAnalysisModel',
                       'ScalerModel': 'Scaler.ScalerModel',
                       'UserCFModel': 'UserCF.UserCFModel',
                       'XGBoostClassificationModel': 'XGBoostClassifier.XGBoostClassificationModel',
                       'XGBoostRegressionModel': 'XGBoostRegressor.XGBoostRegressionModel'}

    def importJar(self):
        java_import(global_dict['sc']._jvm, "io.transwarp.sophon.engine.SophonNotebook")
        java_import(global_dict['sc']._jvm, "io.transwarp.sophon.model.NativeSparkType")

    def get_df_by_id(self, spark, pid, dataId):
        """
        Sophon python to get dataset by id

        Parameters
        ----------
            spark : pyspark.sql.session.SparkSession
                pyspark session
            pid : string
                project id in sophon
            dataId : string
                dataset id loaded in sophon

        Returns
        ----------
            out : pyspark.sql.dataframe.DataFrame
                spark dataframe spark from certian dataId

        Examples
        ----------
        >>> input_iris = entry.get_df_by_id(spark, "54844200-f843-4257-ac2f-267a94424db4", "sample_iris")
        >>> input_iris.show()
        +---+---+---+---+-----+-----------+
        | a1| a2| a3| a4|   id|      label|
        +---+---+---+---+-----+-----------+
        |5.1|3.5|1.4|0.2| id_1|Iris-setosa|
        |4.9|3.0|1.4|0.2| id_2|Iris-setosa|
        |4.7|3.2|1.3|0.2| id_3|Iris-setosa|
        |4.6|3.1|1.5|0.2| id_4|Iris-setosa|
        |5.0|3.6|1.4|0.2| id_5|Iris-setosa|
        |5.4|3.9|1.7|0.4| id_6|Iris-setosa|
        |4.6|3.4|1.4|0.3| id_7|Iris-setosa|
        |5.0|3.4|1.5|0.2| id_8|Iris-setosa|
        |4.4|2.9|1.4|0.2| id_9|Iris-setosa|
        |4.9|3.1|1.5|0.1|id_10|Iris-setosa|
        |5.4|3.7|1.5|0.2|id_11|Iris-setosa|
        |4.8|3.4|1.6|0.2|id_12|Iris-setosa|
        |4.8|3.0|1.4|0.1|id_13|Iris-setosa|
        |4.3|3.0|1.1|0.1|id_14|Iris-setosa|
        |5.8|4.0|1.2|0.2|id_15|Iris-setosa|
        |5.7|4.4|1.5|0.4|id_16|Iris-setosa|
        |5.4|3.9|1.3|0.4|id_17|Iris-setosa|
        |5.1|3.5|1.4|0.3|id_18|Iris-setosa|
        |5.7|3.8|1.7|0.3|id_19|Iris-setosa|
        |5.1|3.8|1.5|0.3|id_20|Iris-setosa|
        +---+---+---+---+-----+-----------+
        only showing top 20 rows

        """
        self.importJar()
        _df = global_dict['sc']._jvm.SophonNotebook.getDataFrameByID(spark._jsparkSession, pid, dataId)
        return DataFrame(_df, global_dict['sqlContext'])

    def get_usr_df_by_name(self, spark, pid, dataName):
        """
        Sophon python to get usr's dataset by name

        Parameters
        ----------
            spark : pyspark.sql.session.SparkSession
                pyspark session
            pid : string
                project id in sophon
            dataName : string
                dataset name stored in sophon

        Returns
        ----------
            out : pyspark.sql.dataframe.DataFrame
                spark dataframe spark from certian dataset name

        Examples
        ----------
        >>> input_iris = entry.get_usr_df_by_name(spark, "54844200-f843-4257-ac2f-267a94424db4", "iris")
        >>> input_iris.show()
        +---+---+---+---+-----+-----------+
        | a1| a2| a3| a4|   id|      label|
        +---+---+---+---+-----+-----------+
        |5.1|3.5|1.4|0.2| id_1|Iris-setosa|
        |4.9|3.0|1.4|0.2| id_2|Iris-setosa|
        |4.7|3.2|1.3|0.2| id_3|Iris-setosa|
        |4.6|3.1|1.5|0.2| id_4|Iris-setosa|
        |5.0|3.6|1.4|0.2| id_5|Iris-setosa|
        |5.4|3.9|1.7|0.4| id_6|Iris-setosa|
        |4.6|3.4|1.4|0.3| id_7|Iris-setosa|
        |5.0|3.4|1.5|0.2| id_8|Iris-setosa|
        |4.4|2.9|1.4|0.2| id_9|Iris-setosa|
        |4.9|3.1|1.5|0.1|id_10|Iris-setosa|
        |5.4|3.7|1.5|0.2|id_11|Iris-setosa|
        |4.8|3.4|1.6|0.2|id_12|Iris-setosa|
        |4.8|3.0|1.4|0.1|id_13|Iris-setosa|
        |4.3|3.0|1.1|0.1|id_14|Iris-setosa|
        |5.8|4.0|1.2|0.2|id_15|Iris-setosa|
        |5.7|4.4|1.5|0.4|id_16|Iris-setosa|
        |5.4|3.9|1.3|0.4|id_17|Iris-setosa|
        |5.1|3.5|1.4|0.3|id_18|Iris-setosa|
        |5.7|3.8|1.7|0.3|id_19|Iris-setosa|
        |5.1|3.8|1.5|0.3|id_20|Iris-setosa|
        +---+---+---+---+-----+-----------+
        only showing top 20 rows

        """
        self.importJar()
        _df = global_dict['sc']._jvm.SophonNotebook.getUserDataFrameByName(spark._jsparkSession, pid, dataName)
        return DataFrame(_df, global_dict['sqlContext'])

    def get_sample_df_by_name(self, spark, dataName):
        """
        Sophon python to get sample dataset by name

        Parameters
        ----------
            spark : pyspark.sql.session.SparkSession
                pyspark session
            dataName : string
                sample dataset name stored in sophon

        Returns
        ----------
            out : pyspark.sql.dataframe.DataFrame
                spark dataframe spark from certian sample dataset name

        Examples
        ----------
        >>> input_iris = entry.get_sample_df_by_name(spark, "iris")
        >>> input_iris.show()
        +---+---+---+---+-----+-----------+
        | a1| a2| a3| a4|   id|      label|
        +---+---+---+---+-----+-----------+
        |5.1|3.5|1.4|0.2| id_1|Iris-setosa|
        |4.9|3.0|1.4|0.2| id_2|Iris-setosa|
        |4.7|3.2|1.3|0.2| id_3|Iris-setosa|
        |4.6|3.1|1.5|0.2| id_4|Iris-setosa|
        |5.0|3.6|1.4|0.2| id_5|Iris-setosa|
        |5.4|3.9|1.7|0.4| id_6|Iris-setosa|
        |4.6|3.4|1.4|0.3| id_7|Iris-setosa|
        |5.0|3.4|1.5|0.2| id_8|Iris-setosa|
        |4.4|2.9|1.4|0.2| id_9|Iris-setosa|
        |4.9|3.1|1.5|0.1|id_10|Iris-setosa|
        |5.4|3.7|1.5|0.2|id_11|Iris-setosa|
        |4.8|3.4|1.6|0.2|id_12|Iris-setosa|
        |4.8|3.0|1.4|0.1|id_13|Iris-setosa|
        |4.3|3.0|1.1|0.1|id_14|Iris-setosa|
        |5.8|4.0|1.2|0.2|id_15|Iris-setosa|
        |5.7|4.4|1.5|0.4|id_16|Iris-setosa|
        |5.4|3.9|1.3|0.4|id_17|Iris-setosa|
        |5.1|3.5|1.4|0.3|id_18|Iris-setosa|
        |5.7|3.8|1.7|0.3|id_19|Iris-setosa|
        |5.1|3.8|1.5|0.3|id_20|Iris-setosa|
        +---+---+---+---+-----+-----------+
        only showing top 20 rows

        """
        self.importJar()
        _df = global_dict['sc']._jvm.SophonNotebook.getSampleDataFrameByName(spark._jsparkSession, dataName)
        return DataFrame(_df, global_dict['sqlContext'])


    def write_to_inceptor(self, dataFrame, url, usrName, password, tableName, isOverwrite, tableType = "text",
                         isRemote = False, namenodes = "", webHdfsPort = 50070):
        """
        write spark dataFrame to inceptor or hive

        Parameters
        ----------
            dataFrame : pyspark.sql.dataframe.DataFrame
                dataFrame will write to inceptor
            url : string
                inceptor or hive connection url, as: jdbc:hive2://inceptor or hive ip:inceptor or hive Port/databaseName
            usrName : string
                user name who can write to inceptor or hive
            password : string
                password for user to access to inceptor or hive
            tableName : string
                table name to store the data
            isOverwrite : bool
                overwrite the table if table already exist
        Returns
        ----------
            out : none

        Examples
        ----------
        >>> input_iris = entry.get_sample_df_by_name(spark, "iris")
        >>> entry.write_to_inceptor(input_iris, "jdbc:hive2://************:10000/default", "hive", "123456", "iris_data", True)

        """
        self.importJar()
        global_dict['sc']._jvm.SophonNotebook.writeToInceptor(dataFrame._jdf, global_dict['spark']._jsparkSession, url, usrName, password, tableName, isOverwrite, tableType, isRemote, namenodes, webHdfsPort)

    def distribute_run_script(self, spark, resourcePath, scriptPath, scriptArgs, columns, currentUsr, ifUploadAll = True, partitions = 10, delimiter = ",", scriptCMD = "python2"):
        """
        common method to run script in distribute way

        Parameters
        ----------
            spark : pyspark.sql.session.SparkSession
                pyspark session
            resourcePath : string
                resource path including data or depend data the script need
            scriptPath : string
                script entry to run in distribute way
            scriptArgs : string
                script extra args
            columns : List[[name, type]]
                output dataFrame schema
            currentUsr : user name
                user name to get script from nfs
            ifUploadAll : bool
                upload all resource, , default as "True"
            partitions : int
                num to split the job, default as 10
            delimiter : string
                Separator to split csv data stored in temporary file, default as ","
            scriptCMD : string
                linux command, such as python2, python3 or Rscript, default as "python2"

        Returns
        ----------
            out : pyspark.sql.dataframe.DataFrame
                spark dataframe spark from certian sample dataset name

        Examples
        ----------
        >>> column = [["closeFillTime","string"],["closePrice","float"],["closeTime","string"],["holdTime","float"],
          ["instrument","string"],["openFillTime","string"],["openPrice","float"],["openTime","string"],
          ["pnlRate","float"],["quantity","int"],["tradeFlag","string"],
          ["para","string"],["strategyName","string"],["strategyID","string"]]
        >>> test = entry.distribute_run_script(spark, resourcePath="Sample01/", scriptPath="demo01.py",
                   scriptArgs="${PARTITIONS} ${INDEX} ${FILE} ", columns=column, currentUsr="test",
                   ifUploadAll = True, partitions = 6, delimiter = ";", scriptCMD = "python2")
        """
        self.importJar()
        output_df = global_dict['sc']._jvm.SophonNotebook.distributeScript(spark._jsparkSession,  resourcePath, scriptPath, scriptArgs, columns, currentUsr, ifUploadAll, partitions, delimiter, scriptCMD)
        return DataFrame(output_df, global_dict['sqlContext'])

    def add_file_type_model(self, filePath, pid, modelName, versionName = ""):
        """
        add file type model in model management

        Parameters
        ----------
            filePath : string
                model path stored as file stored in sophon-web contianer
            pid : string
                project id in sophon
            modelName: string
                model name as stored in model management
            versionName: string
                version name as stored in model management


        Returns
        ----------
            out : none

        Examples
        ----------
        >>> from sklearn import svm
        >>> from sklearn import datasets
        >>> clf = svm.SVC(gamma='scale')
        >>> iris = datasets.load_iris()
        >>> X, y = iris.data, iris.target
        >>> clf.fit(X, y)
        >>> from joblib import dump, load
        >>> dump(clf, '/tmp/sklearn.joblib')
        >>> entry.add_file_type_model("/tmp/sklearn.joblib", "54844200-f843-4257-ac2f-267a94424db4", "sklearn")

        """
        self.importJar()
        global_dict['sc']._jvm.SophonNotebook.addFileTypeModel(filePath, pid, modelName, versionName)

    def get_model_store_as_file(self, version_id, path, isDirectory = False):
        """
        get model from model management and stored as file in sophon container

        Parameters
        ----------
            version_id : string
                version id of this model
            path : string
                target path to store the model

        Returns
        ----------
            out : none

        Examples
        ----------
        >>> entry.get_model_store_as_file("sklearn-uuidxxx", "/tmp/sklearn.joblib")
        """
        self.importJar()
        global_dict['sc']._jvm.SophonNotebook.getModelStoreAsFile(version_id, path, isDirectory)

    def get_latest_model_store_as_file(self, pid, name, path, isDirectory = False):
        """
        get latest model from model management and stored as file in sophon container

        Parameters
        ----------
            pid : string
                project id in sophon
            name : string
                model name stored in model management
            path : string
                target path to store the model

        Returns
        ----------
            out : none

        Examples
        ----------
        >>> entry.get_latest_model_store_as_file("54844200-f843-4257-ac2f-267a94424db4", "sklearn", "/tmp/sklearn.joblib")
        >>> from joblib import dump, load
        >>> from sklearn import svm
        >>> from sklearn import datasets
        >>> clf2 = load('/tmp/sklearn.joblib')
        >>> clf2.predict(X[0:1])[0]
        """
        self.importJar()
        global_dict['sc']._jvm.SophonNotebook.getLatestModelStoreAsFile(pid, name, path, isDirectory)

    def get_model_list_by_name(self, pid, modelName):
        """
        get model version name list according to model name

        Parameters
        ----------
            pid : string
                project id in sophon
            modelName : string
                model name stored in model management

        Returns
        ----------
            out : list
                model version list of certian model name

        Examples
        ----------
        >>> version_list = entry.get_model_list_by_name("54844200-f843-4257-ac2f-267a94424db4", "sklearn")
        >>> version_list
        ["sklearn-3jnsdh7a", "sklearn-91jsy7a2"]

        """
        self.importJar()
        return _java2py(global_dict['sc'], global_dict['sc']._jvm.SophonNotebook.getModelListByName(pid, modelName))

    def add_native_spark_model(self, model, pid, modelName, versoionName = ""):
        """
        save spark machine learning model to model magament

        Parameters
        ----------
            model : pyspark.ml.modeltype
                python spark model to save
            pid : string
                project id in sophon
            modelName : string
                model name stored in model management
            versoionName : string
                version name stored in model management

        Returns
        ----------
            out : none

        Examples
        ----------
        >>> from pyspark.sql import Row
        >>> from pyspark.ml.linalg import Vectors
        >>> from pyspark.ml.classification import LogisticRegression
        >>> bdf = sc.parallelize([
                Row(label=1.0, weight=1.0, features=Vectors.dense(0.0, 5.0)),
                Row(label=0.0, weight=2.0, features=Vectors.dense(1.0, 2.0)),
                Row(label=1.0, weight=3.0, features=Vectors.dense(2.0, 1.0)),
                Row(label=0.0, weight=4.0, features=Vectors.dense(3.0, 3.0))]).toDF()
        >>> blor = LogisticRegression(regParam=0.01, weightCol="weight")
        >>> blorModel = blor.fit(bdf)
        >>> entry.add_native_spark_model(blorModel, "54844200-f843-4257-ac2f-267a94424db4", "lrmodel")

        """
        self.importJar()
        if "mllib" in model.__module__:
            raise Exception("save model to model management only support ml model, "
                            "you can try save it to hdfs using model.save(sc, hdfs_path)")
        # Self-developed model
        if "pyspark" not in model.__module__:
            # referring the java model class name
            java_class = model.java_package_name
            if java_class == "":
                raise Exception("Model write method has not been implemented!")
            # python model save
            hdfs_path = "/tmp/sophonModel/" + str(uuid.uuid4())
            model.save(hdfs_path)
            java_obj = global_dict['sc']._jvm
            for name in java_class.split("."):
                java_obj = getattr(java_obj, name)
            # java model load
            java_model = java_obj.read().load(hdfs_path)
        # Native spark model
        else:
            java_model = model._to_java()
        global_dict['sc']._jvm.SophonNotebook.addNativeSparkModel(java_model, pid,
                                                                  modelName, versoionName)

    @staticmethod
    def __java_package_to_python(java_package_name):
        python_package_name = ''
        for model_name in EntryPoint.pysophon_models.keys():
            if model_name in java_package_name:
                python_package_name = 'pysophon.ml.' + EntryPoint.pysophon_models.get(model_name)
                break
        if python_package_name == '':
            python_package_name = java_package_name.replace("org.apache.spark", "pyspark")
        return python_package_name

    @staticmethod
    def __from_java(java_stage, python_package_name):
        """
        Given a Java model, create and return a Python wrapper of it.
        Used for ML persistence.
        Refer to the method of the same name in JavaParams
        """
        def __get_class(clazz):
            """
            Loads Python class from its name.
            """
            parts = clazz.split('.')
            module = ".".join(parts[:-1])
            m = __import__(module)
            for comp in parts[1:]:
                m = getattr(m, comp)
            return m
        py_type = __get_class(python_package_name)
        # Load information from java_stage to the instance.
        py_stage = py_type()
        py_stage._java_obj = java_stage
        # SPARK-10931: Temporary fix so that persisted models would own params from Estimator
        py_stage._create_params_from_java()
        py_stage._resetUid(java_stage.uid())
        py_stage._transfer_params_from_java()
        return py_stage

    def get_spark_model_with_version_id(self, versionId):
        """
        get spark machine learning model from model magament and tansfer to pyspark model

        Parameters
        ----------
            versionId : string
                model version id stored in model management

        Returns
        ----------
            out : pyspark.ml.model
                pyspark model type

        Examples
        ----------
        >>> entry.get_spark_model_with_version_id("c56e0816-f4c0-45ec-877f-3e65934aa510")
        LogisticRegressionModel: uid = LogisticRegression_6f9bd8976c0a, numClasses = 2, numFeatures = 2

        """
        self.importJar()
        java_obj = global_dict['sc']._jvm.SophonNotebook.getSparkModelWithVersionId(versionId)
        java_package_name = java_obj.write().getClass().getName().split("$")[0]
        python_package_name = EntryPoint.__java_package_to_python(java_package_name)
        return EntryPoint.__from_java(java_obj, python_package_name)

    def get_latest_model_version_spark(self, pid, name):
        """
        get latest spark machine learning model from model magament and tansfer to pyspark model

        Parameters
        ----------
            pid : string
                project id in sophon
            name : string
                model name

        Returns
        ----------
            out : pyspark.ml.model
                pyspark model type

        Examples
        ----------
        >>> entry.get_latest_model_version_spark("54844200-f843-4257-ac2f-267a94424db4", "lrmodel")
        LogisticRegressionModel: uid = LogisticRegression_6f9bd8976c0a, numClasses = 2, numFeatures = 2

        """
        self.importJar()
        java_obj = global_dict['sc']._jvm.SophonNotebook.getLatestModelVersionSpark(pid, name)
        java_package_name = java_obj.write().getClass().getName().split("$")[0]
        python_package_name = EntryPoint.__java_package_to_python(java_package_name)
        return EntryPoint.__from_java(java_obj, python_package_name)


def sophonMain():
    entry = EntryPoint()
    global_dict['entry'] = entry
    main()


if __name__ == '__main__':
    sys.exit(sophonMain())
