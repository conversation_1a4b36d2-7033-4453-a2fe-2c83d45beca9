package io.transwarp.sophon.engine.job.model

import io.transwarp.sophon.model.ModelCache
import org.apache.livy.{JobContext, Job}

class CheckModelExistJob(historyId: String,
                         op: String,
                         port: String
                        ) extends Job[Boolean] {
  override def call(jobContext: JobContext): Boolean = {
    val m = ModelCache.get(s"${historyId}:${op}:${port}")
    m match {
      case Some(_) => true
      case None => false
    }
  }
}
