package handler

import (
	"k8s.io/klog"
	"path"
	"regexp"
	"transwarp.io/mlops/llmops-fluent-bit/config"
	"transwarp.io/mlops/llmops-fluent-bit/dao"
	"transwarp.io/mlops/llmops-fluent-bit/prometheus"
	"transwarp.io/mlops/llmops-fluent-bit/util"
)

const (
	commonErrRegexp = `(?i)(error|fail|panic|fatal|err)`
)

func NewErrorHandler(log *Logger) LogHandler {
	return func(line string, tag LogTag) error {
		if containsError(line) && filterWhiteList(line) {
			/*files, err := dao.GetSqliteByName(log.PodName, log.CtnName)
			if err != nil {
				klog.Errorf("can not get data from sqlite, err is %s", err.Error())
				return err
			}

			desc := fmt.Sprintf("\ntimeStamp: %s, offset: %v\n", time.Now().Format("20060102"), files.Offset)

			err = util.WriteToFile(dst, desc)
			if err != nil {
				return err
			}
			*/

			dst := path.Join(tag.PodName, tag.CtnName) + "_err.log"
			prometheus.InsertLogCount2Prom(dao.LogTagMetric{
				PodName:      log.PodName,
				PodNamespace: log.NameSpace,
				CntName:      log.CtnName,
			})

			return util.WriteToFile(dst, line)
		}
		return nil
	}
}

func filterWhiteList(line string) bool {
	whitelist := config.Conf.Whitelist
	if whitelist == "" {
		return true
	}

	_, err := regexp.Compile(whitelist)
	if err != nil {
		klog.Errorf(" whitelist is not valid regex, will be ignore")
		return true
	}
	return !regexp.MustCompile(whitelist).MatchString(line)

}

func containsError(line string) bool {
	re := regexp.MustCompile(commonErrRegexp)
	return re.MatchString(line)
}
