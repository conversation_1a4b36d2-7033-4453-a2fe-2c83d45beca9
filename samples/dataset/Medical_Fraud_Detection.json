{"id": "sample_Medical_Fraud_Detection", "name": "Medical_Fraud_Detection", "desc": "Medical_Fraud_Detection", "sourceType": "hdfs", "params": {"file": "Medical_Fraud_Detection.csv"}, "schemaConfig": {"columns": [{"name": "amount_paid_to_date", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": []}, {"name": "number_presc_to_date", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "max_presc_to_date", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "max_presc_per_doctor", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "max_presc_per_hospital", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "max_presc_per_year", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "id", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "FRAUD_LABEL", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": []}, {"name": "amount_paid_per_year", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": []}, {"name": "amount_paid_per_hospital", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": []}, {"name": "amount_paid_per_doctor", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": []}, {"name": "amount_paid_to_prescription", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": []}, {"name": "amount_paid_total", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": []}, {"name": "number_presc_per_year", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "number_presc_per_hospital", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "number_presc_per_doctor", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "number_presc_to_prescription", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}, {"name": "number_presc_total", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": []}]}}