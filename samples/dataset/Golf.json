{"id": "sample_golf", "name": "golf", "desc": "golf", "params": {"file": "golf.csv"}, "sourceType": "hdfs", "schemaConfig": {"columns": [{"name": "outlook", "type": "string", "role": "feature", "meaning": "text", "ignore": false, "values": []}, {"name": "temperature", "type": "int", "role": "feature", "meaning": "text", "ignore": false, "values": []}, {"name": "humidity", "type": "int", "role": "feature", "meaning": "text", "ignore": false, "values": []}, {"name": "wind", "type": "string", "role": "feature", "meaning": "text", "ignore": false, "values": []}, {"name": "play", "type": "string", "role": "label", "meaning": "text", "ignore": false, "values": []}]}}