{"id": "sample_mb_us_navy_fleet", "name": "mb_us_navy_fleet", "desc": "", "sourceType": "hdfs", "params": {"file": "mb_us_navy_fleet.csv"}, "schemaConfig": {"columns": [{"name": "WIKIID", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": null, "message": "", "hide": false, "comment": null}, {"name": "中文名称", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": null, "message": "", "hide": false, "comment": null}, {"name": "总部所在地", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": null, "message": "", "hide": false, "comment": null}, {"name": "创立时间", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": null, "message": "", "hide": false, "comment": null}]}, "format": {"format": "csv", "params": {"encoding": "UTF-8", "inferSchema": "true", "header": "true", "delimiter": ",", "multiLine": "false"}}}