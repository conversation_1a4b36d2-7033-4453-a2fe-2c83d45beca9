{"id": "sample_kbqa_company", "name": "kbqa_company", "desc": "kbqa_company", "sourceType": "hdfs", "params": {"file": "kbqa_company.csv"}, "schemaConfig": {"columns": [{"name": "index", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": null, "message": "", "hide": false, "comment": null}, {"name": "entity", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": null, "message": "", "hide": false, "comment": null}]}, "format": {"format": "csv", "params": {"encoding": "UTF-8", "inferSchema": "false", "header": "true", "delimiter": ",", "multiLine": "false"}}}