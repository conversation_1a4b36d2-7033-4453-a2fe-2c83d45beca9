{"root": {"id": "gjOMoNeEx", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"w": 0, "h": 0, "ops": [{"id": "1xcb77mO8", "desc": "dataset", "name": "sentimentAnalysisTest", "x": -120, "y": 101, "params": {"source": "sample_sentimentAnalysis", "isTmp": "false"}, "enabled": true, "markFeed": false, "units": []}, {"id": "UlY0Lqnaq", "desc": "filter", "name": "Filter", "x": -126, "y": 227, "params": {"condition": "notMissing"}, "enabled": true, "markFeed": false, "units": []}, {"id": "4vIag6Bad", "desc": "sentiment_analysis", "name": "Sentiment Analysis", "x": -119, "y": 361, "params": {"inputColumn": "content", "outputColumn": "rs"}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "1xcb77mO8", "toOp": "UlY0Lqnaq", "fromPort": "output", "toPort": "input"}, {"fromOp": "UlY0Lqnaq", "toOp": "4vIag6Bad", "fromPort": "output", "toPort": "input"}, {"fromOp": "4vIag6Bad", "toOp": null, "fromPort": "output", "toPort": "result 1"}], "notes": []}]}, "versionNumber": 1}