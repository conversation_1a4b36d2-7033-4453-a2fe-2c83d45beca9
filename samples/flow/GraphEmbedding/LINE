{"root": {"id": "cNWnmwa9-", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"w": 0, "h": 0, "ops": [{"id": "uU-wb7h6M", "desc": "dataset", "name": "eu_email_edges", "x": -1058, "y": 207, "params": {"source": "eu_email_edges", "isTmp": "false"}, "enabled": true, "markFeed": false, "units": []}, {"id": "-8vXRol3G", "desc": "line", "name": "LINE", "x": -1010, "y": 280, "params": {"isDirected": "false", "embeddingSize": "16", "proximityOrder": "2", "negativeSampleNum": "5", "maxIter": "15", "initialLearningRate": "1.0", "normalizeEmbedding": "false", "intermediateStorageLevel": "MEMORY_AND_DISK", "finalStorageLevel": "MEMORY_AND_DISK", "srcVertexCol": "fromNodeID", "dstVertexCol": "toNodeID", "numVertexBlocks": "2"}, "enabled": true, "markFeed": false, "units": []}, {"id": "6c4Nmpg7y", "desc": "dataset", "name": "eu_email_nodes", "x": -709, "y": 192, "params": {"source": "eu_email_nodes", "isTmp": "false"}, "enabled": true, "markFeed": false, "units": []}, {"id": "wdRs9S21xm", "desc": "join", "name": "Join", "x": -911, "y": 357, "params": {"join_type": "inner", "left_prefix": "", "right_prefix": "", "on": "[[\"vertexID\",\"nodeID\"]]", "left columns": "[\"embeddingVector\"]", "right columns": "[\"nodeID\",\"label\"]"}, "enabled": true, "markFeed": false, "units": []}, {"id": "68OZElssSd", "desc": "set_role", "name": "Set Role", "x": -898, "y": 430, "params": {"setAdditionalRoles": "{\"embeddingVector\":\"feature\",\"label\":\"label\"}", "attributeName": "nodeID", "targetRole": "regular"}, "enabled": true, "markFeed": false, "units": []}, {"id": "a7iXsh1WYK", "desc": "split", "name": "Split", "x": -880, "y": 531, "params": {"stratifiedSplit": "true", "seed": "0", "partitions": "[\"0.8\",\"0.2\"]"}, "enabled": true, "markFeed": false, "units": []}, {"id": "GPJhalmPZ", "desc": "logistic_regression", "name": "Logistic Regression", "x": -812, "y": 607, "params": {"threshold": "0.5", "maxIter": "10", "regParam": "0.0", "tol": "1.0E-6", "elasticNetParam": "0.0", "standardization": "true", "aggregationDepth": "2", "fitIntercept": "true", "weightCol": ""}, "enabled": true, "markFeed": false, "units": []}, {"id": "ngCX7RfHU4", "desc": "apply_model", "name": "Apply Model", "x": -895, "y": 705, "params": {}, "enabled": true, "markFeed": false, "units": []}, {"id": "XFw9dl2gv", "desc": "evaluator_multi", "name": "Evaluator Multi", "x": -775, "y": 806, "params": {"confusion matrix": "true", "weighted recall": "false", "weighted precision": "false", "weighted f measure": "false", "accuracy": "false", "main criterion": "weighted recall"}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "uU-wb7h6M", "toOp": "-8vXRol3G", "fromPort": "output", "toPort": "input"}, {"fromOp": "-8vXRol3G", "toOp": "wdRs9S21xm", "fromPort": "output", "toPort": "left"}, {"fromOp": "6c4Nmpg7y", "toOp": "wdRs9S21xm", "fromPort": "output", "toPort": "right"}, {"fromOp": "wdRs9S21xm", "toOp": "68OZElssSd", "fromPort": "output", "toPort": "input"}, {"fromOp": "68OZElssSd", "toOp": "a7iXsh1WYK", "fromPort": "output", "toPort": "input"}, {"fromOp": "a7iXsh1WYK", "toOp": "ngCX7RfHU4", "fromPort": "partition 1", "toPort": "input"}, {"fromOp": "a7iXsh1WYK", "toOp": "GPJhalmPZ", "fromPort": "partition 2", "toPort": "train set"}, {"fromOp": "GPJhalmPZ", "toOp": "ngCX7RfHU4", "fromPort": "model", "toPort": "model"}, {"fromOp": "ngCX7RfHU4", "toOp": null, "fromPort": "output", "toPort": "result 1"}, {"fromOp": "ngCX7RfHU4", "toOp": "XFw9dl2gv", "fromPort": "output", "toPort": "input"}, {"fromOp": "XFw9dl2gv", "toOp": null, "fromPort": "performance", "toPort": "result 2"}], "notes": []}]}, "versionNumber": 1, "description": ""}