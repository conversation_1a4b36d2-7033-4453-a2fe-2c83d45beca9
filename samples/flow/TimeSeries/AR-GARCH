{"root": {"id": "rJGmr6uGm", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"ops": [{"id": "ByD6Q4XXm", "desc": "dataset", "name": "r_arima_dataset1", "x": -131, "y": 196, "params": {"source": "sample_r_arima_dataset1", "isTmp": "false"}, "enabled": true, "markFeed": false, "units": []}, {"id": "S1P07N7QQ", "desc": "set_role", "name": "Set Role", "x": -67, "y": 267, "params": {"setAdditionalRoles": "{}", "attributeName": "timeseries", "targetRole": "label"}, "enabled": true, "markFeed": false, "units": []}, {"id": "rJWyHV777", "desc": "argarch", "name": "ARGARCH", "x": 34, "y": 438, "params": {"max eval": "10000", "max iter": "10000", "no intercept": "false", "m": "2", "p": "3", "q": "2", "time column": "date"}, "enabled": true, "markFeed": false, "units": []}, {"id": "HJ3-V4mQ7", "desc": "apply_model", "name": "Apply Model", "x": -131, "y": 569, "params": {}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "ByD6Q4XXm", "fromPort": "output", "toOp": "S1P07N7QQ", "toPort": "input"}, {"fromOp": "S1P07N7QQ", "fromPort": "output", "toOp": "HJ3-V4mQ7", "toPort": "input"}, {"fromOp": "S1P07N7QQ", "fromPort": "output", "toOp": "rJWyHV777", "toPort": "train set"}, {"fromOp": "HJ3-V4mQ7", "fromPort": "output", "toPort": "result 1"}, {"fromOp": "rJWyHV777", "fromPort": "model", "toOp": "HJ3-V4mQ7", "toPort": "model"}, {"fromOp": "rJWyHV777", "fromPort": "model", "toPort": "result 2"}], "notes": []}]}}