{"root": {"id": "SJeJWzY9M", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100"}, "enabled": true, "markFeed": false, "units": [{"ops": [{"id": "HJ0z-MK5G", "desc": "dataset", "name": "deals", "x": -241, "y": 127, "params": {"source": "sample_deals"}, "enabled": true, "markFeed": false, "units": []}, {"id": "HyDlbGK9z", "desc": "set_role", "name": "set_role", "x": -239, "y": 228, "params": {"setAdditionalRoles": "{}", "attributeName": "future_customer", "targetRole": "label"}, "enabled": true, "markFeed": false, "units": []}, {"id": "HydNbGY5z", "desc": "string_index", "name": "string_index", "x": -240, "y": 322, "params": {"indexAllStringType": "false", "columns": "[\"gender\",\"payment_method\"]"}, "enabled": true, "markFeed": false, "units": []}, {"id": "Hkfw-zF5z", "desc": "logistic_regression", "name": "logistic_regression", "x": -156, "y": 436, "params": {"maxIter": "100", "regParam": "0.0", "tol": "1.0E-6", "elasticNetParam": "0.0", "standardization": "true", "aggregationDepth": "2", "fitIntercept": "true", "threshold": "0.5"}, "enabled": true, "markFeed": false, "units": []}, {"id": "SJwYZMYqf", "desc": "apply_model", "name": "apply_model", "x": -293, "y": 524, "params": {}, "enabled": true, "markFeed": false, "units": []}, {"id": "S1-tWGYcG", "desc": "evaluator_binary", "name": "evaluator_binary", "x": -272, "y": 625, "params": {"confusion matrix": "true", "ROC": "true", "k-s": "true", "precision recall curve": "true", "lift": "true", "main criterion": "ROC", "positive label value": "yes"}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "HJ0z-MK5G", "fromPort": "output", "toOp": "HyDlbGK9z", "toPort": "input"}, {"fromOp": "HyDlbGK9z", "fromPort": "output", "toOp": "HydNbGY5z", "toPort": "input"}, {"fromOp": "HydNbGY5z", "fromPort": "output", "toOp": "Hkfw-zF5z", "toPort": "train set"}, {"fromOp": "HydNbGY5z", "fromPort": "output", "toOp": "SJwYZMYqf", "toPort": "input"}, {"fromOp": "Hkfw-zF5z", "fromPort": "model", "toOp": "SJwYZMYqf", "toPort": "model"}, {"fromOp": "Hkfw-zF5z", "fromPort": "model", "toPort": "result 2"}, {"fromOp": "SJwYZMYqf", "fromPort": "output", "toOp": "S1-tWGYcG", "toPort": "input"}, {"fromOp": "S1-tWGYcG", "fromPort": "performance", "toPort": "result 1"}], "notes": []}]}}