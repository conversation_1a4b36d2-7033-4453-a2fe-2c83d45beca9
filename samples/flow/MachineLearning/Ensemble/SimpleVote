{"root": {"id": "B1u-fmhkX", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"w": 0, "h": 0, "ops": [{"id": "HkPiOx4gX", "desc": "dataset", "name": "banana", "x": -243, "y": 64, "params": {"source": "sample_banana"}, "enabled": true, "markFeed": false, "units": []}, {"id": "ryp3deEgQ", "desc": "set_role", "name": "Set Role", "x": -229, "y": 142, "params": {"setAdditionalRoles": "{}", "attributeName": "a3", "targetRole": "label"}, "enabled": true, "markFeed": false, "units": []}, {"id": "rk5AcgExQ", "desc": "simple_vote", "name": "Simple Voting", "x": -94, "y": 253, "params": {"number_array": "[\"1\",\"1\",\"2\",\"2\"]"}, "enabled": true, "markFeed": false, "units": [{"w": 0, "h": 0, "ops": [{"id": "BJ_1olNxQ", "desc": "logistic_regression", "name": "Logistic Regression", "x": -452, "y": 372, "params": {"threshold": "1", "maxIter": "10", "regParam": "0.0", "tol": "1.0E-6", "elasticNetParam": "0.0", "standardization": "true", "aggregationDepth": "2", "fitIntercept": "true", "weightCol": ""}, "enabled": true, "markFeed": false, "units": []}, {"id": "r1FkjeNgX", "desc": "svm", "name": "svm", "x": -271, "y": 384, "params": {"maxIter": "100", "threshold": "1", "standardization": "true", "tol": "1.0E-6", "fitIntercept": "true", "aggregationDepth": "2", "weightCol": "", "use one_vs_rest": "false", "numIterations": "2", "regParam": "1"}, "enabled": true, "markFeed": false, "units": []}, {"id": "H1s1ie4em", "desc": "multiLayer_perceptron", "name": "Multi-Layer Perceptron", "x": -76, "y": 389, "params": {"seed": "2", "layers": "[\"2\",\"32\",\"12\",\"2\"]", "blockSize": "128", "solver": "l-bfgs", "maxIter": "10", "tol": "1.0E-6", "stepSize": "0.1"}, "enabled": true, "markFeed": false, "units": []}, {"id": "B1ayig4gX", "desc": "decision_tree_classifier", "name": "Decision Tree Classifier", "x": 104, "y": 379, "params": {"enableVectorIndexer": "false", "maxCategories": "32", "maxDepth": "5", "maxBins": "30", "handelInvalid": "skip", "minInstancesPerNode": "1", "minInfoGain": "0.0", "maxMemoryInMB": "256", "cacheNodeIds": "false", "checkPointInterval": "10", "impurity": "entropy", "seed": "5"}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "BJ_1olNxQ", "toOp": null, "fromPort": "model", "toPort": "result 1"}, {"fromOp": "r1FkjeNgX", "toOp": null, "fromPort": "model", "toPort": "result 2"}, {"fromOp": "H1s1ie4em", "toOp": null, "fromPort": "model", "toPort": "result 3"}, {"fromOp": "B1ayig4gX", "toOp": null, "fromPort": "model", "toPort": "result 4"}, {"fromOp": null, "toOp": "BJ_1olNxQ", "fromPort": "train set", "toPort": "train set"}, {"fromOp": null, "toOp": "r1FkjeNgX", "fromPort": "train set", "toPort": "train set"}, {"fromOp": null, "toOp": "H1s1ie4em", "fromPort": "train set", "toPort": "train set"}, {"fromOp": null, "toOp": "B1ayig4gX", "fromPort": "train set", "toPort": "train set"}], "notes": []}]}, {"id": "BJBCdxVeQ", "desc": "apply_model", "name": "Apply Model", "x": -208, "y": 340, "params": {}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "HkPiOx4gX", "toOp": "ryp3deEgQ", "fromPort": "output", "toPort": "input"}, {"fromOp": "ryp3deEgQ", "toOp": "BJBCdxVeQ", "fromPort": "output", "toPort": "input"}, {"fromOp": "ryp3deEgQ", "toOp": "rk5AcgExQ", "fromPort": "output", "toPort": "train set"}, {"fromOp": "rk5AcgExQ", "toOp": "BJBCdxVeQ", "fromPort": "model", "toPort": "model"}, {"fromOp": "rk5AcgExQ", "toOp": null, "fromPort": "model", "toPort": "result 2"}, {"fromOp": "BJBCdxVeQ", "toOp": null, "fromPort": "output", "toPort": "result 1"}], "notes": []}]}, "versionNumber": 1}