{"root": {"id": "S19q_xqcG", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"w": 0, "h": 0, "ops": [{"id": "NhOOegZqZ", "desc": "dataset", "name": "chinagdp", "x": -174, "y": 127, "params": {"source": "sample_chinagdp", "isTmp": "false"}, "enabled": true, "markFeed": false, "units": []}, {"id": "JmpXz3N6m", "desc": "string_index", "name": "String Index", "x": -99, "y": 246, "params": {"indexAllStringType": "true", "handle invalid": "error", "index order": "frequencyDesc", "excludeIDColumns": "false"}, "enabled": true, "markFeed": false, "units": []}, {"id": "p-uwdhoz9", "desc": "split", "name": "Split", "x": -196, "y": 360, "params": {"stratifiedSplit": "false", "seed": "0", "partitions": "[\"0.3\",\"0.7\"]"}, "enabled": true, "markFeed": false, "units": []}, {"id": "b_lTloNvQ", "desc": "svd", "name": "SVD", "x": -96, "y": 463, "params": {"mode": "<PERSON><PERSON><PERSON><PERSON>", "k": "1", "percentage": "0.99"}, "enabled": true, "markFeed": false, "units": []}, {"id": "Eh4TIfRMm", "desc": "apply_model", "name": "Apply Model", "x": -285, "y": 542, "params": {}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "NhOOegZqZ", "toOp": "JmpXz3N6m", "fromPort": "output", "toPort": "input"}, {"fromOp": "JmpXz3N6m", "toOp": "p-uwdhoz9", "fromPort": "output", "toPort": "input"}, {"fromOp": "p-uwdhoz9", "toOp": "Eh4TIfRMm", "fromPort": "partition 1", "toPort": "input"}, {"fromOp": "p-uwdhoz9", "toOp": "b_lTloNvQ", "fromPort": "partition 2", "toPort": "input"}, {"fromOp": "b_lTloNvQ", "toOp": "Eh4TIfRMm", "fromPort": "model", "toPort": "model"}, {"fromOp": "b_lTloNvQ", "toOp": null, "fromPort": "output", "toPort": "result 2"}, {"fromOp": "Eh4TIfRMm", "toOp": null, "fromPort": "output", "toPort": "result 1"}], "notes": []}]}, "versionNumber": 1, "description": ""}