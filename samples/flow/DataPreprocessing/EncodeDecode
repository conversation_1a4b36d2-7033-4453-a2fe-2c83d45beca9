{"root": {"id": "ByjgK2Mem", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"ops": [{"id": "r1yqddUxX", "desc": "dataset", "name": "golf", "x": -62, "y": 48, "params": {"source": "sample_golf"}, "enabled": true, "markFeed": false, "units": []}, {"id": "H1xC5sLgm", "desc": "encode", "name": "encode", "x": -147, "y": 140, "params": {"column": "outlook", "character set": "UTF-8", "output column": "encode-outlook"}, "enabled": true, "markFeed": false, "units": []}, {"id": "rJpTciUgX", "desc": "ascii", "name": "ascii", "x": 32, "y": 214, "params": {"column": "outlook", "output column": "res"}, "enabled": true, "markFeed": false, "units": []}, {"id": "rkkCqoUe7", "desc": "decode", "name": "decode", "x": -147, "y": 264, "params": {"column": "encode-outlook", "character set": "UTF-8", "output column": "decode-outlook"}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "r1yqddUxX", "fromPort": "output", "toOp": "H1xC5sLgm", "toPort": "input"}, {"fromOp": "r1yqddUxX", "fromPort": "output", "toOp": "rJpTciUgX", "toPort": "input"}, {"fromOp": "rJpTciUgX", "fromPort": "output", "toPort": "result 2"}, {"fromOp": "rkkCqoUe7", "fromPort": "output", "toPort": "result 1"}, {"fromOp": "H1xC5sLgm", "fromPort": "output", "toOp": "rkkCqoUe7", "toPort": "input"}], "notes": []}]}}