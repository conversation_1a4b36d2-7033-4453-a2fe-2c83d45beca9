{"root": {"id": "Byp4xJx1Q", "desc": "process", "name": "Process", "x": 0, "y": 0, "params": {"max_rows": "100", "timeout": "0"}, "enabled": true, "markFeed": false, "units": [{"ops": [{"id": "rkB8xygy7", "desc": "dataset", "name": "golf", "x": -101, "y": 91, "params": {"source": "sample_golf"}, "enabled": true, "markFeed": false, "units": []}, {"id": "S1JFgke1X", "desc": "concat", "name": "concat", "x": -348, "y": 280, "params": {"column": "[\"outlook\",\"temperature\"]", "operator": "concat", "output column": "res"}, "enabled": true, "markFeed": false, "units": []}, {"id": "HJxYeylkm", "desc": "concat", "name": "concat", "x": -98, "y": 280, "params": {"column": "[\"outlook\",\"temperature\"]", "operator": "sep", "output column": "res", "separator": "&"}, "enabled": true, "markFeed": false, "units": []}, {"id": "Hk-txklkQ", "desc": "concat", "name": "concat", "x": 118, "y": 272, "params": {"column": "[\"outlook\",\"temperature\"]", "operator": "format", "format": "%s%d", "output column": "res"}, "enabled": true, "markFeed": false, "units": []}], "connections": [{"fromOp": "rkB8xygy7", "fromPort": "output", "toOp": "S1JFgke1X", "toPort": "input"}, {"fromOp": "rkB8xygy7", "fromPort": "output", "toOp": "HJxYeylkm", "toPort": "input"}, {"fromOp": "rkB8xygy7", "fromPort": "output", "toOp": "Hk-txklkQ", "toPort": "input"}, {"fromOp": "S1JFgke1X", "fromPort": "output", "toPort": "result 1"}, {"fromOp": "HJxYeylkm", "fromPort": "output", "toPort": "result 2"}, {"fromOp": "Hk-txklkQ", "fromPort": "output", "toPort": "result 3"}], "notes": []}]}}