package migrate

import (
	"gorm.io/gorm"
	"strings"
	migrate "transwarp.io/mlops/mlops-std/database/migration"
	"transwarp.io/mlops/serving/dao"
)

func MigrateData(db *gorm.DB) {

	_ = migrate.RegisterUpdate(updateMlopsContainer, 1)
	/*	_ = migrate.RegisterUpdate(startEventScheduler, 2)
		_ = migrate.RegisterUpdate(serviceEventScheduler, 3)
		_ = migrate.RegisterUpdate(addTriggerAfterInsert, 4)
		_ = migrate.RegisterUpdate(addTriggerAfterUpdate, 5)*/

	err := migrate.UpdateDB(db, 0)
	if err != nil {
		panic(err)
	}
}

func startEventScheduler(db *gorm.DB) error {
	tx := db.Session(&gorm.Session{NewDB: true}).Debug()

	sql := `SET GLOBAL event_scheduler = ON;`

	return tx.Exec(sql).Error

}

func serviceEventScheduler(db *gorm.DB) error {

	tx := db.Session(&gorm.Session{NewDB: true}).Debug()

	sql := `
		drop event IF EXISTS  decrement_seconds;
    `
	err := tx.Exec(sql).Error
	if err != nil && !strings.Contains(err.Error(), "not exist") {
		return err
	}

	sql = `
    CREATE EVENT IF NOT EXISTS decrement_seconds
    ON SCHEDULE EVERY 1 SECOND
    DO
    BEGIN
        UPDATE mlops_service_infos
        SET limit_time_db = limit_time_db - 1, updated_at = updated_at
        WHERE limit_time_db > 0;
    END;
    `

	return tx.Exec(sql).Error
}

func updateMlopsContainer(db *gorm.DB) error {
	tx := db.Session(&gorm.Session{NewDB: true}).Debug()

	sql := `
    UPDATE mlops_service_container_infos
    SET resource_limit = JSON_SET(
        JSON_REMOVE(resource_limit, '$.cpu', '$.memory'),
        '$.cpu_request', JSON_EXTRACT(resource_limit, '$.cpu'),
        '$.memory_request', JSON_EXTRACT(resource_limit, '$.memory'),
        '$.cpu_limit', IF(JSON_EXTRACT(resource_limit, '$.cpu_limit') = '' OR JSON_EXTRACT(resource_limit, '$.cpu_limit') IS NULL, JSON_EXTRACT(resource_limit, '$.cpu'), JSON_EXTRACT(resource_limit, '$.cpu_limit')),
        '$.memory_limit', IF(JSON_EXTRACT(resource_limit, '$.memory_limit') = '' OR JSON_EXTRACT(resource_limit, '$.memory_limit') IS NULL, JSON_EXTRACT(resource_limit, '$.memory'), JSON_EXTRACT(resource_limit, '$.memory_limit'))
    );
    `

	container := dao.MlopsServiceContainerInfo{}
	return tx.Model(&container).Exec(sql).Error
}

func addTriggerAfterInsert(db *gorm.DB) error {
	tx := db.Session(&gorm.Session{NewDB: true}).Debug()

	sql := `
		drop trigger after_insert_mlops_service_version_infos;
    `

	err := tx.Exec(sql).Error
	if err != nil && !strings.Contains(err.Error(), "not exist") {
		return err
	}

	sql = `
		CREATE  TRIGGER after_insert_mlops_service_version_infos
		AFTER INSERT ON mlops_service_version_infos
		FOR EACH ROW
		BEGIN
			DECLARE total_qps DOUBLE DEFAULT 0;
		
			-- 计算所有相关版本的 RequestLimit 总数
			SELECT SUM(
						JSON_UNQUOTE(JSON_EXTRACT(rate_limit, '$.RequestLimit')) * 
        				JSON_UNQUOTE(JSON_EXTRACT(hpa_cfg, '$.replicas'))
    				)
			INTO total_qps
			FROM mlops_service_version_infos
			WHERE service_id = NEW.service_id
			  AND JSON_VALID(rate_limit)
			  AND JSON_UNQUOTE(JSON_EXTRACT(rate_limit, '$.RequestLimit')) IS NOT NULL
              AND JSON_VALID(hpa_cfg)
	          AND JSON_UNQUOTE(JSON_EXTRACT(hpa_cfg, '$.replicas')) IS NOT NULL
              AND deleted_at = 0;
		
			-- 检查 mlops_service_infos 表中是否存在对应的 service_id
			IF EXISTS (SELECT 1 FROM mlops_service_infos WHERE id = NEW.service_id) THEN
				UPDATE mlops_service_infos
				SET total_rate_qps = total_qps
				WHERE id = NEW.service_id;
			END IF;
		END
	`

	return tx.Exec(sql).Error
}

func addTriggerAfterUpdate(db *gorm.DB) error {
	tx := db.Session(&gorm.Session{NewDB: true}).Debug()

	sql := `
		drop trigger after_update_mlops_service_version_infos;
    `

	err := tx.Exec(sql).Error
	if err != nil && !strings.Contains(err.Error(), "not exist") {
		return err
	}

	sql = `
		CREATE TRIGGER after_update_mlops_service_version_infos
		AFTER UPDATE ON mlops_service_version_infos
		FOR EACH ROW
		BEGIN
			DECLARE total_qps DOUBLE DEFAULT 0;
		
			-- 计算所有相关版本的 RequestLimit 总数
			SELECT SUM(
						JSON_UNQUOTE(JSON_EXTRACT(rate_limit, '$.RequestLimit')) * 
        				JSON_UNQUOTE(JSON_EXTRACT(hpa_cfg, '$.replicas'))
    				)
			INTO total_qps
			FROM mlops_service_version_infos
			WHERE service_id = NEW.service_id
			  AND JSON_VALID(rate_limit)
			  AND JSON_UNQUOTE(JSON_EXTRACT(rate_limit, '$.RequestLimit')) IS NOT NULL
              AND JSON_VALID(hpa_cfg)
	          AND JSON_UNQUOTE(JSON_EXTRACT(hpa_cfg, '$.replicas')) IS NOT NULL
              AND deleted_at = 0;
		
			-- 检查 mlops_service_infos 表中是否存在对应的 service_id
			IF EXISTS (SELECT 1 FROM mlops_service_infos WHERE id = NEW.service_id) THEN
				UPDATE mlops_service_infos
				SET total_rate_qps = total_qps
				WHERE id = NEW.service_id;
			END IF;
		END
	`

	return tx.Exec(sql).Error
}
