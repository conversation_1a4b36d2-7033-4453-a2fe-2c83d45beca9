package expense

import (
	"encoding/json"
	"errors"
	"log"
	"time"

	apiextensionsv1beta1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1"
)

const (
	GPUVendorUnKnow GPUVendor = "UnKnow"
	GPUVendorNvidia GPUVendor = "NVIDIA"
	GPUVendorAscend GPUVendor = "Ascend"
)

type GPUVendor = string

const (
	ResourceType_Exclusive ResourceType = "exclusive"
	ResourceType_Shared    ResourceType = "shared"

	RunningStatus_Running RunningStatus = "running"
	RunningStatus_Idle    RunningStatus = "idle"

	UsageStatus_Occupied UsageStatus = "occupied" // 占用
	UsageStatus_Idle     UsageStatus = "idle"     // 空闲
)

type (

	// ResourceType 表示资源的独占与共享类型
	ResourceType string

	// RunningStatus 运行状态
	RunningStatus string

	// UsageStatus 占用状态
	UsageStatus string
)

// NewRunningStatusFromBool true 为 idle, false 为 running
func NewRunningStatusFromBool(b bool) RunningStatus {
	if b {
		return RunningStatus_Idle
	}
	return RunningStatus_Running
}

type businessData struct {
	CreatedAt   time.Time `json:"created_at"`
	CreatedUser string    `json:"created_user"`
}

func NewBusinessData(user string, t ...time.Time) apiextensionsv1beta1.JSON {
	if len(t) == 0 {
		t = append(t, time.Now())
	}
	b := &businessData{
		CreatedAt:   t[0],
		CreatedUser: user,
	}
	bs, _ := json.Marshal(b)
	return apiextensionsv1beta1.JSON{Raw: bs}
}

func GetBusinessData(k apiextensionsv1beta1.JSON) businessData {
	b := businessData{}
	err := json.Unmarshal(k.Raw, &b)
	if err != nil {
		log.Println(err)
	}
	return b
}

type ResourceGroup struct {
	Id          string               `json:"id"`
	Name        string               `json:"name"`
	Desc        string               `json:"desc"`
	GroupType   ResourceType         `json:"group_type" description:"资源组类型: exclusive,shared"`
	Nodes       []*GroupNode         `json:"nodes" description:"节点列表"`
	BindTenants []*GroupTenantStatus `json:"bind_tenants"`
	UsageStatus UsageStatus          `json:"usage_status" description:"占用状态: occupied:占用,idle:空闲"`
	CreatedAt   time.Time            `json:"created_at"`
	CreatedUser string               `json:"created_user"`
}

func (r *ResourceGroup) ToResourceGroupTenant(tenantID string) ResourceGroupTenant {
	tenantStatus := GroupTenantStatus{}
	for _, ts := range r.BindTenants {
		if ts.Id != tenantID {
			continue
		}
		tenantStatus = GroupTenantStatus{
			GroupTenant: GroupTenant{
				Id:          tenantID,
				Name:        ts.Name,
				CreatedUser: ts.CreatedUser,
			},
			Idle:      ts.Idle,
			Available: ts.Available,
		}
	}
	return ResourceGroupTenant{
		Id:           r.Id,
		Name:         r.Name,
		Desc:         r.Desc,
		GroupType:    r.GroupType,
		TenantStatus: tenantStatus,
		CreatedAt:    r.CreatedAt,
		CreatedUser:  r.CreatedUser,
	}
}

func GetTenantResourceGroupMap(rgs []ResourceGroup) map[string][]ResourceGroup {
	res := make(map[string][]ResourceGroup)
	for _, rg := range rgs {
		for _, t := range rg.BindTenants {
			if v, ok := res[t.Id]; ok {
				res[t.Id] = append(v, rg)
			} else {
				res[t.Id] = []ResourceGroup{rg}
			}
		}
	}
	return res
}

func BatchCvtToResourceGroupTenant(resourceGroups []ResourceGroup, tenantID string) []ResourceGroupTenant {
	res := make([]ResourceGroupTenant, 0)
	for _, rg := range resourceGroups {
		res = append(res, rg.ToResourceGroupTenant(tenantID))
	}
	return res
}

type ResourceGroupTenant struct {
	Id           string            `json:"id"`
	Name         string            `json:"name"`
	Desc         string            `json:"desc"`
	GroupType    ResourceType      `json:"group_type" description:"资源组类型: exclusive,shared"`
	TenantStatus GroupTenantStatus `json:"tenant_status"`
	CreatedAt    time.Time         `json:"created_at"`
	CreatedUser  string            `json:"created_user"`
}

type GroupNode struct {
	Name               string       `json:"name" description:"节点名"`
	ClusterId          string       `json:"cluster_id" description:"集群id"`
	NodeType           ResourceType `json:"node_type" description:"exclusive,shared"`
	Gpus               []*GroupGpu  `json:"gpus" description:"已选择的gpu"`
	ClusterName        string       `json:"cluster_name" description:"集群名"`
	CpuTotal           int32        `json:"cpu_total" description:"cpu总量,核"`
	MemoryTotal        int32        `json:"memory_total" description:"内存总量,GiB"`
	TotalVGpuMemoryGiB int32        `json:"total_vgpu_memory_gib" description:"GPU显存总量"`
	PodMax             int          `json:"pod_max"`
	// Status             RunningStatus `json:"status" description:"running,idle"`
	SelectedGroupId string `json:"selected_group_id" description:"已经属于某个资源组,资源组id"`
}

type GroupGpu struct {
	UUID            string        `json:"uuid"`
	Index           uint          `json:"index"`
	Model           string        `json:"model" description:"型号"`
	Vendor          GPUVendor     `json:"vendor" description:"厂商: NVIDIA,Ascend"`
	TotalMemoryGIB  int32         `json:"totalMemoryGiB" description:"显存总量 GiB"`
	Status          RunningStatus `json:"status" description:"running,idle"`
	SelectedGroupId string        `json:"selected_group_id" description:"已经属于某个资源组,资源组id"`
	Node            string        `json:"node" description:"node name"`
}
type GroupTenant struct {
	Id          string `json:"id" description:"租户id"`
	Name        string `json:"name"`
	CreatedUser string `json:"created_user" description:"绑定关系的创建人"`
}

type GroupTenantStatus struct {
	GroupTenant
	Idle      bool `json:"idle" description:"空闲或者运行中"`
	Available bool `json:"available" description:"可用或已占用"`
}

// ResourceUsage 算力使用
type ResourceUsage struct {
	CoreUsedRate  int32      `json:"core_used_rate" description:"算力使用率, %"`
	GPUMemUsedGi  int32      `json:"gpu_mem_used_gi" description:"显存占用, Gi"`
	GPUMemTotalGi int32      `json:"gpu_mem_total_gi" description:"显存总量, Gi"`
	PodUsed       int32      `json:"pod_used" description:"pod使用量"`
	PodTotal      int32      `json:"pod_total" description:"pod总量"`
	Services      []*Service `json:"services" description:"服务列表"`
}
type Service struct {
	Id                 string `json:"id" description:"服务id"`
	Name               string `json:"name"`
	ProjectName        string `json:"project_name"`
	Type               string `json:"type" description:"类型"`
	CreatedUser        string `json:"created_user"`
	MemoryUsedGi       int32  `json:"memory_used_gi"`
	MemoryAllocationGi int32  `json:"memory_allocation_gi"`
	CpuUsed            int32  `json:"cpu_used"`
	CpuAllocation      int32  `json:"cpu_allocation"`
	GPUMemUsedGi       int32  `json:"gpu_mem_used_gi" description:"显存占用, Gi"`
	GPUMemAllocationGi int32  `json:"gpu_mem_allocation_gi" description:"显存总量, Gi"`
	CoreUsed           int32  `json:"core_used" description:"算力使用"`
	CoreAllocation     int32  `json:"core_allocation" description:"算力分配"`
}

func (r *ResourceGroup) Valid() error {
	if r.Name == "" {
		return errors.New("miss name")
	}
	if r.GroupType == "" {
		return errors.New("miss name")
	}
	if r.GroupType != ResourceType_Exclusive && r.GroupType != ResourceType_Shared {
		return errors.New("group.type err")
	}
	for _, node := range r.Nodes {
		if node.NodeType != ResourceType_Exclusive && node.NodeType != ResourceType_Shared {
			return errors.New("node type err")
		}
	}
	return nil
}
