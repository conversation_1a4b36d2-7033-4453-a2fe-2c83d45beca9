package io.transwarp.sophon.code.repo

import java.io.{BufferedWriter, FileWriter, _}
import java.nio.file.Files
import java.nio.file.attribute.BasicFileAttributes

import io.transwarp.sophon.code.{CodeEntity, CodeService}
import io.transwarp.aip.commons.json.JsonSer
import resource._

/**
  * Created by lcconly on 18-6-8.
  */
class CodeRepo(val basedir: String, val targetFolder: String) {
  def folder: File = {
    val f = new File(basedir, targetFolder)
    f.mkdirs()
    f
  }
  val folderFile = folder

  protected def deserialize(inputStream: InputStream): CodeEntity = {
    JsonSer.fromJson(inputStream, classOf[CodeEntity])
  }
  private def checkEntity(str: String): Boolean = {
    !str.endsWith(CodeService.NbSuffix) && !str.endsWith(CodeService.PySuffix) &&
      !str.endsWith(CodeService.RSuffix) && !str.startsWith(".") && !str.contains(".") &&
      new File(s"${basedir}/${targetFolder}/${str}").isFile
  }

  protected def updateTimestamp(s: CodeEntity): Unit = {
    val f = new File(folder, s.id)
    val attr = Files.readAttributes(f.toPath, classOf[BasicFileAttributes])
    s.createTimestamp = attr.creationTime().toMillis
    s.modifyTimestamp = attr.lastModifiedTime().toMillis
  }

  private def read(id: String): Option[CodeEntity] = {
    val file = new File(folder, id)

    val entity = managed(new FileInputStream(file)).map{ inputStream =>
      val e = deserialize(inputStream)
      updateTimestamp(e)
      e
    }

    entity.opt
  }
  def findOne(id: String): CodeEntity = {
    val t = folder.list().find(_ == id).getOrElse(
      throw new Exception(s"id ${id} not found")
    )
    read(t).getOrElse(
      throw new Exception(s"id ${id} not found")
    )
  }

  def findAll(): Iterable[CodeEntity] = {
    val ids = folderFile.list()
    ids.filter(checkEntity(_)).map(id => findOne(id))
  }
  def getCodeSuffix(entry: CodeEntity): String = {
    if (entry.`type` == CodeService.Notebook) {
      CodeService.NbSuffix
    }
    else {
      if (entry.language == CodeService.Python3) {
        CodeService.PySuffix
      } else {
        CodeService.RSuffix
      }
    }
  }
  protected def getFilePattern(id: String): String = {
    s"${targetFolder}/${id}"
  }

  def saveCode(entry: CodeEntity): CodeEntity = saveCode(entry, "")
  def saveCode(entry: CodeEntity, mesg: String): CodeEntity = {
    // since we save timestamp in file meta, discard them in object
    val suffix = getCodeSuffix(entry)
    val filePath = s"${basedir}/${getFilePattern(s"${entry.name}.${suffix}")}"
    if(new File(filePath).exists()) {
      new File(filePath).delete()
    }
    val out = new FileWriter(filePath, false)
    out.write(entry.content)
    out.close()
    entry
  }

  def updateName(entry: CodeEntity, oldName: String): CodeEntity = {
    val suffix = getCodeSuffix(entry)
    val oldFilePath = s"${basedir}/${getFilePattern(s"${oldName}.${suffix}")}"
    val newFilePath = s"${basedir}/${getFilePattern(s"${entry.name}.${suffix}")}"
    new File(oldFilePath).renameTo(new File(newFilePath))
    entry
  }

  def deleteCode(baseDir: String, code: CodeEntity): Unit = {
    val suffix = getCodeSuffix(code)
    new File(s"${baseDir}/${getFilePattern(s"${code.name}.${suffix}")}").delete()
  }
}

object CodeRepo {
  val SPARK = "spark"
  val PYSPARK3 = "pyspark3"
  val SPARKR = "sparkr"
  val PYTHON3 = "python3"
}
