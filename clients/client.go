package clients

import (
	"crypto/tls"
	"fmt"
	"strings"
	"transwarp.io/applied-ai/aiot/csm-backend/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/dockerimage"

	//"k8s.io/client-go/kubernetes"
	//"transwarp.io/applied-ai/aiot/csm-backend/conf"
	//"transwarp.io/applied-ai/aiot/vision-std/stderr"

	//"fmt"
	//docker "github.com/docker/docker/client"
	//httptransport "github.com/go-openapi/runtime/client"
	//"github.com/go-openapi/strfmt"
	//kpct "github.com/influxdata/kapacitor/client/v1"
	//edmc "github.com/thingio/edmc/client"
	//edmco "github.com/thingio/edmc/client/operations"
	"net/http"
	//"strings"
	//"transwarp.io/applied-ai/aiot/vision-backend/conf"
	//"transwarp.io/applied-ai/aiot/vision-std/boot"
	//"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	//"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	//"transwarp.io/applied-ai/aiot/vision-std/license"
	//"transwarp.io/applied-ai/aiot/vision-std/srs"
	//"transwarp.io/applied-ai/aiot/vision-std/stderr"
	//"transwarp.io/applied-ai/aiot/vision-std/stdhub"
	//"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	//"transwarp.io/applied-ai/aiot/vision-std/stdstatus"/**/
	//"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	//"transwarp.io/applied-ai/aiot/vision-std/toolkit/dockerimage"
	//"transwarp.io/applied-ai/aiot/vision-std/transport/mqtt"
	//mwc "transwarp.io/applied-ai/model-whs/mw-client/client"
	//"transwarp.io/applied-ai/model-whs/mw-client/client/operations"
)

// const DefaultKapacitorAddr = "http://127.0.0.1:9092"
var (
	//InfluxdbCli *influxdb.InfluxClient // 用于查询时序数据库中的数据
	// 	MqttCli      mqtt.MQTTClient          // 用于收发MQTT消息的Client
	// 	StatusMgr    stdstatus.StatusManager  // 状态管理组件, 提供可运行系统资源的统一收集与存储等
	// 	DockerCli    *docker.Client           // 与Docker守护进程通信, 用于镜像启动(boot)/ 镜像构建(faas)
	//K8sCli *k8s.Client // 与K8S API SERVER 通信
	// 	InfluxWriter influxdb.InfluxWriter    // 高并发的时序数据库写入客户端
	// 	BootMgr      boot.Manager             // 管理边缘平台的容器
	// 	KapacitorCli *kpct.Client             // 用于与时序流处理引擎通信
	// 	Notifier     *stdstatus.MsgSender     // 用于向所有前端界面发送系统通知消息(右下角弹窗形式)
	// 	SrsCli       srs.MMGatewayAPI         // 用户和多媒体网关交互的客户端
	// 	MWhsCli      operations.ClientService // 模型仓库的客户端
	// 	EdmCli       edmco.ClientService      // edge-device-manager 的客户端
	// 	DLIEClients  DLIEClientCache          // Deep learning Inference Engine(Triton Inference Server)的客户端
	ImageHubCli dockerimage.ImageHubAPI // Docker Registry 客户端
	// 	Verifier     *license.Verifier        // 与授权服务器交互的客户端
	//
	// 	RscHub                stdhub.ResourceHub
	HttpCli               *http.Client // 用于一般请求
	HttpCliWithoutTimeout *http.Client // 用于进行文件传输等耗时较长的请求
	CommitterCli          *CommitterClient
	ResourceCli           *ResourceClient
	CasCli                *CasClient
	//
	// 	// 注意，初始化时各客户端之间存在依赖，因此需要按顺序进行。
	commonInitializers = []cliInitializer{
		// 		initMQTTCli,
		// 		initRscHubCli,
		// 		initNotifier,
		initHttpCli,
		initHttpCliWithoutTimeout,
		//initCommitterCli,
		initResourceCli,
		initCasCli,
		//initInfluxDBCli,
		initImageHubCli,
		// 		initDockerCli,
		//initK8SCli,
		// 		initMwhCli,
		// 		initEdmCli,
		// 		initLicenseVerifier,
	}
	//edgeInitializers = []cliInitializer{
	// 		initInfluxDBCli,
	// 		initSrsCli,
	// 		initKapacitorCli,
	// 		initBootMgrCli,
	// 		initEdgeStatusMgr,
	// 		initTritonClients,
	// 		initImageHubCli,
	// 	}
)

type cliInitializer func() error

func Init() {
	if err := startCommonClients(); err != nil {
		panic(any(fmt.Errorf("failed to start common clients: %+v", err)))
	}
}

func startCommonClients() error {
	if err := initClients(commonInitializers); err != nil {
		return err
	}

	return nil
}

func initClients(initializers []cliInitializer) error {
	for _, initializer := range initializers {
		name := strings.TrimPrefix(toolkit.GetFunctionName(initializer), "init")
		if err := initializer(); err != nil {
			stdlog.WithError(err).Errorf("failed to initialize %s", name)
			return err
		}
		stdlog.Infof("succeed to initialize %s", name)
	}
	return nil
}

//	func initEdgeStatusMgr() error {
//		StatusMgr = stdstatus.NewEdgeStatusMgrWithConfig(&stdstatus.EdgeConfig{
//			EdgeId: conf.EdgeID(),
//			Influx: conf.Config.InfluxDB,
//		})
//		if err := StatusMgr.Init(); err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initMQTTCli() error {
//		MqttCli = mqtt.NewMQTTClient(conf.Config.Mqtt)
//		if err := MqttCli.Start(); err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initLicenseVerifier() error {
//		Verifier = license.NewVerifier(conf.Config.License.VerifierPath, conf.Config.License.LicensorAddr)
//		return nil
//	}
//func initInfluxDBCli() (err error) {
//	c := conf.Config.InfluxDB
//	InfluxdbCli, err = influxdb.NewInfluxClient(c)
//	if err != nil {
//		return stderr.Wrap(err, "influxdb's config maybe illegal: %+v", c)
//	}
//	if err = InfluxdbCli.CreateDB(); err != nil {
//		return stderr.Wrap(err, "fail to create database by: %+v", c)
//	}
//
//	//InfluxWriter, err = influxdb.NewInfluxWriter(influxdb.InfluxWriterConfig{
//	//	Cfg:           conf.Config.InfluxDB,
//	//	MaxBatch:      1000,
//	//	MaxConcurrent: 20,
//	//}, nil)
//	//if err != nil {
//	//	return stderr.Wrap(err, "failed to init influxdb writer")
//	//}
//	return nil
//}

//
// func initBootMgrCli() (err error) {
// 	BootMgr, err = boot.NewBootManager(conf.Config.Engine)
// 	if err != nil {
// 		return stderr.Wrap(err, "boot manager's config maybe illegal: %+v", conf.Config.Engine)
// 	}
// 	return
// }
//

//	func initSrsCli() error {
//		c := conf.Config.MMGateway
//		SrsCli = srs.NewMMGatewayAPI(c.Host, c.HttpPort, c.Timeout)
//		if SrsCli == nil {
//			return fmt.Errorf("invalid mm-gateway config: %+v", c)
//		}
//		return nil
//	}
//
//	func initKapacitorCli() (err error) {
//		url := conf.Config.Plmgr.TickRunner.KapacitorAddr
//		if url == "" {
//			url = DefaultKapacitorAddr
//		}
//		if !strings.HasPrefix(url, "http://") {
//			url = "http://" + url
//		}
//		KapacitorCli, err = kpct.New(kpct.Config{URL: url})
//		if err != nil {
//			return err
//		}
//		return
//	}
//
//	func initRscHubCli() (err error) {
//		RscHub, err = stdhub.NewResourceHub(conf.Config.DBPath, stdhub.NewDropRevisionOption(conf.Config.DropRscRevision))
//		if err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initNotifier() error {
//		Notifier = stdstatus.CreateMsgSender()
//		return nil
//	}
func initHttpCli() error {
	HttpCli = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		Timeout: conf.Config.HttpTimeout,
	}
	return nil
}
func initHttpCliWithoutTimeout() error {
	HttpCliWithoutTimeout = &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	return nil
}

func initCommitterCli() error {
	CommitterCli = &CommitterClient{
		HttpClient: HttpClient{
			token: conf.Config.Token,
		},
	}
	return nil
}

func initResourceCli() error {
	ResourceCli = &ResourceClient{
		HttpClient: HttpClient{
			token: conf.Config.Token,
		},
	}
	return nil
}

func initCasCli() error {
	CasCli = &CasClient{
		HttpClient: HttpClient{
			token: conf.Config.Token,
		},
	}
	return nil
}

//	func initDockerCli() (err error) {
//		DockerCli, err = docker.NewClientWithOpts(docker.WithVersion(conf.Config.Engine.DockerApiVersion))
//		if err != nil {
//			return err
//		}
//		return nil
//	}
//
//	func initMwhCli() error {
//		c := conf.Config.ModelWhs
//		// WARNING: 不要使用 sdk.DefaultSchemes
//		// transwarp.io/aip/model-whs/mw-backend/api/swagger/mount.go 中定义了 swagger.Schemes = []string{"https", "http"}
//		// 会导致使用 https-client
//		schemes := []string{"http"}
//		transport := httptransport.New(c.Endpoint, mwc.DefaultBasePath, schemes)
//		transport.DefaultAuthentication = httptransport.APIKeyAuth("Authorization", "header", conf.Config.AuthToken)
//		MWhsCli = mwc.New(transport, strfmt.Default).Operations
//		if MWhsCli == nil {
//			return fmt.Errorf("invalid model warehouse config: %+v", c)
//		}
//		return nil
//	}
//
//	func initEdmCli() error {
//		c := conf.Config.Edm
//		transport := httptransport.New(c.Endpoint, edmc.DefaultBasePath, []string{"http"})
//		//transport.DefaultAuthentication = httptransport.APIKeyAuth("Authorization", "header", conf.Config.AuthToken)
//		EdmCli = edmc.New(transport, strfmt.Default).Operations
//		if EdmCli == nil {
//			return fmt.Errorf("invalid edm config: %+v", c)
//		}
//		return nil
//	}

func initImageHubCli() error {
	ImageHubCli = dockerimage.NewImageHubCli(conf.Config.RegistryTokens, conf.Config.RegistryTimeout)
	return nil
}
