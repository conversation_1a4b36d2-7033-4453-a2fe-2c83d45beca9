package clients

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type HttpClient struct {
	token string
}

func (c *HttpClient) execute(url, method string, body interface{}) (*http.Response, error) {
	request, err := c.buildRequest(url, method, body)
	if err != nil {
		return nil, stderr.Wrap(err, "Build request error.")
	}
	c.addCommonHeader(request)
	response, err := HttpCliWithoutTimeout.Do(request)
	if err != nil {
		return nil, stderr.Wrap(err, "Execute error.")
	}
	if http.StatusOK > response.StatusCode || response.StatusCode >= http.StatusMultipleChoices {
		data, err := io.ReadAll(response.Body)
		if err != nil {
			return nil, fmt.Errorf("illegal response: [%d] -> %s", response.StatusCode, response.Status)
		}
		return nil, fmt.<PERSON><PERSON>rf("illegal response: [%d] -> %s", response.StatusCode, string(data))
	}
	return response, nil
}

func (c *HttpClient) executeWithTimeout(url, method string, body interface{}) (*http.Response, error) {
	request, err := c.buildRequest(url, method, body)
	if err != nil {
		return nil, stderr.Wrap(err, "Build request error.")
	}
	c.addCommonHeader(request)
	response, err := HttpCli.Do(request)
	if err != nil {
		return nil, stderr.Wrap(err, "Execute error.")
	}
	if http.StatusOK > response.StatusCode || response.StatusCode >= http.StatusMultipleChoices {
		data, err := io.ReadAll(response.Body)
		if err != nil {
			return nil, fmt.Errorf("illegal response: [%d] -> %s", response.StatusCode, response.Status)
		}
		return nil, fmt.Errorf("illegal response: [%d] -> %s", response.StatusCode, string(data))
	}
	return response, nil
}

func (c *HttpClient) parseResponse(response *http.Response, t reflect.Type) (interface{}, error) {
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body")
	}
	v := reflect.New(t)
	defer response.Body.Close()
	err = json.Unmarshal(data, v.Interface())
	if err != nil {
		return nil, err
	}
	return v.Elem().Interface(), nil
}

func (c *HttpClient) buildRequest(url, method string, body interface{}) (*http.Request, error) {
	if body == nil {
		return http.NewRequest(method, url, nil)
	}
	data, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	return http.NewRequest(method, url, bytes.NewReader(data))
}

func (c *HttpClient) addCommonHeader(request *http.Request) {
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", c.token)
}
