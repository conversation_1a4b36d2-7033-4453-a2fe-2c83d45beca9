package metrics

import (
	"k8s.io/klog"
	"transwarp.io/mlops/llmops-fluent-bit/prometheus/template"
)

const (
	AvgDurationTotalName = "llmops_svc_duration_total"
	AvgDurationTotalDesc = "avg duration"
)

type AvgDurationMetric struct {
	Template *template.MetricTemplate
}

func NewAvgDurationMetric(template *template.MetricTemplate) *AvgDurationMetric {
	return &AvgDurationMetric{
		Template: template,
	}
}

func InitAvgDurationMetric() *AvgDurationMetric {
	tem := template.NewMetricTemplate(
		AvgDurationTotalName,
		AvgDurationTotalDesc,
		template.DefaultLabels...,
	)

	metric := NewAvgDurationMetric(tem)

	metric.Template = tem
	metric.Template.UpdateFunc = metric.UpdateFunc

	klog.Infof("InitAvgDurationMetric done!")
	return metric

}

func (v *AvgDurationMetric) UpdateFunc(message *template.Message) {
	v.Template.Collector.WithLabelValues(
		message.RefId,
		message.RefProjectId,
		message.RefVersionId,
		message.RefPodNamespace,
		message.RefPodName,
		message.RefNodeName,
		message.RefPodShortName,
		"service").Add(message.Count)

}
