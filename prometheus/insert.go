package prometheus

import (
	"k8s.io/klog"
	"os"
	"transwarp.io/mlops/llmops-fluent-bit/dao"
	"transwarp.io/mlops/llmops-fluent-bit/prometheus/metrics"
)

const (
	succeedCode = "200"
)

func InsertSvcData2Prom(log dao.VisitLog) {
	//insert to prometheus
	baseMetric := ParseSvc2Metric(log)

	//visit
	if visit != nil && visit.Template != nil {
		baseMetric.Count = 1
		visit.Template.Update(baseMetric)
	}

	//visit succeed count
	if visitSucceed != nil && visitSucceed.Template != nil && log.ResponseCode == succeedCode {
		baseMetric.Count = 1
		visitSucceed.Template.Update(baseMetric)
	}

	//avg
	if avg != nil && avg.Template != nil {
		baseMetric.Count = float64(log.Duration)
		avg.Template.Update(baseMetric)
	}

	//input token
	if input != nil && input.Template != nil {
		baseMetric.Count = float64(log.PromptTokens)
		input.Template.Update(baseMetric)
	}

	// output token
	if output != nil && output.Template != nil {
		baseMetric.Count = float64(log.CompletionTokens)
		output.Template.Update(baseMetric)
	}

	//first token
	if first != nil && first.Template != nil {
		baseMetric.Count = float64(log.FirstTokenTime)
		first.Template.Update(baseMetric)
	}
}

func InsertLogCount2Prom(log dao.LogTagMetric) {
	//insert to prometheus
	baseMetric := ParseErr2Metric(log)
	podErr.Template.Update(baseMetric)
}

func registryMetricsData() {
	var records []dao.MetricsCount
	klog.Infof("node_name is %s", os.Getenv("NODE_NAME"))
	err := dao.CommonDb.Clone().Model(&dao.MetricsCount{}).
		Where("count > ?", 0).
		Where("ref_node_name = ? ", os.Getenv("NODE_NAME")).
		Find(&records).Error
	if err != nil {
		klog.Errorf("can not registry metrics from db to prome, err is %s", err.Error())
		return
	}
	for _, record := range records {
		defaultVal := ParseRecord2Metric(record)
		switch record.MetricType {
		case metrics.AvgDurationTotalName, metrics.FirsTokenTotalName, metrics.InputTokenTotalName,
			metrics.OutputTokenTotalName, metrics.VisitCountTotalName:
			visit.Template.SetMetricsDefault(defaultVal)
		case metrics.PodErrLogDesc:
			podErr.Template.SetPodErrDefault(defaultVal)
		}
	}
}
