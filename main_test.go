package main

import (
	"github.com/emicklei/go-restful/v3"
	"github.com/joho/godotenv"
	"github.com/json-iterator/go/extra"
	"k8s.io/apimachinery/pkg/util/rand"
	"log"
	"net/http"
	"testing"
	"time"
	"transwarp.io/applied-ai/aiot/cvat-backend/clients"
	"transwarp.io/applied-ai/aiot/cvat-backend/common"
	"transwarp.io/applied-ai/aiot/cvat-backend/conf"
	"transwarp.io/applied-ai/aiot/cvat-backend/core/samplemgr/processor"
	"transwarp.io/applied-ai/aiot/cvat-backend/dao"
	"transwarp.io/applied-ai/aiot/cvat-backend/service/ws"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func TestMain(m *testing.M) {
	// 加载.env文件用于本地调试
	err := godotenv.Load()
	if err != nil {
		log.Fatal(err)
	}

	rand.Seed(time.Now().UnixNano())

	// init config
	conf.Init()

	conf.InitTrainingConfig()

	common.InitErrMsg()

	// init clients
	clients.Init()

	// init dao
	dao.Init()

	// init core manager
	// ... any others managers
	//trainer.Init()

	// init labels
	//annotation.Init()

	// init ws
	ws.Init()

	// init processor
	processor.Init()

	// 使用jsoniter处理int和string转化
	extra.RegisterFuzzyDecoders()

	cfg := conf.Config.Server
	stdlog.Infoln("server listen at " + cfg.Addr)
	prepareServer()

	// This will run forever until channel receives error
	stdlog.Infof("Staring Sever Template HTTP service on %s ...", cfg.Addr)
	if err := http.ListenAndServe(cfg.Addr, restful.DefaultContainer); err != nil {
		stdlog.Errorf("Could not start serving service due to (error: %s)", err)
	}
}
