package client

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"transwarp.io/mlops/mlops-std/stdlog"
	token_util "transwarp.io/mlops/mlops-std/token"
)

type HttpClient struct {
	*http.Client
	baseUrl string
}

func NewHttpClient(url string) *HttpClient {
	//if url == "" {
	//	url = "http://autocv-cas-service:80"
	//}
	return &HttpClient{
		Client: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
			Timeout: 60 * time.Second,
		},
		baseUrl: url,
	}
}

func (a *HttpClient) url() string {
	return a.baseUrl
}

func (m *HttpClient) doRequest(ctx context.Context, method string, requestUrl string, requestBody interface{}, result interface{}) error {
	var req *http.Request
	var err error
	if requestBody != nil {
		// 将请求体转换为 JSON 格式
		jsonBody, err := json.Marshal(requestBody)
		if err != nil {
			return err
		}

		req, err = http.NewRequest(method, requestUrl, bytes.NewBuffer(jsonBody))
		if err != nil {
			return err
		}
	} else {
		req, err = http.NewRequest(method, requestUrl, nil)
		if err != nil {
			return err
		}
	}
	token, err := token_util.GetToken(ctx)
	if err != nil {
		return err
	}
	req.Header.Set(token_util.AUTH_HEADER, token)
	req.Header.Set("Content-Type", "application/json")
	start := time.Now()
	rsp, err := m.Do(req)
	stdlog.WithFields("url", requestUrl).Infof("do request result:err=%+v,rsp=%+v", err, rsp)

	if err != nil {
		return err
	}
	defer rsp.Body.Close()

	body, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return err
	}
	end := time.Now()
	stdlog.WithFields("url", requestUrl).Infof("do request times:%d", end.Sub(start).Milliseconds())
	if rsp.StatusCode != 200 {
		return fmt.Errorf("status code:%d, err message: %+v", rsp.StatusCode, string(body))
	}
	stdlog.WithFields("url", requestUrl).Infof("do request success,response:%s", string(body))
	if err := json.Unmarshal(body, &result); err != nil {
		stdlog.WithFields("url", requestUrl).Debugf("failed to unmarshal json:%s", string(body))
	}
	return nil
}
