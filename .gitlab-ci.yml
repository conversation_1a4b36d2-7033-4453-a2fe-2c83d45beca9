image: ***********/aip/base/go1.20.1-builder:sophon-0.0

before_script:
  - source /etc/profile
  - export KRUX_COMMON="krux-common"
  - BRANCH_NAME="master"
  - echo ${CI_COMMIT_REF_NAME}
  - git clone -b ${BRANCH_NAME} http://gitlab-ci-token:${CI_JOB_TOKEN}@***********:10080/aip/infra/krux/krux-common.git ${KRUX_COMMON}
  - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWD} ${DOCKER_REPO}
  # docker alias
  - shopt -s expand_aliases
  - alias "killdocker=ps aux | grep docker | grep -v grep | awk '{print \$2}' | xargs kill -9 || true"
  - alias "startdocker=(startdocker.sh &) ; sleep 10"


stages:
  - build


krux-agent-charts:
  image: ***********/tostmp/kube-build-base:ubuntu-focal
  stage: build
  script:
    - make build-chart WHAT=krux-agent
    - make push-chart WHAT=krux-agent
  tags:
    - k8s

krux-agent-image:
  stage: build
  script:
    - startdocker
    - make build-image WHAT=krux-agent ARCH=amd64
    - make push-image WHAT=krux-agent ARCH=amd64 
    - killdocker
  tags:
    - k8s

vgpu-client-image:
  stage: build
  script:
    - startdocker
    - make build-image WHAT=vgpu-client ARCH=amd64
    - make push-image WHAT=vgpu-client ARCH=amd64
    - killdocker
  tags:
    - k8s
