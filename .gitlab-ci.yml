image: ***********/gold/aip-mm/base/golang-gitlab-builder:1.23-ubuntu24.04

stages:
  #  - test
  - trigger
  - build
  - deploy

  
variables:
  DEPS_VERSION: dev-pf

include:
  - project: applied-ai/aiot/common-ci
    ref: master
    file: go-build.yml
  - project: applied-ai/aiot/common-ci
    ref: master
    file: deploy.yml

auto-update-licensor-permission:
  stage: trigger
  allow_failure: true
  only:
    - dev
    - /^llm-.*$/
  script:
    - "curl -X POST --fail -F token=4e824c4e64b17d6577fbb9e650fe28 -F ref=$REF_NAME http://***********/api/v4/projects/13182/trigger/pipeline"
  tags:
    - k8s
