on:
  push:
    tags:
      - '*'
  release:
    types: [created]
name: Build release
jobs:
  build-distro-packages:
    name: build packages
    strategy:
      max-parallel: 48
      fail-fast: true
      matrix:
        distro: [ amazonlinux/2, amazonlinux/2.arm64v8, centos/7, centos/7.arm64v8, debian/jessie,
                  debian/jessie.arm64v8, debian/stretch, debian/stretch.arm64v8, debian/buster,
                  debian/buster.arm64v8, ubuntu/16.04, ubuntu/18.04, ubuntu/20.04, ubuntu/18.04.arm64v8,
                  ubuntu/20.04.arm64v8, raspbian/jessie, raspbian/stretch, raspbian/buster ]

    runs-on: [ ubuntu-latest ] #self-hosted, Linux, X64, packet-builder]
    steps:
      - name: Setup environment
        run: |
            sudo apt-get install --yes qemu binfmt-support qemu-user-static qemu-utils qemu-efi-aarch64 qemu-system-arm docker.io containerd runc
            sudo systemctl unmask docker && sudo systemctl start docker
            docker run --rm --privileged --name qemu multiarch/qemu-user-static:register --reset

      - name: Get the version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}

      - uses: frabert/replace-string-action@master
        id: formatted_version
        with:
          pattern: '[v]*(.*)$'
          string: "${{ steps.get_version.outputs.VERSION }}"
          replace-with: '$1'
          flags: 'g'

      - uses: frabert/replace-string-action@master
        id: formatted_distro
        with:
          pattern: '(.*)\/(.*)$'
          string: "${{ matrix.distro }}"
          replace-with: '$1-$2'
          flags: 'g'

      - uses: actions/checkout@v2
        with:
          repository: ${{ github.actor }}/fluent-bit-packaging
          fetch-depth: 1
          path: packaging

      - name: Build the distro artifacts
        run: ./build.sh -v ${{ env.release }} -d ${{ env.distro }}
        env:
          release: ${{ steps.formatted_version.outputs.replaced }}
          distro: ${{ matrix.distro }}
        working-directory: packaging

      - name: Archive the release artifacts (packages)
        uses: actions/upload-artifact@v2
        with:
          name: release-${{env.release}}-${{env.bucket-name}}-pkgs
          path: |
            packaging/packages/${{env.distro}}/${{env.release}}/**/*
        env:
          bucket-name: ${{ steps.formatted_distro.outputs.replaced }}
          release: ${{ steps.formatted_version.outputs.replaced }}
          distro: ${{ matrix.distro }}

  build-docker-images:
    name: build docker images
    strategy:
      max-parallel: 48
      fail-fast: true
      matrix:
        arch: [ amd64, arm64v8, arm32v7 ]
        suffix: [ x86_64, arm64v8, arm32v7, x86_64-debug ]
        os: [ linux ]
        exclude:
          - {arch: amd64, suffix: arm64v8}
          - {arch: amd64, suffix: arm32v7}
          - {arch: arm64v8, suffix: x86_64}
          - {arch: arm64v8, suffix: arm32v7}
          - {arch: arm64v8, suffix: x86_64-debug}
          - {arch: arm32v7, suffix: x86_64}
          - {arch: arm32v7, suffix: arm64v8}
          - {arch: arm32v7, suffix: x86_64-debug}
    runs-on: [ ubuntu-latest ] #self-hosted, Linux, X64, packet-builder]
    steps:
      - uses: actions/checkout@master
      - name: Setup environment
        run: |
          sudo apt-get install --yes qemu binfmt-support qemu-user-static qemu-utils qemu-efi-aarch64 qemu-system-arm docker.io containerd runc
          sudo systemctl unmask docker && sudo systemctl start docker
          docker run --rm --privileged --name qemu multiarch/qemu-user-static:register --reset

      - name: Get the version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}

      - uses: frabert/replace-string-action@master
        id: formatted_version
        with:
          pattern: '[v]*(.*)$'
          string: "${{ steps.get_version.outputs.VERSION }}"
          replace-with: '$1'
          flags: 'g'

      - name: Build the docker images
        run: docker buildx build --no-cache -f ./dockerfiles/Dockerfile.${{ env.suffix }} -t ${{ env.dockerhub_organization }}/fluent-bit:${{ env.suffix }}-${{ env.release }} --platform ${{ env.os }}/${{ env.arch }} .
        env:
          release: ${{ steps.formatted_version.outputs.replaced }}
          arch: ${{ matrix.arch }}
          suffix: ${{ matrix.suffix }}
          os: ${{ matrix.os }}
          dockerhub_organization: ${{ secrets.DOCKERHUB_ORGANIZATION }}

      - name: Archive the docker images
        uses: ishworkh/docker-image-artifact-upload@v1
        with:
          image: ${{ env.dockerhub_organization }}/fluent-bit:${{ env.arch }}-${{ env.release }}
        env:
          release: ${{ steps.formatted_version.outputs.replaced }}
          arch: ${{ matrix.arch }}
          dockerhub_organization: ${{ secrets.DOCKERHUB_ORGANIZATION }}
