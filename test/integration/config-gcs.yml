general:
  remote_storage: gcs
  upload_concurrency: 4
  download_concurrency: 4
  restore_schema_on_cluster: "{cluster}"
  allow_object_disk_streaming: true
s3:
  disable_ssl: false
  disable_cert_verification: true
clickhouse:
  host: clickhouse
  port: 9000
  restart_command: bash -c 'echo "FAKE RESTART"'
  timeout: 60s
gcs:
  bucket: altinity-qa-test
  path: backup/{cluster}/{shard}
  object_disk_path: object_disks/{cluster}/{shard}
  credentials_file: /etc/clickhouse-backup/credentials.json
  compression_format: tar
