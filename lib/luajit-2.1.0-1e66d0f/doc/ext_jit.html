<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<title>jit.* Library</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="Copyright" content="Copyright (C) 2005-2021">
<meta name="Language" content="en">
<link rel="stylesheet" type="text/css" href="bluequad.css" media="screen">
<link rel="stylesheet" type="text/css" href="bluequad-print.css" media="print">
</head>
<body>
<div id="site">
<a href="https://luajit.org"><span>Lua<span id="logo">JIT</span></span></a>
</div>
<div id="head">
<h1><tt>jit.*</tt> Library</h1>
</div>
<div id="nav">
<ul><li>
<a href="luajit.html">LuaJIT</a>
<ul><li>
<a href="https://luajit.org/download.html">Download <span class="ext">&raquo;</span></a>
</li><li>
<a href="install.html">Installation</a>
</li><li>
<a href="running.html">Running</a>
</li></ul>
</li><li>
<a href="extensions.html">Extensions</a>
<ul><li>
<a href="ext_ffi.html">FFI Library</a>
<ul><li>
<a href="ext_ffi_tutorial.html">FFI Tutorial</a>
</li><li>
<a href="ext_ffi_api.html">ffi.* API</a>
</li><li>
<a href="ext_ffi_semantics.html">FFI Semantics</a>
</li></ul>
</li><li>
<a class="current" href="ext_jit.html">jit.* Library</a>
</li><li>
<a href="ext_c_api.html">Lua/C API</a>
</li><li>
<a href="ext_profiler.html">Profiler</a>
</li></ul>
</li><li>
<a href="status.html">Status</a>
</li><li>
<a href="faq.html">FAQ</a>
</li><li>
<a href="http://wiki.luajit.org/">Wiki <span class="ext">&raquo;</span></a>
</li><li>
<a href="https://luajit.org/list.html">Mailing List <span class="ext">&raquo;</span></a>
</li></ul>
</div>
<div id="main">
<p>
The functions in this built-in module control the behavior of the JIT
compiler engine. Note that JIT-compilation is fully automatic &mdash;
you probably won't need to use any of the following functions unless
you have special needs.
</p>

<h3 id="jit_onoff"><tt>jit.on()<br>
jit.off()</tt></h3>
<p>
Turns the whole JIT compiler on (default) or off.
</p>
<p>
These functions are typically used with the command line options
<tt>-j on</tt> or <tt>-j off</tt>.
</p>

<h3 id="jit_flush"><tt>jit.flush()</tt></h3>
<p>
Flushes the whole cache of compiled code.
</p>

<h3 id="jit_onoff_func"><tt>jit.on(func|true [,true|false])<br>
jit.off(func|true [,true|false])<br>
jit.flush(func|true [,true|false])</tt></h3>
<p>
<tt>jit.on</tt> enables JIT compilation for a Lua function (this is
the default).
</p>
<p>
<tt>jit.off</tt> disables JIT compilation for a Lua function and
flushes any already compiled code from the code cache.
</p>
<p>
<tt>jit.flush</tt> flushes the code, but doesn't affect the
enable/disable status.
</p>
<p>
The current function, i.e. the Lua function calling this library
function, can also be specified by passing <tt>true</tt> as the first
argument.
</p>
<p>
If the second argument is <tt>true</tt>, JIT compilation is also
enabled, disabled or flushed recursively for all sub-functions of a
function. With <tt>false</tt> only the sub-functions are affected.
</p>
<p>
The <tt>jit.on</tt> and <tt>jit.off</tt> functions only set a flag
which is checked when the function is about to be compiled. They do
not trigger immediate compilation.
</p>
<p>
Typical usage is <tt>jit.off(true, true)</tt> in the main chunk
of a module to turn off JIT compilation for the whole module for
debugging purposes.
</p>

<h3 id="jit_flush_tr"><tt>jit.flush(tr)</tt></h3>
<p>
Flushes the root trace, specified by its number, and all of its side
traces from the cache. The code for the trace will be retained as long
as there are any other traces which link to it.
</p>

<h3 id="jit_status"><tt>status, ... = jit.status()</tt></h3>
<p>
Returns the current status of the JIT compiler. The first result is
either <tt>true</tt> or <tt>false</tt> if the JIT compiler is turned
on or off. The remaining results are strings for CPU-specific features
and enabled optimizations.
</p>

<h3 id="jit_version"><tt>jit.version</tt></h3>
<p>
Contains the LuaJIT version string.
</p>

<h3 id="jit_version_num"><tt>jit.version_num</tt></h3>
<p>
Contains the version number of the LuaJIT core. Version xx.yy.zz
is represented by the decimal number xxyyzz.
</p>

<h3 id="jit_os"><tt>jit.os</tt></h3>
<p>
Contains the target OS name:
"Windows", "Linux", "OSX", "BSD", "POSIX" or "Other".
</p>

<h3 id="jit_arch"><tt>jit.arch</tt></h3>
<p>
Contains the target architecture name:
"x86", "x64", "arm", "arm64", "arm64be", "ppc", "mips", "mipsel", "mips64", "mips64el", "mips64r6", "mips64r6el".
</p>

<h2 id="jit_opt"><tt>jit.opt.*</tt> &mdash; JIT compiler optimization control</h2>
<p>
This sub-module provides the backend for the <tt>-O</tt> command line
option.
</p>
<p>
You can also use it programmatically, e.g.:
</p>
<pre class="code">
jit.opt.start(2) -- same as -O2
jit.opt.start("-dce")
jit.opt.start("hotloop=10", "hotexit=2")
</pre>
<p>
Unlike in LuaJIT 1.x, the module is built-in and
<b>optimization is turned on by default!</b>
It's no longer necessary to run <tt>require("jit.opt").start()</tt>,
which was one of the ways to enable optimization.
</p>

<h2 id="jit_util"><tt>jit.util.*</tt> &mdash; JIT compiler introspection</h2>
<p>
This sub-module holds functions to introspect the bytecode, generated
traces, the IR and the generated machine code. The functionality
provided by this module is still in flux and therefore undocumented.
</p>
<p>
The debug modules <tt>-jbc</tt>, <tt>-jv</tt> and <tt>-jdump</tt> make
extensive use of these functions. Please check out their source code,
if you want to know more.
</p>
<br class="flush">
</div>
<div id="foot">
<hr class="hide">
Copyright &copy; 2005-2021
<span class="noprint">
&middot;
<a href="contact.html">Contact</a>
</span>
</div>
</body>
</html>
