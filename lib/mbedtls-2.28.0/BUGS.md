## Known issues

Known issues in Mbed TLS are [tracked on GitHub](https://github.com/ARMmbed/mbedtls/issues).

## Reporting a bug

If you think you've found a bug in Mbed TLS, please follow these steps:

1. Make sure you're using the latest version of a
   [maintained branch](BRANCHES.md): `master`, `development`,
   or a long-time support branch.
2. Check [GitHub](https://github.com/ARMmbed/mbedtls/issues) to see if
   your issue has already been reported. If not, …
3. If the issue is a security risk (for example: buffer overflow,
   data leak), please report it confidentially as described in
   [`SECURITY.md`](SECURITY.md). If not, …
4. Please [create an issue on on GitHub](https://github.com/ARMmbed/mbedtls/issues).

Please do not use GitHub for support questions. If you want to know
how to do something with Mbed TLS, please see [`SUPPORT.md`](SUPPORT.md) for available documentation and support channels.
