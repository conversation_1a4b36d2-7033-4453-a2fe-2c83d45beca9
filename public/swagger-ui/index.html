<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="./swagger-ui.css">
    <link rel="icon" type="image/png" href="./favicon-32x32.png" sizes="32x32"/>
    <link rel="icon" type="image/png" href="./favicon-16x16.png" sizes="16x16"/>
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }

        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            background: #fafafa;
        }
    </style>
</head>

<body>
<div id="swagger-ui"></div>

<script src="./swagger-ui-bundle.js"></script>
<script src="./swagger-ui-standalone-preset.js"></script>
<script>
    window.onload = function () {
        // Build a system
        const ui = SwaggerUIBundle({
            url: window.location.pathname + "swagger.json",
            dom_id: '#swagger-ui',
            deepLinking: true,
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIStandalonePreset
            ],
            plugins: [
                SwaggerUIBundle.plugins.DownloadUrl,
                {
                    statePlugins: {
                        spec: {
                            wrapActions: {
                                updateSpec: function (oriAction, system) {
                                    return (spec) => {
                                        var originSpec = JSON.parse(spec);
                                        originSpec.host = window.location.host;
                                        originSpec.basePath = window.location.pathname
                                            .split("/")
                                            .slice(0, -2)
                                            .join("/");
                                        var newSpec = JSON.stringify(originSpec);
                                        return oriAction(newSpec);
                                    };
                                },
                            },
                        },
                    },
                },
            ],
            layout: "StandaloneLayout"
        })
        window.ui = ui
    }
</script>
</body>
</html>
