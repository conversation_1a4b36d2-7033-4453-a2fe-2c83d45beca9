apiVersion: v1
kind: Service
metadata:
  name: {{ include "sophon-serving.fullname" . }}
  labels:
    {{- include "sophon-serving.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.serverPort }}
      targetPort: {{ .Values.sophon.mlops.serving.serverPort }}
      protocol: TCP
      name: http
    - port: {{ .Values.service.grpcPort }}
      targetPort: {{ .Values.sophon.mlops.serving.grpcPort }}
      protocol: TCP
      name: tcp
  selector:
    {{- include "sophon-serving.selectorLabels" . | nindent 4 }}
