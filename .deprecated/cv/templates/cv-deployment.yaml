apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    {{- include "charts.labels" . | nindent 4 }}
    cv.component: {{ include "charts.servername" . }}
  name: {{ include "charts.servername" . }}
  namespace: {{ .Release.Namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      io.transwarp.aip.service: {{ include "charts.servername" . }}
  strategy:
    type: Recreate
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        io.transwarp.aip.service: {{ include "charts.servername" . }}
    spec:
      {{- if and .Values.global .Values.global.affinity }}
      affinity:
      {{- toYaml .Values.global.affinity | nindent 8 }}
      {{- end}}
      terminationGracePeriodSeconds: 3
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ .Values.global.account.adminName }}
      containers:
        # CV 后端服务
        - command:
            - /bin/boot.sh
          env:
            - name: SOPHON_CVAT_SERVICE_ID
              value: {{ include "charts.servername" . }}
            - name: SOPHON_SERVICE_ID
              value: {{ include "charts.fullname" . }}
            - name: IP_ADDR
              value: {{ include "charts.servername" . }}-service
            {{ if .Values.global.auth.disable }}
            - name: hack_to_skip
              value: skip
            {{ end }}
            - name: k8s_mode
              value: incluster
            {{- with .Values.envs }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.cvJavaBE }}
          imagePullPolicy: {{ .Values.global.images.pullPolicy }}
          name: backend
          ports:
            - name: cv-be
              containerPort: 80
            - name: cv-ws
              containerPort: 81
          resources:
          {{- toYaml .Values.resources | nindent 12 }}
          securityContext:
            privileged: true
          volumeMounts:
            - name: cv-volume # deprecated
              mountPath: /etc/cv/conf/license
              subPath: cv/license
            - name: cv-volume
              mountPath: /var/log/{{ include "charts.servername" . }}
              subPath: cv/log
            - name: cv-volume
              mountPath: /share
              subPath: licensor/share
            - name: cv-volume
              mountPath: /init-share
              subPath: share
            - name: cv-volume
              mountPath: /data/nfs
              subPath: vision/store
            - name: conf-volume
              mountPath: /etc/{{ include "charts.servername" . }}/conf
              subPath: server

        # CV 前端服务
        - command:
            # 新版基础镜像中 ***********/aip/arm64/aip-ui-base:branch-3.0 的默认nginx配置（/etc/nginx/nginx.conf）
            # 中已包含了 daemon off； 因此在启动时不能再次指定 daemon off； 负责会导致 “daemon” directive is duplicate in ...
            - nginx
          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.cvFE }}
          imagePullPolicy: {{ .Values.global.images.pullPolicy }}
          name: frontend
          ports:
            - name: cv-fe
              containerPort: 443
          resources:
          {{- toYaml .Values.resources | nindent 12 }}
          env:
          {{- with .Values.envs }}
          {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            privileged: true
          volumeMounts:
            - name: conf-volume
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: ui/default.conf
        # tensorboard nginx
        - command:
            - nginx
            - -g
            - "daemon off;"
          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.cvTB }}
          imagePullPolicy: {{ .Values.global.images.pullPolicy }}
          name: tensorboard
          ports:
            - name: cv-tb
              containerPort: 6006
          resources:
          {{- toYaml .Values.resources | nindent 12 }}
          env:
          {{- with .Values.envs }}
          {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            privileged: true
          volumeMounts:
            - name: conf-volume
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: tensorboard/default.conf
      restartPolicy: Always
      volumes:
        # cv 持久化文件根目录
        - name: cv-volume
          persistentVolumeClaim:
            claimName: {{ .Values.global.storage.pvcName }}
        # cv 配置文件卷
        - name: conf-volume
          configMap:
            name: cv-configmap
            defaultMode: 0777
            items:
              - key: application.yml
                path: server/application.yml
              - key: log4j2.xml
                path: server/log4j2.xml
              - key: application-server.yml
                path: server/application-server.yml
              - key: application-training-zh.yml
                path: server/application-training-zh.yml
              - key: application-training-en.yml
                path: server/application-training-en.yml
              - key: ui-default.conf
                path: ui/default.conf
              - key: tensorboard-default.conf
                path: tensorboard/default.conf
