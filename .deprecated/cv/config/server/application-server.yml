sophon.k8s.api.server: {{ .Values.k8s.server }}
sophon.k8s.namespace: default
sophon.k8s.ca.cert.file: /srv/k3s/ca.crt
sophon.k8s.client.cert.file: /srv/k3s/user.pem
sophon.k8s.client.key.file: /srv/k3s/key.pem

sophon.k8s.host.network: false
sophon.k8s.cni.networks: overlay
sophon.k8s.image.pull.policy: IfNotPresent

sophon.cv.training.image.cpu: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.trainCpu }}
sophon.cv.training.image.gpu: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.trainGpu }}
sophon.cv.training.detection.start.command: bash autocv_det/pipeline.sh
sophon.cv.training.classification.start.command: bash autocv_cls/pipeline.sh
sophon.cv.training.semantic.start.command: bash autocv_seg/pipeline.sh
sophon.cv.training.instance.start.command: bash autocv_ins_seg/pipeline.sh
sophon.cv.training.ocr.recognize.start.command: bash autocv_det/pipeline.sh
sophon.cv.training.keypoint.recognize.start.command: bash autocv_keypoint/pipeline.sh


sophon.cv.training.detection.eval.command: cd autocv_det/model_eval && bash src/start_eval.sh
sophon.cv.training.classification.eval.command: bash autocv_cls/model_eval/src/start_eval.sh
sophon.cv.training.semantic.eval.command: bash autocv_seg/model_eval/src/start_eval.sh
sophon.cv.training.instance.eval.command: bash autocv_ins_seg/model_eval/src/start_eval.sh
sophon.cv.training.ocr.recognize.eval.command: cd autocv_ocr_server/model_eval && bash src/start_eval.sh
sophon.cv.training.keypoint.recognize.eval.command: bash autocv_keypoint/model_eval/src/start_eval.sh

# TODO 暂时写为sleep以调试
sophon.cv.training.detection.augment.command: bash autocv_det/dataset_augmentation/data_aug_pipeline.sh
sophon.cv.training.classification.augment.command: bash autocv_cls/data_augmentation/data_aug_pipeline.sh
sophon.cv.training.semantic.augment.command: bash autocv_seg/data_augmentation/data_aug_pipeline.sh
sophon.cv.training.instance.augment.command: bash autocv_ins_seg/dataset_augmentation/data_aug_pipeline.sh
sophon.cv.training.ocr.recognize.augment.command: bash autocv_ocr_server/dataset_augmentation/data_aug_pipeline.sh
sophon.cv.training.keypoint.recognize.augment.command: bash autocv_keypoint/dataset_augmentation/data_aug_pipeline.sh

sophon.build.script: /usr/lib/cvat/bin/%s/build.sh

sophon.model.warehouse.object.url: http://autocv-mw-service/api/v1/model-warehouse/models/%s/releases/%s/release-file
sophon.model.warehouse.create.url: http://autocv-mw-service/api/v1/model-warehouse/models
sophon.model.warehouse.release.url: http://autocv-mw-service/api/v1/model-warehouse/models/%s/releases
sophon.model.warehouse.exist.url: http://autocv-mw-service/api/v1/model-warehouse/models/existence
sophon.model.warehouse.release.exist.url: http://autocv-mw-service/api/v1/model-warehouse/models/%s/releases/existence
sophon.model.warehouse.host: http://autocv-mw-service:80

sophon.model.warehouse.token: eyJob3N0IjoiMTI3LjAuMC4xOjMyMDgwIiwibmFtZSI6ImFkbWluIiwic2siOiJmMTliOGRjMjAyOWNmNzA3OTM5ZTg4NmU0YjE2NDY4MSIsInJvbGVzIjpudWxsLCJjcmVhdGVkIjoiMjAyMi0wMi0xMFQxOTozNTowNi42Njk0ODk0MDIrMDg6MDAifQ==

sophon.docker.hub.host: localhost:{{ .Values.global.nodePorts.registry }}

sophon.cv.pre.anno.max.task.number: 5
sophon.cv.pre.anno.model.test.url: https://autocv-portal-service:443/egw/edge/api/v1/svcmgr/services/%s/calls
sophon.cv.pre.recognition.model.inference.url: https://autocv-portal-service:443/egw/edge/api/v1/svcmgr/models/%s

sophon.cv.model.service.list.url: https://autocv-portal-service:443/egw/edge/api/v1/svcmgr/services

sophon.cv.tensorboard.image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.trainTensorboard }}