{{/*
Expand the name of the chart.
*/}}
{{- define "charts.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "charts.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "charts.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "charts.labels" -}}
helm.sh/chart: {{ include "charts.chart" . }}
{{ include "charts.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "charts.selectorLabels" -}}
app.kubernetes.io/name: {{ include "charts.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "charts.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "charts.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Get the docker image repo tag of an container.
返回类似如下形式的镜像模板
 ***********/aip/@:sophon-22.04
 ***********:5000/aip-arm/@:sophon-22.04
后续由具体镜像将 @ 替换为具体的镜像仓库即可
e.g.
 image: {{ include "image-tpl" . | replace "@" "vision-backend" }}
*/}}
{{- define "image-tpl" -}}
{{- printf "%s/%s/@:%s" .Values.global.images.registry .Values.global.images.baseRepo .Values.global.images.baseTag }}
{{- end }}

{{/*
defined by ocr
*/}}
{{- define "charts.servername" -}}
{{ include "charts.fullname" . }}server
{{- end }}

{{- define "charts.uiname" -}}
{{ include "charts.fullname" . }}-ui
{{- end }}

{{- define "charts.mysqlname" -}}
{{ include "charts.fullname" . }}-mysql
{{- end }}

{{- define "charts.ossgwname" -}}
{{ include "charts.fullname" . }}-ossgw
{{- end }}

{{- define "charts.redisname" -}}
{{ include "charts.fullname" . }}-redis
{{- end }}
