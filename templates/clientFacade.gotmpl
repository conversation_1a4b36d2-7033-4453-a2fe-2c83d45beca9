// Code generated by go-swagger; DO NOT EDIT.


{{ if .Copyright -}}// {{ comment .Copyright -}}{{ end }}


package {{ .Package }}

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command
// To edit this file, edit the custom template in templates/clientFacade.gotmpl
// More info on custom templates can be found in the README or here: https://github.com/go-swagger/go-swagger/blob/master/docs/generate/templates.md
// The template itself can be found here: https://github.com/go-swagger/go-swagger/blob/master/generator/templates/client/facade.gotmpl

import (
  "bytes"
  "crypto/tls"
  "encoding/json"
  "io"
  "net/http"
  "net/url"
  "strconv"

  "github.com/go-openapi/runtime"
  httptransport "github.com/go-openapi/runtime/client"
  "github.com/go-openapi/strfmt"
  
  "github.com/grafana/grafana-openapi-client-go/pkg/transport"

  {{ imports .DefaultImports }}
  {{ imports .Imports }}
)

// Default {{ humanize .Name }} HTTP client.
var Default = NewHTTPClient(nil)

const (
    // DefaultHost is the default Host
    // found in Meta (info) section of spec file
    DefaultHost string = {{ printf "%#v" .Host }}
    // DefaultBasePath is the default BasePath
    // found in Meta (info) section of spec file
    DefaultBasePath string = {{ printf "%#v" .BasePath }}
    // Optional property that specifies the org.
    // For more info, see: https://grafana.com/docs/grafana/latest/developers/http_api/auth/
    OrgIDHeader = "X-Grafana-Org-Id"
)

// DefaultSchemes are the default schemes found in Meta (info) section of spec file
var DefaultSchemes = {{ printf "%#v" .Schemes }}

// NewHTTPClient creates a new {{ humanize .Name }} HTTP client.
func NewHTTPClient(formats strfmt.Registry) *{{ pascalize .Name }} {
  return NewHTTPClientWithConfig(formats, nil)
}

// NewHTTPClientWithConfig creates a new {{ humanize .Name }} HTTP client,
// using a customizable transport config.
func NewHTTPClientWithConfig(formats strfmt.Registry, cfg *TransportConfig) *{{ pascalize .Name }} {
  // ensure nullable parameters have default
  if cfg == nil {
    cfg = DefaultTransportConfig()
  }

  // create transport and client
  transport := newTransportWithConfig(cfg)
  return New(transport, cfg, formats)
}

// New creates a new {{ humanize .Name }} client
func New(transport runtime.ClientTransport, cfg *TransportConfig, formats strfmt.Registry) *{{ pascalize .Name }} {
  // ensure nullable parameters have default
  if formats == nil {
    formats = strfmt.Default
  }

  cli := new({{ pascalize .Name }})
  cli.cfg = cfg
  cli.Transport = transport
  cli.formats = formats
  {{- range .OperationGroups }}
  cli.{{ pascalize .Name }} = {{ .PackageAlias }}.New(transport, formats)
  {{- end }}
  return cli
}

// Clone creates a clone of the {{ humanize .Name }} client.
// This new client can then be modified independently of the original client with the With* methods.
func (c *GrafanaHTTPAPI) Clone() *GrafanaHTTPAPI {
	cfg := *c.cfg
	return New(c.Transport, &cfg, c.formats)
}

// DefaultTransportConfig creates a TransportConfig with the
// default settings taken from the meta section of the spec file.
func DefaultTransportConfig() *TransportConfig {
    return &TransportConfig {
        Host: DefaultHost,
        BasePath: DefaultBasePath,
        Schemes: DefaultSchemes,
    }
}

// TransportConfig contains the transport related info,
// found in the meta section of the spec file.
type TransportConfig struct {
    // Host is the doman name or IP address of the host that serves the API.
    Host string
    // BasePath is the URL prefix for all API paths, relative to the host root.
    BasePath string
    // Schemes are the transfer protocols used by the API (http or https).
    Schemes []string
    // APIKey is an optional API key or service account token.
    APIKey string
    // BasicAuth is optional basic auth credentials.
    BasicAuth *url.Userinfo
    // OrgID provides an optional organization ID.
    // OrgID is only supported with BasicAuth since API keys are already org-scoped.
    OrgID int64
    // TLSConfig provides an optional configuration for a TLS client
    TLSConfig *tls.Config
    // NumRetries contains the optional number of attempted retries
    NumRetries int
    // RetryTimeout sets an optional time to wait before retrying a request
    RetryTimeout time.Duration
    // RetryStatusCodes contains the optional list of status codes to retry
    // Use "x" as a wildcard for a single digit (default: [429, 5xx])
    RetryStatusCodes []string
    // HTTPHeaders contains an optional map of HTTP headers to add to each request
    HTTPHeaders map[string]string
    // Debug sets the optional debug mode for the transport
    Debug bool
    // Client sets the net/http client used for the transport
    Client *http.Client
}

// WithHost overrides the default host,
// provided by the meta section of the spec file.
func (cfg *TransportConfig) WithHost(host string) *TransportConfig {
    cfg.Host = host
    return cfg
}

// WithBasePath overrides the default basePath,
// provided by the meta section of the spec file.
func (cfg *TransportConfig) WithBasePath(basePath string) *TransportConfig {
    cfg.BasePath = basePath
    return cfg
}

// WithSchemes overrides the default schemes,
// provided by the meta section of the spec file.
func (cfg *TransportConfig) WithSchemes(schemes []string) *TransportConfig {
    cfg.Schemes = schemes
    return cfg
}

// {{ pascalize .Name }} is a client for {{ humanize .Name }}
type {{ pascalize .Name }} struct {
  {{ range .OperationGroups }}
    {{ pascalize .Name }} {{ .PackageAlias }}.ClientService
  {{ end }}
  Transport runtime.ClientTransport
  // cfg is private because it should only be read (for example, to get the OrgID) or set (and then the transport must be created again)
  cfg *TransportConfig
  formats strfmt.Registry
}

// SetTransport changes the transport on the client and all its subresources
func (c *{{pascalize .Name}}) SetTransport(transport runtime.ClientTransport) {
  c.Transport = transport
  {{- range .OperationGroups }}
  c.{{ pascalize .Name }}.SetTransport(transport)
  {{- end }}
}

// OrgID returns the organization ID that was set in the transport config
func (c *{{pascalize .Name}}) OrgID() int64 {
  return c.cfg.OrgID
}

// WithOrgID sets the organization ID and returns the client
func (c *{{pascalize .Name}}) WithOrgID(orgID int64) *{{pascalize .Name}} {
  c.cfg.OrgID = orgID
  c.SetTransport(newTransportWithConfig(c.cfg))
  return c
}

// WithRetries sets retry parameters and returns the client
func (c *{{pascalize .Name}}) WithRetries(numRetries int, retryTimeout time.Duration, retryStatusCodes ...string) *{{pascalize .Name}} {
	c.cfg.NumRetries = numRetries
	c.cfg.RetryTimeout = retryTimeout
	c.cfg.RetryStatusCodes = retryStatusCodes
	c.SetTransport(newTransportWithConfig(c.cfg))
	return c
}

// WithHTTPClient sets the used net/http client and returns the API client
func (c *{{pascalize .Name}}) WithHTTPClient(client *http.Client) *{{pascalize .Name}} {
	c.cfg.Client = client
	c.SetTransport(newTransportWithConfig(c.cfg))
	return c
}

func newTransportWithConfig(cfg *TransportConfig) *httptransport.Runtime {
	httpTransport := http.DefaultTransport.(*http.Transport)
	httpTransport.TLSClientConfig = cfg.TLSConfig

	retryableTransport := &transport.RetryableTransport{
		Transport: httpTransport,
    NumRetries: cfg.NumRetries,
    RetryTimeout: cfg.RetryTimeout,
    RetryStatusCodes: cfg.RetryStatusCodes,
    HTTPHeaders: cfg.HTTPHeaders,
	}

	tr := httptransport.NewWithClient(cfg.Host, cfg.BasePath, cfg.Schemes, cfg.Client)	
	tr.Transport = retryableTransport

	var auth []runtime.ClientAuthInfoWriter
	if cfg.BasicAuth != nil {
		pwd, _ := cfg.BasicAuth.Password()
		basicAuth := httptransport.BasicAuth(cfg.BasicAuth.Username(), pwd)
		auth = append(auth, basicAuth)
	}
	if cfg.OrgID != 0 {
		orgIDHeader := runtime.ClientAuthInfoWriterFunc(func(r runtime.ClientRequest, _ strfmt.Registry) error {
			return r.SetHeaderParam(OrgIDHeader, strconv.FormatInt(cfg.OrgID, 10))
		})
		auth = append(auth, orgIDHeader)
	}
	if cfg.APIKey != "" {
		APIKey := httptransport.BearerToken(cfg.APIKey)
		auth = append(auth, APIKey)
	}

	tr.DefaultAuthentication = httptransport.Compose(auth...)

	// The default runtime.JSONConsumer uses `json.Number` for numbers which is unwieldy to use.
	tr.Consumers[runtime.JSONMime] = runtime.ConsumerFunc(func(reader io.Reader, data interface{}) error {
		return json.NewDecoder(reader).Decode(data) 
	})

  tr.Debug = cfg.Debug

	return tr
}
