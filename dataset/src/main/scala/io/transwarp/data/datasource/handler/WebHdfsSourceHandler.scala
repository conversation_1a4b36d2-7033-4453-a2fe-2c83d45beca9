package io.transwarp.data.datasource.handler

import io.transwarp.aip.commons.dataset.{ConnectionHandlerFactory, SourceConfig}
import io.transwarp.aip.commons.dataset.hdfs.HdfsNodesChecker
import io.transwarp.data.datasource.connection.WebHdfsConnectionHandler
import org.apache.hadoop.fs.Path
class WebHdfsSourceHandler extends FileSystemSourceHandler {
  override val name = SourceConfig.WebHdfs
  override def connectionHandler: WebHdfsConnectionHandler = {
    val conn = config.connection
    ConnectionHandlerFactory.build(conn.category, conn)
      .asInstanceOf[WebHdfsConnectionHandler]
  }

  override def hdfsPath: Path = {
    val fs = connectionHandler.fs
    val uri = fs.getUri
    val path = HdfsNodesChecker.getWebHdfsUrl(uri.getHost, uri.getPort, $(file))
    new Path(path)
  }
  override def pathForSpark: String = {
    hdfsPath.toString
  }
}
