FROM ***********/transwarp/arm-seldon-base-image-treelite:branch-1.2
COPY . /app
WORKDIR /app

RUN pip uninstall setuptools -y && pip install setuptools && pip install cython && pip install scipy

RUN pip3 install -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com --no-cache-dir -r ./requirements_arm.txt
EXPOSE 5000

# Define environment variable
ENV MODEL_NAME Model
ENV API_TYPE REST
ENV SERVICE_TYPE MODEL
ENV PERSISTENCE 0
ENV LANG C.UTF-8

CMD exec seldon-core-microservice $MODEL_NAME --service-type $SERVICE_TYPE --persistence $PERSISTENCE

