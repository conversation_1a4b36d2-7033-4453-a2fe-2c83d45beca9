import os
import sys

import treelite
import treelite.sklearn
import treelite_runtime
from sklearn.ensemble import ExtraTreesClassifier as ExtraTreesC
from sklearn.ensemble import ExtraTreesRegressor as ExtraTreesR
from sklearn.ensemble import GradientBoostingClassifier as GradientBoosting<PERSON>
from sklearn.ensemble import GradientBoostingRegressor as GradientBoostingR
from sklearn.ensemble import IsolationForest
from sklearn.ensemble import RandomForestClassifier as RandomForestC
from sklearn.ensemble import RandomForestRegressor as RandomForestR
import joblib

EXTRA_PACKAGE_PATH = os.environ.get("EXTRA_PACKAGE_PATH", "/app")
sys.path.append(os.path.abspath(EXTRA_PACKAGE_PATH))

from base.base_model import BaseModel

local_mount_path = '/app/model'
PREDICTOR_ID = os.environ.get("PREDICTOR_ID", "???")
PREDICTIVE_UNIT_ID = os.environ.get("PREDICTIVE_UNIT_ID", "???")
IS_PROBA = os.environ.get("IS_PROBA", "false")


class Model(BaseModel):
    def __init__(self, pip_ini=[], packages=[]):
        super(Model, self).__init__(pip_ini, packages)
        self._model = None
        self.predictor = None
        self.model = None
        self._can_compile_model = False

    def load(self):
        self._model = joblib.load(self._model_path)
        if isinstance(self._model, (
                RandomForestR, ExtraTreesR, GradientBoostingR, GradientBoostingC, IsolationForest, RandomForestC,
                ExtraTreesC)):
            self._can_compile_model = True
            self.model = treelite.sklearn.import_model(self._model)
            toolchain = 'gcc'  # clang is faster than gcc ?
            self.model.export_lib(toolchain=toolchain, libpath='./model.so', verbose=True)
            self.predictor = treelite_runtime.Predictor('./model.so', verbose=True)

    def predict_raw(self, inference_request: dict):
        X = self.get_input(inference_request)
        meta = self.get_parameters(inference_request)

        if self._can_compile_model:
            dmat = treelite_runtime.DMatrix(X, dtype='float64')
            predict = self.predictor.predict(dmat)
        else:
            if (not meta == None) and (not meta.get("tags") == None):
                tags = meta.get("tags")
                if tags.get("IS_PROBA") == "true":
                    predict = self._model.predict_proba(X)
                elif tags.get("IS_PROBA") == "false":
                    predict = self._model.predict(X)
                elif IS_PROBA == "true":
                    predict = self._model.predict_proba(X)
                else:
                    predict = self._model.predict(X)
            elif IS_PROBA == "true":
                predict = self._model.predict_proba(X)
            else:
                predict = self._model.predict(X)
        return self.genOutput(predict)
