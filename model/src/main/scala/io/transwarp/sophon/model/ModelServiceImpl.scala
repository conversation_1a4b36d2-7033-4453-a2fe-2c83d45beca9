package io.transwarp.sophon.model

import java.io.{File, FileInputStream, FileOutputStream}
import java.util.UUID

import io.transwarp.aip.commons.json.JsonSer
import io.transwarp.sophon.api.json.model.AddVersionJson
import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.config.BasicConfig
import io.transwarp.sophon.exception.SophonUserException
import io.transwarp.sophon.model.entity.{ModelEntity, ModelVersionBuilder, ModelVersionEntity}
import io.transwarp.sophon.model.repo._
import io.transwarp.sophon.utils.{LengthCheckType, ProjectUtil}
import org.apache.commons.io.FileUtils
import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.{FileSystem, Path}
import org.jooq.DSLContext

import scala.collection.JavaConverters._

class ModelServiceImpl(config: BasicConfig,
                       create: DSLContext) extends ModelService {
  val repo = new ModelRepo(create)
  val versionRepo = new ModelVersionRepo(create)

  def prepareNewModelPath(): ModelPath = {
    ModelPath.getModelPath(
      config.k8sNfsDir,
      config.hdfsDir,
      UUID.randomUUID().toString
    )
  }

  override def getModels(projectKey: String): Seq[ModelEntity] = {
    repo.findByPid(projectKey)
  }

  override def deleteModel(model: ModelEntity): Unit = {
    deleteModel(model.id)
  }

  override def addModel(model: ModelEntity): ModelEntity = {
    if (model.name.trim.equals("")){
      throw new SophonUserException(
        MessageKey.MODEL_SAVE_PARAMS_ERROR, "name is null")
    }
    val modelWithSameName = repo
      .findByNameAndPid(model.name, model.pid)
    if (modelWithSameName != null) {
      throw new SophonUserException(
        MessageKey.MODEL_RESOURCE_ALREADY_EXIST, model.name)
    }
    repo.save(model)
  }

  override def getModel(id: String): ModelEntity = {
    val m = repo.findOne(id)
    if (m == null) {
      throw new Exception(s"model ${id} not found")
    }
    m
  }

  override def deleteModel(id: String): Unit = {
    repo.deleteById(id)
  }

  override def getVersions(modelId: String): Seq[ModelVersionEntity] = {
    versionRepo.findByModel(modelId)
  }

  override def findLatestVersion(modelId: String): ModelVersionEntity = {
    versionRepo.findLatestVersion(modelId)
  }

  override def deleteVersion(version: String): Unit = {
    versionRepo.deleteById(version)
  }

  override def getVersion(versionId: String): ModelVersionEntity = {
    versionRepo.findOne(versionId)
  }

  override def updateModel(model: ModelEntity): ModelEntity = {
    repo.update(model)
  }

  override def getModelByNameAndPid(name: String, pid: String): ModelEntity = {
    repo.findByNameAndPid(name, pid)
  }

  private def validateParams(request: AddVersionJson): Unit = {
    if (!request.useNewModel) {
      if (request.modelId == null){
        throw new SophonUserException(MessageKey.MODEL_SAVE_PARAMS_ID_ERROR)
      }
      if (getModel(request.modelId) == null) {
        throw new SophonUserException(MessageKey.MODEL_SAVE_HISTORY_NOTFOUND, request.modelName)
      }
    } else {
      if (request.pid == null || request.modelName == null) {
        throw new SophonUserException(MessageKey.MODEL_SAVE_PARAMS_ERROR)
      }
      ProjectUtil.lengthCheck(request.modelName, LengthCheckType.Name)
    }

    ProjectUtil.lengthCheck(request.name, LengthCheckType.Name)

    request.modelType match {
      case ModelTypes.pmml.name =>
        // nothing
      case ModelTypes.tensorflow.name =>
        if (request.nfsPath == null) {
          throw new Exception("path can't be null")
        }
      case ModelTypes.File.name =>
        if (request.nfsPath == null) {
          throw new Exception("path can't be null")
        }
      case ModelTypes.nativeSpark.name =>
        if (request.hdfsPath == null) {
          throw new Exception("path can't be null")
        }
      case _ =>
        // do nothing
    }
  }

  /**
   * 封装创建模型和版本的接口。
   * 如果模型不存在，则先创建模型
   * @param request
   * @return
   */
  override def addVersion(request: AddVersionJson): ModelVersionEntity = {
    validateParams(request)

    val modelEntity = if (!request.useNewModel) {
      getModel(request.modelId)
    } else { // add new model
      // create model
      val modelEntity = ModelEntity(
        UUID.randomUUID().toString,
        request.pid,
        request.modelName,
        System.currentTimeMillis(),
        System.currentTimeMillis(),
        Option(request.description)
      )

      addModel(modelEntity)
    }

    val extraInfo = Option(request.extraInfo).map(_.asScala.toMap).getOrElse(Map())
    val bundle = ModelBundle(request.modelType, Array(), request.bytes,
      request.hdfsPath, request.nfsPath, extraInfo)

    addVersion(
      modelEntity.id,
      request.name,
      request.description,
      bundle,
      Map(
        "jid" -> request.jid,
        "checkpoint" -> request.checkpoint,
        "codeId" -> request.codeId,
        "tfParams" -> JsonSer.toJson(request.tfParams)
      )
    )
  }

  override def addVersion(modelId: String,
                          name: String,
                          description: String,
                          bundle: ModelBundle,
                          extraInfo: Map[String, String] = Map()): ModelVersionEntity = {
    val addVersionJson = new AddVersionJson
    addVersionJson.useNewModel = false
    addVersionJson.modelId = modelId
    addVersionJson.name = name

    validateParams(addVersionJson)

    val entity = new ModelVersionBuilder(modelId, name, description)
        .withBundle(bundle)
        .addInfo(extraInfo)
        .build()
    saveVersion(entity)
  }

  override def getVersionNamesByPidAndModelName(pid: String, modelName: String): Seq[String] = {
    val model = repo.findByNameAndPid(modelName, pid)
    versionRepo.findVersionNamesByModelId(model.id)
  }

  private def saveVersion(versionEntity: ModelVersionEntity): ModelVersionEntity = {
    val versionWithSameName =
      versionRepo.findByNameAndModelId(versionEntity.name, versionEntity.modelId)
    if (versionWithSameName != null) {
      throw new SophonUserException(
        MessageKey.MODEL_VERSION_ALREADY_EXIST, versionEntity.name)
    }
    versionRepo.save(versionEntity)
  }

  override def getVersionFile(pid: String,
                              modelName: String,
                              versionName: String,
                              path: String): Unit = {
    throw new NotImplementedError("use bundle in the future")
  }

  override def saveVersion(version: ModelVersionEntity, localDir: String): Unit = {
    val dir = new File(localDir)
    dir.mkdirs()

    // save version info
    val modelJson = new File(localDir, "version")
    JsonSer.toJson(version, new FileOutputStream(modelJson))

    // save bundle
    val bundleDir = new File(localDir, "bundle")

    if (version.nfsPath != null) {
      val nfsPath = new File(version.nfsPath)
      if (nfsPath.exists()) {
        val nfsDir = new File(bundleDir, "nfs")
        nfsDir.mkdirs()
        FileUtils.copyFileToDirectory(nfsPath, nfsDir)
      }
    }

    if (version.hdfsPath != null) {
      val conf = new Configuration()
      val fs = FileSystem.get(conf)
      val hdfsPath = new Path(version.hdfsPath)
      if (fs.exists(hdfsPath)) {
        val localPath = new File(bundleDir, "hdfs")
        localPath.mkdirs()

        fs.copyToLocalFile(hdfsPath, new Path(s"${localDir}/bundle"))
        new File(bundleDir, hdfsPath.getName).renameTo(localPath)
      }
    }
  }

  override def loadVersion(localDir: String, relocatePath: Boolean = true): ModelVersionEntity = {
    val basedir = new File(localDir)
    val version = JsonSer.fromJson(
      new FileInputStream(new File(basedir, "version")),
      classOf[ModelVersionEntity])

    val nfs = new File(basedir, "bundle/nfs")
    val hdfs = new File(basedir, "bundle/hdfs")

    var finalNfs: String = null
    var finalHdfs: String = null

    if (relocatePath) {
      // move nfs and hdfs
      val path = prepareNewModelPath()
      if (nfs.exists()) {
        finalHdfs = path.hdfsPath
        FileUtils.copyDirectory(nfs, new File(path.nfsPath))
        finalNfs = s"${path.nfsPath}/${new File(version.nfsPath).getName}"
      }

      if (hdfs.exists()) {
        finalHdfs = path.hdfsPath
        val conf = new Configuration()
        val fs = FileSystem.get(conf)
        fs.mkdirs(new Path(path.hdfsPath))
        hdfs.listFiles().foreach(f => {
          fs.copyFromLocalFile(new Path(f.getAbsolutePath), new Path(path.hdfsPath))
        })
      }

    } else {
      if (nfs.exists()) {
        finalNfs = s"${nfs.getAbsolutePath}/${new File(version.nfsPath).getName}"
      }

      if (hdfs.exists()) {
        finalHdfs = s"file://${hdfs.getAbsolutePath}"
      }
    }

    version.copy(nfsPath = finalNfs, hdfsPath = finalHdfs)
  }
}

