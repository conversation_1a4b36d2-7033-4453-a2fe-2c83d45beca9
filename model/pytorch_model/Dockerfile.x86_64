FROM ***********/aip/seldon-base-image:branch-1.2
COPY . /app
WORKDIR /app

RUN pip3 install torch torchvision torchaudio -i http://mirrors.aliyun.com/pypi/simple/  --index-url https://download.pytorch.org/whl/cpu

EXPOSE 5000

# Define environment variable
ENV MODEL_NAME Model
ENV API_TYPE REST
ENV SERVICE_TYPE MODEL
ENV PERSISTENCE 0
ENV LANG C.UTF-8

CMD exec seldon-core-microservice $MODEL_NAME --service-type $SERVICE_TYPE --persistence $PERSISTENCE








