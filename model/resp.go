package model

import "time"

type ChainUserAudit struct {
	TotalIndices TotalIndices         `json:"total_indices"`
	DailyUser    map[int64]*DailyUser `json:"daily_users"`
}

type TotalIndices struct {
	ChatCount    int32 `json:"chat_count"`
	UserCount    int32 `json:"user_count"`
	NewUserCount int32 `json:"new_user_count"`
}

type DailyUser struct {
	Date               time.Time `json:"date"`                 //统计日期
	ActiveUsers        int32     `json:"active_users"`         //日活用户
	DailyNewUsers      int32     `json:"new_users"`            //日新增用户
	NewUserPercent     float64   `json:"new_user_percent"`     //新用户占比
	WeeklyActiveUsers  int32     `json:"weekly_active_users"`  //周活用户
	MonthlyActiveUsers int32     `json:"monthly_active_users"` //月活用户
	ChatCount          int64     `json:"chat_count"`           //总聊天数
	ChatCountPerUser   float64   `json:"chat_count_per_user"`  //用户平均聊天数
	RatingCount        int64     `json:"rating_count"`         //总点赞数
	UnRatingCount      int64     `json:"un_rating_count"`      // 总点踩数
}

type IsStreamAPI struct {
	IsStream       bool   `json:"is_stream"`
	HttpStatusCode int    `json:"http_status_code"`
	NonStreamResp  string `json:"non_stream_resp"`
}

type ServiceEvaluation struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type ServiceEvaluationHistory struct {
	Code     int      `json:"code"`
	Message  string   `json:"message"`
	Metadata Metadata `json:"metadata"`
}

type Metadata struct {
	Items []ZZItem `json:"items"`
}

type ZZItem struct {
	ModelName   string `json:"ModelName"`
	EvalDataset string `json:"EvalDataset"`
	CreateAt    string `json:"createAt"`
	Remark      string `json:"remark"`
}

type Item struct {
	ModelName   string `json:"model_name"`
	EvalDataset string `json:"eval_dataset"`
	CreateAt    string `json:"createAt"`
	Remark      string `json:"remark"`
}

type ServiceEvaluationHistoryResp struct {
	Items []Item `json:"items"`
}

type ServingCfgResp struct {
	EnableEvaluation bool `json:"enable_evaluation"`
}
