package io.transwarp.sophon.config

import java.util.concurrent.ConcurrentHashMap

import io.transwarp.aip.commons.basic.config._
import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.exception.SophonUserException

/**
  * define basic config for Sophon
  * for services who want to add their own configuration items,
  * create their own Config object that get config from here, like:
  *
  * class ServiceConfig {
  *     val basic: BasicConfig
  *     val config = basic.config("nested-config")
  * }
  */
class BasicConfig extends CommonConfig {
  import BasicConfig._
  private val _config = new ConcurrentHashMap[String, String]()

  def sophonHome: Option[String] = sys.env.get(SOPHON_HOME)
  def confHome: Option[String] = sys.env.get(SOPHON_CONF_DIR)
  def driverHome: Option[String] = sys.env.get(SOPHON_DRIVER_HOME)
  def sparkHome: Option[String] = sys.env.get(SPARK_HOME)

  def sophonHost: String = getString(SophonHost)

  def secret: String = getString(Secret)
  // change unit to millisecond
  def tokenTimeOut: Long = getLong(TokenTimeOut) * 60 * 1000

  def basedir: String = getString(Basedir)
  private def jdbcDriverDir: String = s"$k8sNfsDir/driver"
  def jdbcDriverJarsDir: String = s"$jdbcDriverDir/jars"
  def jdbcDriverConfigFile: String = s"$jdbcDriverDir/config/driver-info.json"
  def sophonSamplesDir: String = s"$hdfsDir/samples"
  def hdfsAssetDir: String = s"$hdfsDir/upload"
  def sampleDir: Option[String] = sophonHome.map(s => s + "/samples")
  def cacheHost: String = getString(CacheHost)
  def cachePort: Int = getInt(CachePort)
  def cachePassword: String = getString(CachePassword)
  def yarnClusterId: String = getString(YarnClusterId)

  def useCustomerPython: Boolean = getBoolean(UseCustomerPython)
  def pythonDir: String = getString(PythonDir)
  def pythonBinary: String = getString(PythonBinary)

  def cacheReUpload: Boolean = getBoolean(CacheReUpload)
  def cacheJars: Boolean = getBoolean(CacheJars)
  def cacheJarsDir: String = getString(CacheJarsDir)

  // k8s
  def k8sResource: String = getString(K8sResource)
  def k8sMountPath: String = getString(K8sMountPath)
  def socketPort: Int = getInt(SocketPort)
  def proxyApiIp: String = getString(ProxyApiIp)
  def proxyApiPort: Int = getInt(ProxyApiPort)
  def sophonPort: Int = getInt(SophonPort)

  def k8sDriverCustomSecrets: Map[String, String] =
    getPrefixedKeyValuePairs(K8S_Driver_CUSTOM_SECRETS_PREFIX)

  def k8sSessionCleanupTimeoutHours: Int = getInt(K8sSessionCleanupTimeoutHours)

  def licenseUrl: String = getString(LicenseUrl)
  def licenseType: String = getString(LicenseType)

  // k8s
  def sparkNamespace: String = getString(SparkNamespace)
  def sparkDeployMode: String = getString(SparkDeployMode)


  // HBase
  def hbaseNS: String = getString(HBaseNS)


  def tfEachImageSize: Int = getInt(TfImageBufferSizeOfEachPicture)

  // fake email info
  def emailAccount: String = getString(EmailAccount)
  def emailPass: String = getString(EmailPassword)
  def emailHost: String = getString(EmailHost)
  def emailPort: String = getString(EmailPort)
  def emailProtocol: String = getString(EmailProtocol)
  def emailTo: String = getString(EmailTo)
  def emailEnable: Boolean = getBoolean(EmailEnable)

  // master
  def sparkSchedulerMode: SparkSchedulerMode = {
    getString(MasterUrl) match {
      case master if master.startsWith("yarn") =>
        getString(DeployMode) match {
          case "client" =>
            SparkSchedulerMode.YARN_CLIENT
          case "cluster" =>
            SparkSchedulerMode.YARN_CLUSTER
          case _ =>
            throw new SophonUserException(
              MessageKey.OTHER_PARAM_INVALID, "livy.spark.deployMode")
        }
      case master if master.startsWith("k8s") =>
        getString(DeployMode) match {
          case "client" =>
            SparkSchedulerMode.K8S_CLIENT
          case "cluster" =>
            SparkSchedulerMode.K8S_CLUSTER
          case _ =>
            throw new SophonUserException(
              MessageKey.OTHER_PARAM_INVALID, "livy.spark.deployMode")
        }
      case master if master.startsWith("local") =>
        SparkSchedulerMode.LOCAL
      case _ => throw new SophonUserException(
        MessageKey.OTHER_PARAM_INVALID, "livy.spark.master")
    }
  }

  // dieu deep conf
  val hdfsURIPrefix: String = "hdfs://default"
  def addPvc: Boolean = getBoolean(AddPvc)

  def ps: Int = getInt(PsInstances)
  def worker: Int = getInt(WorkerInstances)
  def hostNetwork: Boolean = getBoolean(HostNetwork)
  def apiHostNetwork: Boolean = getBoolean(APIHostNetwork)
  def imagePullPolicy: String = getString(ImagePullPolicy)
  def timeout: Long = getInt(Timeout).toLong
  def jupyterNbConvertExtraCmd: String = getString(JupyterNbConvertExtraCmd)
  def tFimage: String = get(K8sTFImageName)
  def jupyterCpuImage: String = getString(JupyterCpuImageName)
  def jupyterGpuImage: String = getString(JupyterGpuImageName)
  def tBimage: String = getString(K8sTBImageName)
  def apiServiceImage: String = getString(ApiServiceImage)
  def apiServiceArmImage: String = getString(ApiServiceArmImage)
  def apiServiceReplicasLimit: Int = getInt(ApiServiceReplicasLimit)

  def notebookArmImage: String = getString(NotebookArmImage)

  def dockerRegistryUrl: String = getString(DockerRegistryUrl)

  // tdc
  def tdcSpaBasePath: String = getString(TdcSpaBasePath)
  def tdcVisitHost: String = getString(TdcHost)

  def personalStorage: Long = getLong(PersonalStorage)
  def k8sApiServer: String = getString(K8sApiServer)
  def k8sNameSpace: String = getString(K8sNameSpace)
  def k8sCniNet: String = getString(K8sCniNet)
  def k8sCaCertFile: String = getString(K8sCaCertFile)
  def k8sClientCertFile: String = getString(K8sClientCertFile)
  def k8sClientKeyFile: String = getString(K8sClientKeyFile)
  def k8sPipLocation: String = getString(K8sNotebookPipLocation)
  def k8sNotebookPythonVersion: String = getString(K8sNotebookPyspark)
  def k8sNotebookNoActivityTimeout: Int = getInt(K8sNotebookNoActivityTimeout)
  def k8sNotebookCullKernelTimeout: Int = getInt(K8sNotebookCullKernelTimeout)
  private def get(entry: Entry): String = {
    _config.getOrDefault(entry.key, entry.default)
  }

  def getSpaceSizeAsKb(entry: Entry): Long = {
    SpaceSizeStrValidator.parse(get(entry))
  }

  // streaming
  def streamingResultCacheSize: Int = getInt(StreamingResultCacheSize)
  def resultSizeInRedis: Int = getInt(ResultSizeInRedis)
  def streamingResultSamplePercent: Double = getString(ResultSamplePercent).toDouble
  def streamingJdbcCacheSize: Int = getInt(StreamingWrite2JdbcCacheSize)

  // data auth
  def dataDownloadable: Boolean = getBoolean(DataDownloadable)
  def dataPreviewLimit: Int = getInt(DataPreviewLimit)
  def disableSSLCheck: Boolean = getBoolean(DisableSSLCheck)
  def disableCasProxy: Boolean = getBoolean(DisableCasProxy)

  def testMode: Boolean = getBoolean(TestMode)

  // haproxy
  def haProxyEnable: Boolean = getBoolean(HaproxyEnable)
  def haProxyPrefix: String = getString(HaproxyPrefix)

  // kong
  def kongUseSubpath: Boolean = getBoolean(KongUseSubpath)

  def livySeverSessionTimeoutCheck: String = getString(LivyServerSessionTimeoutCheck)
  def livyServerSessionTimeoutStrategy: String = getString(LivyServerSessionTimeoutStrategy)
  def livySeverSessionTimeout: String = getString(LivyServerSessionTimeout)
  def sessionUserGroupRoot: String = getString(SessionUserGroupRoot)
  def execReadLocal: Boolean = getBoolean(ExecReadLocal)

  // deep graph image
  def deepGraphImageName: String = getString(DeepGraphImageName)

  def kafkaUesKerberos: Boolean = getBoolean(KafkaUesKerberos)
  def sophonKafkaKeytab: String = getString(SophonKafkaKeytab)
  def sophonKafkaPrinciple: String = getString(SophonKafkaPrinciple)
  def sophonJaasKeytab: String = getString(SophonJaasKeytab)

  // def sophonScheduler: String = getString(SophonScheduler)

  def kafkaSaslKerberosServiceName: String = getString(KafkaSaslKerberosServiceName)
  def kafkaSaslKerberosServicePrincipalInstance: String =
    getString(KafkaSaslKerberosServicePrincipalInstance)

  // stellardb
  def stellardbUesKerberos: Boolean = getBoolean(StellardbUesKerberos)
  def sophonStellardbKeytab: String = getString(SophonStellardbKeytab)
}

object BasicConfig {
  val PREFIX = "sophon"

  val SPARK_HOME = "SPARK_HOME"
  val SOPHON_HOME = "SOPHON_HOME"
  val SOPHON_CONF_DIR = "SOPHON_CONF_DIR"
  val SOPHON_DRIVER_HOME = "SOPHON_DRIVER_DIR"

  val SPA_BASE_PATH = "X-Spa-Base-Path"

  // nfs config
  // sophon host
  val SophonHost = Entry("sophon.host", null)

  // auth
  val Secret = Entry("sophon.auth.secret", null)
  // default to ten days
  val TokenTimeOut = Entry("sophon.token.timeout", "14400")
  // whether to upload jars to hdfs when server starts to speed up session

  val UseCustomerPython = Entry("sophon.use.customer.python", "false")
  val PythonDir = Entry("sophon.python.dir", "/usr/local/lib/python3.6")
  val PythonBinary = Entry("sophon.python.binary", "./python.zip/python3.6/bin/python3")
  val CacheReUpload = Entry("sophon.cache.reupload", "true")
  val CacheJars = Entry("sophon.cache.jars", "true")
  val CacheJarsDir = Entry("sophon.cache.jars.dir", "/tmp/.sophon2.6")

  val AuthLDAPHost = Entry("sophon.auth.ldap.host", null)
  val AuthLDAPPort = Entry("sophon.auth.ldap.port", null)
  val AuthLDAPDNInfo = Entry("sophon.auth.ldap.dninfo", null)
  val AuthLDAPAuthentication = Entry("sophon.auth.ldap.authentication", "simple")
  val AuthLDAPRoleAdmin = Entry("sophon.auth.ldap.role.admin", null)
  val KafkaUesKerberos = Entry("sophon.kafka.enable.kerberos", "false")
  val SophonKafkaKeytab = Entry("sophon.kafka.keytab", "/etc/keytabs/keytab")
  val SophonKafkaPrinciple = Entry("sophon.kafka.principle", "kafka/tos@TDH")
  val SophonJaasKeytab = Entry("sophon.jaas.keytab", "/etc/sophon/conf/base/session_jaas.keytab")
  // val SophonScheduler = Entry("spark.scheduler.allocation.file",
  //                            "/etc/sophon/conf/base/scheduler.xml")
  val AuthLoginUrl = Entry("sophon.auth.loginUrl", null)
  // filter configuration, it will define is token or cookie based auth method
  // and token based is default
  val AuthFilterClass = Entry("sophon.auth.filter.class",
    "io.transwarp.sophon.server.security.JWTSecurityConfig")

  // tdc cas auth config
  val CasServicePrefix = Entry("sophon.auth.cas.service.prefix", null)
  val AppServicePrefix = Entry("sophon.auth.app.service.prefix", null)

  // auth redis
  val SessionTimeout = Entry("sophon.auth.session.timeout", "2592000")

  val Basedir = Entry("sophon.basedir", "/home/<USER>/sophon")
  val CacheHost = Entry("sophon.cache.host", null)
  val CachePort = Entry("sophon.cache.port", "6379")
  val CachePassword = Entry("sophon.cache.password", "")
  val ProxyApiIp = Entry("sophon.proxy.api.ip", "localhost")
  val ProxyApiPort = Entry("sophon.proxy.api.port", "8083")
  val SocketPort = Entry("sophon.socket.port", "9099")

  val RequestTimeout = Entry("sophon.request.timeout", "1") // in minutes

  // license
  val LicenseUrl = Entry("sophon.license.url", null)
  val LicenseType = Entry("sophon.license.type", "sophon")
  // session resource, those are hard max limit, user config should not exceed this limit
  val YarnClusterId = Entry("sophon.session.yarn-cluster-id", "default")
  val DriverCores = Entry("spark.driver.cores", "1")
  val DriverMemory = Entry("spark.driver.memory", "2g")
  val ExecutorCores = Entry("spark.executor.cores", "1")
  val ExecutorMemory = Entry("spark.executor.memory", "2g")
  val ExecutorInstances = Entry("spark.executor.instances", "3")
  val YarnQueue = Entry("spark.yarn.queue", "default")

  // Currently, the value can be "lastActivity"(default) or "created"
  val LivyServerSessionTimeoutStrategy =
    Entry("livy.server.session.timeout.strategy", "lastActivity")
  val LivyServerSessionTimeoutCheck = Entry("livy.server.session.timeout-check", "true")
  val LivyServerSessionTimeout = Entry("livy.server.session.timeout", "8h")
  val ResourceConfigs = Array(
    BasicConfig.DriverCores,
    BasicConfig.DriverMemory,
    BasicConfig.ExecutorCores,
    BasicConfig.ExecutorInstances,
    BasicConfig.ExecutorMemory,
    BasicConfig.YarnQueue
  )

  val SparkNamespace = Entry("spark.kubernetes.namespace", "sophon")
  val SparkDeployMode = Entry("livy.spark.deployMode", "client")

  // history
  // in days, less than 0 if never timeout
  val HistoryTimeout = Entry("sophon.job.history.timeout", "-1")
  val JobTimeoutCheckInterval = Entry("sophon.job.timeout-check.interval", "10") // in seconds

  // api server
  val KongAdminUrl = Entry("sophon.kong.admin-url", null)
  val KongProxyUrl = Entry("sophon.kong.proxy-url", null)
  val KongUseSubpath = Entry("sophon.kong.proxy.use-subpath", "false")
  val KongAuth = Entry("sophon.kong.auth", null)
  val KongUsername = Entry("sophon.kong.username", null)
  val KongPassword = Entry("sophon.kong.password", null)

  // database
  val SophonPort = Entry("server.port", "8080")

  // pilot for Wuxi project
  val PilotUrl = Entry("sophon.pilot.url", null)

  // HBase
  val HBaseNS = Entry("sophon.hbase.namespace", "sophon")

  // fake email info
  val EmailAccount = Entry("sophon.email.account", "")
  val EmailPassword = Entry("sophon.email.password", "")
  val EmailHost = Entry("sophon.email.host", "")
  val EmailPort = Entry("sophon.email.port", "")
  val EmailProtocol = Entry("sophon.email.protocol", "")
  val EmailTo = Entry("sophon.email.to", "")
  val EmailEnable = Entry("sophon.email.enable", "false")

  // master
  val MasterUrl = Entry("livy.spark.master", null)
  val DeployMode = Entry("livy.spark.deploy-mode", "client")

  // k8s conf
  val AddPvc = Entry("sophon.k8s.auto.add.pvc", "false")
  val PsInstances = Entry("sophon.k8s.ps.instances", null)
  val WorkerInstances = Entry("sophon.k8s.worker.instances", null)
  val HostNetwork = Entry("sophon.k8s.host.network", "true")
  val APIHostNetwork = Entry("sophon.apiservice.k8s.host.network", "false")
  val ImagePullPolicy = Entry("sophon.k8s.image.pull.policy", "Always")
  val Timeout = Entry("sophon.k8s.running.timeout", "120000")
  val K8sMountPath = Entry("sophon.k8s.mount.path", null)
  val K8sResource = Entry("sophon.k8s.resource", "cpu:2,memory:2Gi")
  val JupyterCpuImageName = Entry("sophon.k8s.jupyter.cpu.image.name", null)
  val JupyterGpuImageName = Entry("sophon.k8s.jupyter.gpu.image.name", null)
  val JupyterNbConvertExtraCmd = Entry("sophon.jupyter.nbconvert.extra.cmd", null)
  val K8sTFImageName = Entry("sophon.k8s.tensorflow.image.name", null)
  val K8sApiServer = Entry("sophon.k8s.api.server", null)
  val K8sNameSpace = Entry("sophon.k8s.namespace", "default")
  val K8sTBImageName = Entry("sophon.k8s.tensorboard.image.name", null)
  val DockerRegistryUrl = Entry("sophon.k8s.docker.registry.url", "http://localhost:8081")

  val K8sCniNet = Entry("sophon.k8s.cni.networks", "overlay")
  val K8sCaCertFile = Entry("sophon.k8s.ca.cert.file", "/srv/kubernetes/ca.crt")
  val K8sClientCertFile = Entry("sophon.k8s.client.cert.file", "/srv/kubernetes/admin.pem")
  val K8sClientKeyFile = Entry("sophon.k8s.client.key.file", "/srv/kubernetes/admin-key.pem")
  val K8sNotebookPipLocation = Entry("sophon.k8s.nfs.pip.install.location",
    ".local/lib/python3.6/site-packages/")
  val K8sNotebookPyspark = Entry("sophon.k8s.notebook.python.version",
    "python3.6")
  val K8sNotebookNoActivityTimeout = Entry("sophon.k8s.notebook.noactivity.timeout", "3600")
  val K8sNotebookCullKernelTimeout = Entry("sophon.k8s.notebook.kernel.cull.timeout", "1800")
  val K8sSessionCleanupTimeoutHours = Entry("sophon.k8s.session.cleanup.timeout.hours", "24")
  val ApiServiceImage = Entry("sophon.apiservice.image.name", null)
  val ApiServiceArmImage = Entry("sophon.apiservice.arm.image.name", null)
  val ApiServiceReplicasLimit = Entry("sophon.apiservice.replicas.limit", "5")

  val NotebookArmImage = Entry("sophon.notebook.arm.image.name", null)

  val K8sNodeSelectorKey = Entry("sophon.k8s.nodeSelector.key", null)
  val K8sNodeSelectorValue = Entry("sophon.k8s.nodeSelector.value", null)

  // tdc
  val TdcSpaBasePath = Entry("sophon.tdc.spa.path", null)
  val TdcHost = Entry("sophon.tdc.visit.host", null)

  // image buffer
  val TfImageBufferSizeOfEachPicture = Entry("sophon.tf.image.buffer.size", "1024000")

  // tensorboard
  val tensorboardLiveTime = Entry("tensorboard.thread.live.time", "300")
  val tensorboardCheckInterval = Entry("tensorboard.thread.check.interval", "10")
  // thirdparty
  val codeZeppelin = Entry("sophon.thirdparty.zeppelin.url", null)

  val PersonalStorage = Entry("sophon.user.personal.storage", "10240")

  // streaming
  val StreamingResultCacheSize = Entry("sophon.streaming.result.cache.size", "5")
  val ResultSizeInRedis = Entry("sophon.streaming.result.size.in.redis", "100")
  val ResultSamplePercent = Entry("sophon.streaming.result.sample.percent", "0.1")
  val StreamingWrite2JdbcCacheSize = Entry("sophon.streaming.jdbc.cache.size", "1")

  // auth
  val DataDownloadable = Entry("sophon.data.downloadable", "true")
  val DataPreviewLimit = Entry("sophon.data.preview.limit", "50000")
  val DisableSSLCheck = Entry("sophon.auth.disable.ssl.check", "true")
  val DisableCasProxy = Entry("sophon.auth.disable.cas.proxy", "false")


  val TestMode = Entry("sophon.mode.test", "false")

  // for haproxy
  val HaproxyEnable = Entry("sophon.haproxy.enable", "false")
  val HaproxyPrefix = Entry("sophon.haproxy.prefix", "api")


  // PREFIX
  val K8S_Driver_CUSTOM_SECRETS_PREFIX = "sophon.k8s.driver.custom.secrets."


  // root of sophon groups
  val SessionUserGroupRoot = Entry("guardian.session.user.group.root", "sophonResource")
  // DANGER!
  // 针对安全hive/impala的临时解决方案
  val ExecReadLocal = Entry("sophon.exec.read.local", "false")

  val DeepGraphImageName = Entry("sophon.deepgraph.image.name", null)

  val KafkaSaslKerberosServiceName = Entry("kafka.sasl.kerberos.service.name", "kafka")
  val KafkaSaslKerberosServicePrincipalInstance =
    Entry("kafka.sasl.kerberos.service.principal.instance", null)

  // stellardb
  val StellardbUesKerberos = Entry("sophon.stellardb.enable.kerberos", "false")
  val SophonStellardbKeytab = Entry("sophon.stellardb.keytab", "/etc/keytabs/keytab")
}