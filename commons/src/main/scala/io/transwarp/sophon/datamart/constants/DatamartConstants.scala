package io.transwarp.sophon.datamart.constants

object DatamartConstants {
  val Datamart = "datamart"
  val ID = "id"
  val DatamartSourceId = "datamartSourceId"

  val Type = "type"
  val TypeFeature = "feature"
  val TypeTableField = "tableField"

  val Marker = "marker"
  val MarkerTag = "tag"
  val MarkerMetric = "metric"

  // dataset param
  val EntityId = "entityId"
  val DatamartUrl = "datamartUrl"
  val TagIds = "tagIds"
  val MetricIds = "metricIds"
  val TableFields = "tableFields"
  val DatasetConfigs = "datasetConfigs"
}
