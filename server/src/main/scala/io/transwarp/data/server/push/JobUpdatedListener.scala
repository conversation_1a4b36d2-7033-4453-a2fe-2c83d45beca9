package io.transwarp.data.server.push

import io.transwarp.aip.commons.messagebus.MessageListener
import io.transwarp.aip.commons.websocket.{Event, PushService}
import io.transwarp.data.config.BasicConfig

import org.springframework.stereotype.Component

@Component
class JobUpdatedListener(basic: BasicConfig, sse: PushService) extends MessageListener {
//  val historyManager = new HistoryManager(basic)
  override def onMessage(channel: String, msg: String): Unit = {
//    val jobKey = msg
//    val tokens = jobKey.split(":")
//    val jobType = tokens.head
//    val status = historyManager.getStatus(jobKey)
//    val pid = if (tokens.length > 1) {
//      tokens(1)
//    } else {
//      ""
//    }
//    val runId = if (tokens.length > 2) {
//      tokens(2)
//    } else {
//      ""
//    }
//    val meta = historyManager.getMeta(jobKey)
//    val user = meta.uid
//    jobType match {
//      case HistoryManager.GraphJobType =>
//        val json = new job.GraphJobUpdateJson()
//        val bpIdAndname = runId.split("&")
//        json.pid = pid
//        json.user = user
//        json.state = status
//        json.id = jobKey
//        json.bpId = bpIdAndname.head
//        json.blueprintName = bpIdAndname(1)
//        JobHandle.State.valueOf(status.state) match {
//          case JobHandle.State.SUCCEEDED | JobHandle.State.CANCELLED | JobHandle.State.FAILED =>
//            GraphJobCache.populatingCache.remove(jobKey)
//          case _ =>
//        }
//        sse.sendMsg(user, Event(Channels.job.name, json))
//      case HistoryManager.GraphAlgoJobType =>
//        val json = new job.GraphAlgoUpdateJson()
//        val branchIdAndAlgo = runId.split("&")
//        json.id = jobKey
//        json.branchId = branchIdAndAlgo.head
//        json.state = status
//        json.algos = branchIdAndAlgo(1).split("-")
//        JobHandle.State.valueOf(status.state) match {
//          case JobHandle.State.SUCCEEDED | JobHandle.State.CANCELLED | JobHandle.State.FAILED =>
//            GraphJobCache.algoCache.remove(jobKey)
//          case _ =>
//        }
//        sse.sendMsg(user, Event(Channels.graphAlgoJob.name, json))
//      case HistoryManager.AutoFEJobType =>
//        val json = new AutoFEJobUpdateJson()
//        val taskId = runId.split("&").head
//        json.id = jobKey
//        json.pid = pid
//        json.taskId = taskId
//        json.state = status
//
//        val keyForSavingFlow = historyManager.key(pid, taskId,
//          HistoryManager.AutoFEJobType).toString
//        val prevFlow = historyManager.getTmpFlow(keyForSavingFlow)
//        val report = historyManager.getAutoFEResultReport(keyForSavingFlow)
//
//        JobHandle.State.valueOf(status.state) match {
//          case JobHandle.State.SUCCEEDED =>
//            report.stage match {
//              case AutoFEJob.AutoCleanStage =>
//                report.AutoCleanReport.EndTime = System.currentTimeMillis()
//              case AutoFEJob.AutoExpandStage =>
//                report.AutoExpandReport.EndTime = System.currentTimeMillis()
//              case AutoFEJob.AutoFCStage =>
//                report.AutoFCReport.EndTime = System.currentTimeMillis()
//              case _ =>
//            }
//            historyManager.savePreviousFlow(keyForSavingFlow, prevFlow)
//            historyManager.saveAutoFEResultReport(keyForSavingFlow, report)
//          case _ =>
//        }
//        sse.sendMsg(user, Event(Channels.autofe.name, json))
//      case _ =>
//        val json = new job.JobUpdateJson()
//        json.pid = pid
//        json.state = status
//        json.id = jobKey
//        json.path = meta.path
//        sse.sendMsg(user, Event(Channels.job.name, json))
//    }
  }
}
