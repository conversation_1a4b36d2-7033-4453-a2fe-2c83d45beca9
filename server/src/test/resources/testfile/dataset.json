{"id": "0db2c406-8f23-4795-9e94-d952921a01fe", "name": "gf_icc", "createTimestamp": 0, "modifyTimestamp": 1545807568000, "desc": "", "sourceType": "hdfs", "params": {"file": "/sophon/samples/gf_icc.csv"}, "connection": {"id": "sample", "name": "sample", "createTimestamp": 0, "modifyTimestamp": 0, "category": "hdfs", "params": {"basePath": "/sophon/samples"}, "pid": null, "shareable": false}, "schemaConfig": {"columns": [{"name": "p1", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "in_date", "role": "feature", "type": "string", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_degree", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_net_size", "role": "feature", "type": "int", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_rel_degree", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_avg_nei_degree", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_pgrank", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_max_nei_pgrank", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}, {"name": "icc_percentage", "role": "feature", "type": "double", "meaning": "text", "ignore": false, "values": [], "message": "", "hide": false}]}, "format": {"format": "csv", "params": {"encoding": "UTF-8", "inferSchema": "true", "header": "false", "delimiter": ","}}, "pid": "62ac45b5-76e6-4b0c-85ed-660c41294e5c"}