package io.transwarp.sophon.server.oprec

import io.transwarp.sophon.api.json.flow.OperatorJson
import io.transwarp.sophon.api.json.oprec.RecRuleJson
import io.transwarp.sophon.operator.OperatorRegistry
import org.apache.spark.sql.types._
import org.scalatest.{BeforeAndAfterAll, FunSuite}

import scala.collection.JavaConverters._
import scala.collection.mutable

class RuleBasedOpRecommenderTest extends FunSuite with BeforeAndAfterAll{

  import RuleBasedOpRecommenderTest._

  test("Keys in RecRules are valid.") {
    recRules.foreach {
      case (stage, recRule) =>
        assert(recRule.ops!=null && recRule.info!=null && recRule.feedBack!=null,
          s"Stage $stage is lacking keys.")
      case _ => throw new Exception("Invalid rules.")
    }
  }

  test("Stages in RecRules are valid") {
    val stagesAll = allStages()
    val stagesInRec = allStagesInRec()
    assert(stagesInRec.subsetOf(stagesAll), s"Got invalid stage " +
      s"${stagesInRec.diff(stagesAll).mkString(", ")} in operator recommendation rules.")
  }

  test("Operators in rules are valid.") {
    val opAll = allOps()
    val opInRules = allOpInRules()
    assert(opInRules.subsetOf(opAll), s"Got invalid operators " +
      s"${opInRules.diff(opAll).mkString(", ")} in operator recommendation rules.")
  }

  test("Info in rules contains all supported languages.") {
    recRules.foreach { case (stage, recRule) =>
      assert(RuleBasedOpRecommender.supportedLanguages.toSet == recRule.info.keySet().asScala,
        s"Languages in $stage info are ${recRule.info.keySet().asScala} while we should " +
          s"support ${RuleBasedOpRecommender.supportedLanguages.toSet}")
    }
  }
}

object RuleBasedOpRecommenderTest {
  val recRules: Map[String, RecRuleJson] = RuleBasedOpRecommender.rulesJson.recRules.asScala.toMap

  def allOpInRules(): Set[String] = {
    val opSet = mutable.Set[String]()
    recRules.foreach { case (_, recRule) =>
      recRule.ops.foreach(opSet.add)
    }
    opSet.toSet
  }

  def allOps(): Set[String] = {
    OperatorRegistry.clear()
    OperatorRegistry.load()
    OperatorRegistry.getOpKeys().toSet
  }

  def allStages(): Set[String] = {
    recRules.keySet
  }

  def allStagesInRec(): Set[String] = {
    recRules.flatMap { case (_, recRule) =>
      recRule.feedBack.keySet.asScala
    }.toSet
  }

  def generateOpJson(opNames: Array[String]): Array[OperatorJson] = {
    val opAll = allOps()
    assert(opNames.toSet.subsetOf(opAll), s"Got invalid operators" +
      s" ${opNames.toSet.diff(opAll).mkString(", ")} in operator recommendation rules."
    )
    opNames.map { opName =>
      val opJson = new OperatorJson()
      opJson.name = opName
      opJson
    }
  }

  def generateSchema(
    withNumeric: Boolean = true,
    withString: Boolean = true,
    withDate: Boolean = true,
    withLabel: Boolean = true,
    LabelType: DataType = StringType): StructType = {

    assert(withNumeric || withString,
      "Schema should be with at least one column of String or Numerical type")

    val fieldMetas = mutable.ArrayBuffer[StructField]()

    if (withNumeric) {
      fieldMetas append StructField(
        "numericalCol", DoubleType, false,
        new MetadataBuilder().putString("role", "feature").build())
    }

    if (withString) {
      fieldMetas append StructField(
        "StringCol", StringType, false,
        new MetadataBuilder().putString("role", "feature").build())
    }

    if (withString) {
      fieldMetas append StructField(
        "DateCol", DateType, false,
        new MetadataBuilder().putString("role", "feature").build())
    }

    if (withLabel) {
      fieldMetas append StructField(
        "LabelCol", LabelType, false,
        new MetadataBuilder().putString("role", "label").build())
    }

    StructType(fieldMetas.toArray)
  }

}
