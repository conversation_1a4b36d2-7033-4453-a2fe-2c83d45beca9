package io.transwarp.sophon.server.api

import io.transwarp.sophon.api.json.project.ProjectJson
import io.transwarp.sophon.api.json.sheet.ChartJson
import io.transwarp.sophon.server.BootConfig
import junit.framework.TestSuite
import org.junit._
import org.junit.runner.RunWith
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestPropertySource
import org.springframework.test.context.junit4.SpringRunner


@RunWith(classOf[SpringRunner])
@SpringBootTest(classes = Array(classOf[BootConfig]),
  webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = Array("classpath:application.properties"))
class ChartControllerTest extends SophonTestSuite {
  import io.transwarp.sophon.server.api.ChartControllerTest._

  @Before
  override def login: Unit = {
    super.login
    if (project==null){
      project = createProject
    }

    if (chart==null){
      chart = createChart
    }
  }


  @Test
  def addChart: Unit = {
    val chart = new ChartJson
    chart.name = "chart"
    chart.pid = project.id
    val result = sophonHttpClient.chart.addChart(chart)
    Assert.assertEquals("chart", result.name)
  }

  @Test
  def getCharts: Unit = {
    val productId = project.id
    val result = sophonHttpClient.chart.getCharts(productId)
    Assert.assertTrue(result.size>0)
  }
//  @Test
//  def getChartsByDataSet: Unit = {
//    val dataSetId = ChartControllerTest.chart.dataSetId
//    val productId = ChartControllerTest.project.id
//    val result = chartApiClient.getChartsByDataSet(productId, dataSetId)
//  }


  @Test
  def getChart: Unit = {
    val chartName = chart.name
    val chartId = chart.id
    val productId = project.id
    val result = sophonHttpClient.chart.getChart(productId, chartId)
    Assert.assertEquals(chartName, result.name)
  }

  @Test
  def deleteChart: Unit = {
    val chartId = chart.id
    val productId = project.id
    sophonHttpClient.chart.deleteChart(productId, chartId)
    chart = null
  }
  @Test
  def updateChart: Unit = {
    val newchart = chart
    newchart.name = "NewChart"
    val result = sophonHttpClient.chart.updateChart(newchart)
    Assert.assertEquals("NewChart", result.name)
  }

  def createChart: ChartJson = {
    val chart = new ChartJson
    chart.name = "ChartOneChart"
    chart.pid = project.id
    // chart.dataSetId = "1"
    sophonHttpClient.chart.addChart(chart)
  }

}




object ChartControllerTest extends TestSuite{
  var project: ProjectJson = null
  var chart: ChartJson = null
}



