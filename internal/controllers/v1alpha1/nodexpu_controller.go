package v1alpha1

import (
	"context"
	"fmt"
	"sort"
	"time"

	"k8s.io/apimachinery/pkg/api/equality"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"

	twresourcesv1alpha1 "transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"
	"transwarp.io/applied-ai/kube-nodexpu-manager/pkg/common"
	contextLogger "transwarp.io/applied-ai/kube-nodexpu-manager/pkg/context-logger"
	"transwarp.io/applied-ai/kube-nodexpu-manager/pkg/device"
	"transwarp.io/applied-ai/kube-nodexpu-manager/pkg/localcache"
	"transwarp.io/applied-ai/kube-nodexpu-manager/pkg/models"
	"transwarp.io/applied-ai/kube-nodexpu-manager/pkg/options"
	nodexpuutils "transwarp.io/applied-ai/kube-nodexpu-manager/pkg/utils/node-xpu-utils"
	podutils "transwarp.io/applied-ai/kube-nodexpu-manager/pkg/utils/pod-utils"
)

const (
	// TODO: use command flag
	UPDATE_NODE_XPU_STATUS_DURATION = time.Second * 60
	NODEXPU_CONTROLLE_NAME          = "nodexpu-controller"
)

// NodeXpuReconciler reconcile NodeXpu Object with devices on node.
// This controller is not added to the provided manager, and must thus be started manually.
// TODO: refactor using controller-runtime/pkg/controller/controller-utils, use CreateOrUpdate to init NodeXpu
// if result is OperationResultCreated, requeue and use CreateOrPatch to update status
// add owner refer to nodexpu
type NodeXpuReconciler struct {
	mgr    ctrl.Manager
	logger *contextLogger.ContextLogger

	Opts                 *options.Options
	PodCache             *localcache.LocalXpuPodCache
	NodeXpuEventRecorder nodexpuutils.NodeXpuEventRecorder
}

func (r *NodeXpuReconciler) SetupWithManager(mgr ctrl.Manager, logger *contextLogger.ContextLogger) error {
	r.mgr = mgr
	r.logger = logger.WithValues("type", "controller", "kind", "nodexpu")
	return nil
}

// +kubebuilder:rbac:groups=resources.transwarp.io,resources=nodexpus,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=resources.transwarp.io,resources=nodexpus/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=resources.transwarp.io,resources=nodexpus/finalizers,verbs=update

// Start to create or sync nodexpus object and start a coroutine to update statuses
func (r *NodeXpuReconciler) Start(ctx context.Context) error {
	r.logger.Info("Start node xpus controller")
	if err := r.SyncNodeXpus(ctx); err != nil {
		return err
	}
	// TODO: SyncNodeXpus at node events(?)

	r.logger.Info("Start watching to update xpus statuses")

	go func() {
		// Block until our controller manager is elected leader. We presume our
		// entire process will terminate if we lose leadership, so we don't need
		// to handle that.
		r.logger.Info("Waiting to acquire lease")
		<-r.mgr.Elected()
		r.logger.Info("Successfully acquired lease")

		// Sharing opts.Background context with mgr.
		wait.Until(func() { r.UpdateNodeXpuStatuses(ctx) }, UPDATE_NODE_XPU_STATUS_DURATION, ctx.Done())
	}()

	r.logger.Info("Start nodexpu controller successfully")
	return nil
}

func (r *NodeXpuReconciler) SyncNodeXpus(ctx context.Context) error {
	r.logger.Info("Syncing all NodeXpus in Cluster")
	nodes, err := r.Opts.NodeInformer.Lister().List(labels.Everything())
	if err != nil {
		return fmt.Errorf("list all nodes failed: %v", err)
	}
	for _, n := range nodes {
		nodeLogger := r.logger.WithNode(n)
		nodeLogger.Info("Syncing all NodeXpus on node")
		nodeAnnos := n.Annotations
		// It's impossible to know what type of device is registered on the node, so try all vendors and types
		for _, vendorDev := range device.Devices {
			for _, typeDev := range vendorDev.TypeDevices() {
				// Try to extract register annotation
				typeDevLogger := r.logger.WithVendorType(typeDev.VendorType())
				registerAnno, exists := typeDev.ExtractNodeRegisterAnno(nodeAnnos)
				if !exists {
					continue
				}
				typeDevLogger.Debug("Node registered device annotation", "registerAnnotation", registerAnno)
				devInfos, err := typeDev.GetNodeDevices(registerAnno)
				if err != nil {
					return fmt.Errorf("decode node registered device failed: %v", err)
				}
				typeDevLogger.Debug("Parse node register annotation successfully")

				// Try to create or update NodeXpu resource according to devInfo
				for _, info := range devInfos {
					if err := r.syncNodeGPU(ctx, n.Name, vendorDev.Vendor(), info); err != nil {
						return fmt.Errorf("sync NodeXpu resource failed: %v", err)
					}
				}
			}
		}
	}
	return nil
}

// syncNodeGPU syncs the device information to nodeGPU,
// it will try to create nodeGPU if not exists, otherwise update it.
// This function should only be called in initiating progress once
func (r *NodeXpuReconciler) syncNodeGPU(ctx context.Context, nodeName, vendor string, device *models.DeviceInfo) error {
	name := nodexpuutils.GetName(nodeName, device)
	nodeXpuLogger := r.logger.WithNodeXpuName(name)
	nodeXpuLogger.Info("Syncing NodeXpu with device info",
		contextLogger.LOG_KEY_NODE_NAME, nodeName,
		"count", device.Count,
		"devcore", device.Devcore,
		"devmem(MB)", device.Devmem/common.MB,
		"devmem(MiB)", device.Devmem/common.MiB,
	)

	var currentNodeXpu *twresourcesv1alpha1.NodeXpu
	var err error
	// Try to get NodeXpu by name. If not found, the retruned nodeXpu is an empty object not nil, so use a `found` flag
	found := true
	currentNodeXpu, err = r.Opts.ClusterClientSets.VersionedClient().ResourcesV1alpha1().NodeXpus().Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return fmt.Errorf("get nodeXpu failed: %v", err)
		}
		found = false
	}

	// Warning: Here define the expected Labels/Annotations/Spec
	// If added new content, make sure to sync it.
	nodeXpu := &twresourcesv1alpha1.NodeXpu{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
			Labels: map[string]string{
				nodexpuutils.NODE_XPU_NODE_LABEL: nodeName,
				nodexpuutils.NODE_XPU_UUID_LABEL: device.ID,
			},
		},
		Spec: twresourcesv1alpha1.NodeXpuSpec{
			NodeName: nodeName,
			XpuResource: twresourcesv1alpha1.XpuResource{
				Count:   *nodexpuutils.NewCountQuantity(device.Count),
				Devcore: *nodexpuutils.NewDevcoreQuantity(device.Devcore),
				Devmem:  *nodexpuutils.NewDevmemQuantity(device.Devmem),
			},
			ID:     device.ID,
			Index:  device.Index,
			Type:   device.Type,
			Numa:   device.Numa, // FIXME: no spec.numa field in nodexpu object
			Mode:   device.Mode,
			Vendor: vendor,
		},
	}
	nodeXpuLogger.Debug("Construct the latest NodeXpu object from node annotation successfully")

	if !found {
		// Create NodeXpu if not exists
		nodeXpuLogger.Info("Not found NodeXpu object. Trying to create a new NodeXpu.")
		if currentNodeXpu, err = r.Opts.ClusterClientSets.VersionedClient().ResourcesV1alpha1().NodeXpus().Create(ctx, nodeXpu, metav1.CreateOptions{}); err != nil {
			r.NodeXpuEventRecorder.CreateFail(nodeXpu)
			return fmt.Errorf("create NodeXpu failed: %v", err)
		}
		nodeXpuLogger.Info("Create NodeXpu object successfully")
		r.NodeXpuEventRecorder.Create(currentNodeXpu)
	} else {
		shouldSync := false
		// Check if NodeXpu Spec has changed
		if !equality.Semantic.DeepEqual(nodeXpu.Spec, currentNodeXpu.Spec) {
			nodeXpuLogger.Info("NodeXpu spec has changed. Try to update", "oldSpec", currentNodeXpu.Spec, "newSpec", nodeXpu.Spec)
			currentNodeXpu.Spec = nodeXpu.Spec
			shouldSync = true
		}
		// Check if NodeXpu Labels has changed. DON'T replace the whole Labels map, because it will remove other labels.
		if labelNodeName, exists := currentNodeXpu.Labels[nodexpuutils.NODE_XPU_NODE_LABEL]; !exists || labelNodeName != nodeXpu.Spec.NodeName {
			nodeXpuLogger.Info("NodeXpu node label has changed. Try to update",
				"label", nodexpuutils.NODE_XPU_NODE_LABEL,
				"oldValue", currentNodeXpu.Labels[nodexpuutils.NODE_XPU_NODE_LABEL],
				"newValue", nodeXpu.Spec.NodeName,
			)
			currentNodeXpu.Labels[nodexpuutils.NODE_XPU_NODE_LABEL] = nodeXpu.Spec.NodeName
			shouldSync = true
		}
		if labelXpuUUID, exists := currentNodeXpu.Labels[nodexpuutils.NODE_XPU_UUID_LABEL]; !exists || labelXpuUUID != nodeXpu.Spec.ID {
			nodeXpuLogger.Info("NodeXpu UUID label has changed. Try to update",
				"label", nodexpuutils.NODE_XPU_UUID_LABEL,
				"oldValue", currentNodeXpu.Labels[nodexpuutils.NODE_XPU_UUID_LABEL],
				"newValue", nodeXpu.Spec.ID,
			)
			currentNodeXpu.Labels[nodexpuutils.NODE_XPU_UUID_LABEL] = nodeXpu.Spec.ID
			shouldSync = true
		}

		if shouldSync {
			// TODO: use retryOnConflict to update
			if currentNodeXpu, err = r.Opts.ClusterClientSets.VersionedClient().ResourcesV1alpha1().NodeXpus().Update(ctx, currentNodeXpu, metav1.UpdateOptions{}); err != nil {
				r.NodeXpuEventRecorder.SyncFail(currentNodeXpu)
				return fmt.Errorf("update NodeXpu spec failed: %v", err)
			}
			r.NodeXpuEventRecorder.Sync(currentNodeXpu)
			nodeXpuLogger.Info("Update NodeXpu spec successfully")
		}
	}

	// Synchronize status with the latest version of nodexpu
	nodeXpuLogger.Debug("Try to synchronize NodeXpu status")
	if currentNodeXpu, err = r.updateNodeXpuStatus(ctx, currentNodeXpu); err != nil && !errors.IsNotFound(err) {
		r.NodeXpuEventRecorder.UpdateFail(currentNodeXpu)
		return fmt.Errorf("update nodexpu status failed: %v", err)
	}
	nodeXpuLogger.Info("Update NodeXpu status successfully")
	return nil
}

func (r *NodeXpuReconciler) UpdateNodeXpuStatuses(ctx context.Context) {
	r.logger.Info("Start updating all NodeXpus status")
	nodeXpus, err := r.Opts.NodeXpuInformer.Lister().List(labels.Everything())
	if err != nil {
		r.logger.Error(err, "Update node xpu statuses failed: list all nodexpus failed")
		return
	}

	// TODO: might optimize with coroutine
	for _, nodeXpu := range nodeXpus {
		if updatedNodeXpu, err := r.updateNodeXpuStatus(ctx, nodeXpu); err != nil {
			r.NodeXpuEventRecorder.UpdateFail(updatedNodeXpu)
			r.logger.Error(err, "Periodically update NodeXpus status failed")
		}
	}
}

// TODO: function is too big
// UpdateNodeGPUStatus update nodeGPU status
// includes current allocated resources, pods info and enable mode
func (r *NodeXpuReconciler) updateNodeXpuStatus(updateStatusCtx context.Context, nodeXpu *twresourcesv1alpha1.NodeXpu) (*twresourcesv1alpha1.NodeXpu, error) {
	nodeXpuLogger := r.logger.WithNodeXpu(nodeXpu)
	nodeXpuLogger.Debug("Updating NodeXpu status")
	podInfoMap, exists := r.PodCache.GetPodInfoMapWithXpu(nodeXpu.Spec.ID)
	if !exists {
		// initial status
		nodeXpuStatus := &twresourcesv1alpha1.NodeXpuStatus{
			Count: twresourcesv1alpha1.ResourceInfo{
				Capacity:    nodeXpu.Spec.Count,
				Usage:       *nodexpuutils.NewCountQuantity(0),
				Allocatable: nodeXpu.Spec.Count,
			},
			Devmem: twresourcesv1alpha1.ResourceInfo{
				Capacity:    nodeXpu.Spec.Devmem,
				Usage:       *nodexpuutils.NewDevmemQuantity(0),
				Allocatable: nodeXpu.Spec.Devmem,
			},
			Devcore: twresourcesv1alpha1.ResourceInfo{
				Capacity:    nodeXpu.Spec.Devcore,
				Usage:       *nodexpuutils.NewDevcoreQuantity(0),
				Allocatable: nodeXpu.Spec.Devcore,
			},
			NamespacedPods: make(map[string][]twresourcesv1alpha1.PodUsage),
		}
		nodeXpuLogger.Info("Not found podInfoMap. This means no pod allocated this xpu. Update NodeXpu with empty status")
		return r.tryUpdateStatus(updateStatusCtx, nodeXpuLogger, nodeXpu, nodeXpuStatus)
	}

	// TOOD: upgrade only if data is dirty
	var xpuTotalUsedcount int64 = 0
	var xpuTotalUsedmem int64 = 0 // Bytes
	var xpuTotalUsedcores int64 = 0

	namespacePod := make(map[string][]twresourcesv1alpha1.PodUsage)
	for _, p := range podInfoMap {
		pod, err := r.Opts.PodInformer.Lister().Pods(p.Namespace).Get(p.Name)
		if err != nil {
			// TODO: currently clean LocalPodCache while updating nodexpu status. This can be extracted as a goroutine and start when new a cache
			if errors.IsNotFound(err) {
				r.PodCache.DeletePod(pod)
				continue
			}
			return &twresourcesv1alpha1.NodeXpu{}, fmt.Errorf("get pod failed: %v", err)
		}
		if podutils.IsTerminated(pod) {
			continue
		}
		// Calculate total resource usage of pod
		var usedcount int64 = 0
		var usedmem int64 = 0 // Bytes
		var usedcores int64 = 0
		for _, podDevs := range p.Devices {
			for _, cntDevs := range podDevs {
				for _, dev := range cntDevs {
					// Filter current xpu
					if dev.UUID != nodeXpu.Spec.ID {
						continue
					}
					usedcount++
					usedmem += dev.Usedmem
					usedcores += dev.Usedcores
				}
			}
		}
		// Construct and insert PodUsage
		usage := twresourcesv1alpha1.PodUsage{
			PodMetadta: twresourcesv1alpha1.PodMetadta{
				Name:      p.Name,
				Namespace: p.Namespace,
			},
			XpuResource: twresourcesv1alpha1.XpuResource{
				Count:   *nodexpuutils.NewCountQuantity(usedcount),
				Devcore: *nodexpuutils.NewDevcoreQuantity(usedcores),
				Devmem:  *nodexpuutils.NewDevmemQuantity(usedmem),
			},
		}
		if _, exists := namespacePod[p.Namespace]; !exists {
			namespacePod[p.Namespace] = make([]twresourcesv1alpha1.PodUsage, 0, 1)
		}
		namespacePod[p.Namespace] = append(namespacePod[p.Namespace], usage)

		xpuTotalUsedcount += usedcount
		xpuTotalUsedcores += usedcores
		xpuTotalUsedmem += usedmem
	}
	nodeXpuLogger.Debug("Calculate total xpu usage",
		"totalUsedCount", xpuTotalUsedcount,
		"totalUsedDevcores", xpuTotalUsedcores,
		"totalUsedDevmem(MB)", xpuTotalUsedmem/common.MB,
		"totalUsedDevmem(MiB)", xpuTotalUsedmem/common.MiB,
	)

	countUsage := *nodexpuutils.NewCountQuantity(xpuTotalUsedcount)
	countAllocatable := *nodexpuutils.NewCountQuantity(nodeXpu.Spec.Count.Value() - countUsage.Value())

	devmemUsage := *nodexpuutils.NewDevmemQuantity(xpuTotalUsedmem)
	devmemAllocatable := *nodexpuutils.NewDevmemQuantity(nodeXpu.Spec.Devmem.Value() - devmemUsage.Value())

	devcoresUsage := *nodexpuutils.NewDevcoreQuantity(xpuTotalUsedcores)
	devcoresAllocatable := *nodexpuutils.NewDevcoreQuantity(nodeXpu.Spec.Devcore.Value() - devcoresUsage.Value())

	// Sort namespacePod with pod name, becuase pod usage is calcuate from cache's map which doesn't guarantee of order
	for ns := range namespacePod {
		sort.Slice(namespacePod[ns], func(i, j int) bool {
			return namespacePod[ns][i].Name < namespacePod[ns][j].Name
		})
	}

	nodeXpuStatus := &twresourcesv1alpha1.NodeXpuStatus{
		Count: twresourcesv1alpha1.ResourceInfo{
			Capacity:    nodeXpu.Spec.Count,
			Usage:       countUsage,
			Allocatable: countAllocatable,
		},
		Devmem: twresourcesv1alpha1.ResourceInfo{
			Capacity:    nodeXpu.Spec.Devmem,
			Usage:       devmemUsage,
			Allocatable: devmemAllocatable,
		},
		Devcore: twresourcesv1alpha1.ResourceInfo{
			Capacity:    nodeXpu.Spec.Devcore,
			Usage:       devcoresUsage,
			Allocatable: devcoresAllocatable,
		},
		NamespacedPods: namespacePod,
	}

	nodeXpuLogger.Debug("Get old nodexpu status", "status", nodeXpu.Status)
	nodeXpuLogger.Debug("Calculate new nodexpu status", "status", *nodeXpuStatus)

	return r.tryUpdateStatus(updateStatusCtx, nodeXpuLogger, nodeXpu, nodeXpuStatus)
}

// tryUpdateStatus will update status only when nodeXpuStatus is semantic deepequal to nodeXpu.Status
func (r *NodeXpuReconciler) tryUpdateStatus(ctx context.Context, nodeXpuLogger *contextLogger.ContextLogger, nodeXpu *twresourcesv1alpha1.NodeXpu, nodeXpuStatus *twresourcesv1alpha1.NodeXpuStatus) (*twresourcesv1alpha1.NodeXpu, error) {
	if equality.Semantic.DeepEqual(nodeXpuStatus, &nodeXpu.Status) {
		// No need to update
		nodeXpuLogger.Debug("Status is up-to-date. No need to update")
		return &twresourcesv1alpha1.NodeXpu{}, nil
	}

	nodeXpuLogger.Info("Status is out-of-date. Try to update status", "oldStatus", nodeXpu.Status, "newStatus", *nodeXpuStatus)

	var updatedNodeXpu *twresourcesv1alpha1.NodeXpu
	// Return empty NodeXpu if update fail. Otherwise, return the latest version NodeXpu
	err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		nodeXpuLogger.Debug("Retry updating NodeXpu status")
		// Get the latest version of nodexpu to update
		nodeXpu, err := r.Opts.NodeXpuInformer.Lister().Get(nodeXpu.Name)
		if err != nil {
			return err
		}

		// Try to update status
		nodeXpu = nodeXpu.DeepCopy()
		nodeXpu.Status = *nodeXpuStatus
		nodeXpuLogger.Debug("Update NodeXpu with object", "nodexpu", nodeXpu)
		if updatedNodeXpu, err = r.Opts.ClusterClientSets.VersionedClient().ResourcesV1alpha1().NodeXpus().UpdateStatus(ctx, nodeXpu, metav1.UpdateOptions{}); err != nil {
			nodeXpuLogger.Error(err, "Failed to update NodeXpu status")
			return err
		}
		return nil
	})
	// TODO: delete after debug
	nodeXpuLogger.Info("Client UpdateStatus returned nodexpu object", "nodexpu", updatedNodeXpu)

	if err != nil {
		return updatedNodeXpu, err
	}

	nodeXpuLogger.Info("Update NodeXpu status successfully", "nodexpu", updatedNodeXpu)
	r.NodeXpuEventRecorder.Update(updatedNodeXpu)
	return updatedNodeXpu, nil
}
