package main

import (
	"C"
	"fmt"
	"net/http"
	_ "net/http/pprof" // Import pprof for profiling
	"os"
	"strings"
	"sync"
	"time"
	"unsafe"

	"k8s.io/klog"
	"transwarp.io/mlops/llmops-fluent-bit/client"
	"transwarp.io/mlops/llmops-fluent-bit/config"
	"transwarp.io/mlops/llmops-fluent-bit/dao"
	"transwarp.io/mlops/llmops-fluent-bit/handler"
	"transwarp.io/mlops/llmops-fluent-bit/prometheus"
	"transwarp.io/mlops/llmops-fluent-bit/service/api"
	"transwarp.io/mlops/llmops-fluent-bit/util"

	"github.com/fluent/fluent-bit-go/output"
	"github.com/patrickmn/go-cache"
)

var (
	kubeTagPrefix string // kubeTagPrefix 时 in_tail 插件为pod日志解析后的事件tag自动添加的前缀

	// ENVs
	nodeName    string // nodeName 当前 fluent bit 所在节点名称
	loggerCache = cache.New(time.Minute, time.Minute)
	// mlops
	rw sync.RWMutex
)

// e.g.
// 日志文件名称（/var/log/containers）:
//
//	 格式： $pod_$namespace_$ctnName-$ctnID.log
//	 格式示例：
//	 	scene-instance-cbmarva0sqh62f0n53m0-5bdb8765cf-kflxb_default_scene-instance-cbmarva0sqh62f0n53m0-39f139d4c3bd63d0b2bc8fcfe9afccac04ca1b637a9e5353585f0478273735a9.log
//			model-cbb8dma0sqhf26339lb0-69d7c984c4-5d86t_default_model-cbb8dma0sqhf26339lb0-cd74b3e68377fde32986e3be452868aebc5de77bb3754ffa70c9041d21d714ea.log
//	 	autocv-dlie-qqw7k_default_autocv-dlie-90f6f3b741799b0456f32bf584c7f6a1b81412222868d1a5705841ce99089add.log
//	 内容： JSON格式 {"log":"xxx", "stream":"xxx", "time":"xxx"}
//		内容示例：
//			{"log":"[nsqd] 2022/08/11 06:28:41.038115 INFO: HTTP: listening on [::]:4151\n","stream":"stderr","time":"2022-08-11T06:28:41.038293855Z"}
//			{"log":"[nsqd] 2022/08/11 06:28:41.038133 INFO: TCP: listening on [::]:4150\n","stream":"stderr","time":"2022-08-11T06:28:41.038308463Z"}
const (
	LogFlag = "log"

	LogFileSuffix = ".log"

	LineBreak = "\n"
)

//export FLBPluginRegister
func FLBPluginRegister(ctx unsafe.Pointer) int {
	return output.FLBPluginRegister(ctx, "llmops-fluent-bit", "llmops-fluent-bit Output Plugin.!")
}

//export FLBPluginInit
func FLBPluginInit(plugin unsafe.Pointer) int {

	nodeName = os.Getenv("NODE_NAME")
	if nodeName == "" {
		return output.FLB_ERROR
	}

	//init config
	if config.InitConfig(plugin) == output.FLB_ERROR {
		klog.Error("Config initialization failed")
		return output.FLB_ERROR
	}

	// init clickhouse config
	if err := dao.InitClickhouse(); err != nil {
		klog.Errorf("ClickHouse initialization failed: %v", err)
		return output.FLB_ERROR
	}

	/*	//init sqlite
		if dao.InitSqlIte() == output.FLB_ERROR {
			klog.Error("SQLite initialization failed")
			return output.FLB_ERROR
		}*/

	// init metric db
	if dao.InitMetricDB() == output.FLB_ERROR {
		klog.Error("Metric database initialization failed")
		return output.FLB_ERROR
	}

	//init metrics
	prometheus.Init()
	go api.StartServer(os.Getenv("PORT"))

	if os.Getenv("ENABLE_PPROF") == "true" {
		// Start pprof server on a different port
		pprofPort := os.Getenv("PPROF_PORT")
		if pprofPort == "" {
			pprofPort = "6060" // Default pprof port
		}
		go func() {
			klog.Infof("Starting pprof server on :%s", pprofPort)
			if err := http.ListenAndServe(":"+pprofPort, nil); err != nil {
				klog.Errorf("Failed to start pprof server: %v", err)
			}
		}()
	}

	//init clients
	if client.Init() == output.FLB_ERROR {
		klog.Error("Client initialization failed")
		return output.FLB_ERROR
	}

	//init writer pool
	if util.Init() == output.FLB_ERROR {
		klog.Error("Writer pool initialization failed")
		return output.FLB_ERROR
	}

	return output.FLB_OK
}

//export FLBPluginFlush
func FLBPluginFlush(data unsafe.Pointer, length C.int, tagC *C.char) int {
	rw.Lock()
	defer rw.Unlock()
	lg, res := processorLog(tagC)

	if res == output.FLB_ERROR {
		return output.FLB_ERROR
	}

	// 是否需要处理
	if lg.Ignore || len(lg.Handlers) == 0 {
		return output.FLB_OK
	}

	//处理日志
	decodeLog(data, length, lg)

	return output.FLB_OK
}

// 解析日志Tag
func processorLog(tagC *C.char) (*handler.Logger, int) {
	tag := strings.TrimSuffix(strings.TrimPrefix(string(C.GoString(tagC)), kubeTagPrefix), LogFileSuffix)

	// 获取对应Tag日志处理方法
	var lg *handler.Logger
	if item, ok := loggerCache.Get(tag); ok {
		// 缓存中存在
		lg = item.(*handler.Logger)
	} else {
		// 缓存中不存在
		nl, err := handler.NewLoggerByTag(tag)
		if err != nil {
			klog.Errorf("handler.NewLoggerByTag cause error, err is %s", err.Error())
			return nil, output.FLB_OK
		}
		lg = nl
		klog.Infof("lg is %s", fmt.Sprintf("pod_name : %s, namespace : %s, cnt_name : %s",
			lg.PodName, lg.NameSpace, lg.CtnName))
		lg.IgnoreByLabel()
		if !lg.Ignore {
			lg.SetHandlers()
		}
		loggerCache.SetDefault(tag, lg)
	}
	return lg, output.FLB_OK
}

// 处理日志
func decodeLog(data unsafe.Pointer, length C.int, lg *handler.Logger) {
	if lg == nil || len(lg.Handlers) == 0 {
		klog.Error("Invalid logger configuration")
		return
	}

	dec := output.NewDecoder(data, int(length))
	for {
		// 读取一条日志事件
		ret, _, record := output.GetRecord(dec)
		if ret != 0 {
			break
		}

		/*
			// 创建新的map存储处理后的记录
			processed := make(map[string]interface{})

			// 转换所有字段值为字符串
			for key, val := range record {
				k := ""
				switch v := key.(type) {
				case []byte:
					k = string(v)
				case string:
					k = v
				default:
					k = fmt.Sprintf("%v", v)
				}
				switch v := val.(type) {
				case []byte:
					processed[k] = string(v)
				case string:
					processed[k] = v
				default:
					processed[k] = fmt.Sprintf("%v", v)
				}
			}

			rec, _ := json.Marshal(processed)
			klog.Infof("Processed record: %s", string(rec))*/

		// 解析日志内容
		log, ok := record[LogFlag]
		if !ok {
			continue
		}
		value := ""
		switch text := log.(type) {
		case string:
			value = text
		case []byte:
			value = string(text)
		default:
			value = fmt.Sprintf("%v", log)
		}
		if !strings.HasSuffix(value, LineBreak) {
			value += LineBreak
		}

		// 日志处理与存储
		for _, h := range lg.Handlers {
			if err := h(value, *lg.LogTag); err != nil {
				klog.Warningf("failed to handle log record, err is %s", err.Error())
				continue
			}
		}
	}

	for _, h := range lg.StorageHandlers {
		if err := h(); err != nil {
			klog.Warningf("failed to handle storage, err is %s", err.Error())
			continue
		}
	}

}

//export FLBPluginExit
func FLBPluginExit() int {
	klog.Info("Exiting fluent-bit output plugin")

	/*	// Close database connections
		dao.CloseDatabase()*/

	// Close file handles
	util.Exit()

	klog.Info("All resources released")
	return output.FLB_OK
}

func main() {}
