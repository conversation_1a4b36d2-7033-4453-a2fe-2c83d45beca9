package ugly

import (
	"fmt"
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service"
)

func NewUglyAPI(us *service.UserService, ts *service.AccessTokenService) *restful.WebService {
	return (&Resource{us: us, ts: ts}).WebService()
}

type Resource struct {
	us *service.UserService
	ts *service.AccessTokenService
}

func (r *Resource) WebService() *restful.WebService {
	tags := []string{"适配 SOPHON PORTAL"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags

	ws := new(restful.WebService)
	ws.Path("/api")
	ws.
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	queryParamRedirect := ws.QueryParameter(helper.QueryRedirect, helper.QueryRedirectDesc).
		DataType(helper.ParamTypeString).Required(false)

	ws.Route(ws.GET("/users/role").To(r.GetRole).
		Doc("获取当前用户角色：SOPHON_ADMIN or SOPHON_BASIC").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), "SOPHON_ADMIN").
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/users/profile").To(r.GetProfile).
		Doc("获取当前用户信息").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), Profile{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/users/login").Param(queryParamRedirect).
		To(r.Redirect2LoginPage).
		Doc("借助浏览器根据认证方式重定向到对应的登录页面").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusFound, http.StatusText(http.StatusFound), helper.EmptyRsp{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/login").Param(queryParamRedirect).
		To(r.LoginCheck).
		Doc("用户登录校验").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusFound, http.StatusText(http.StatusFound), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/logout").Param(queryParamRedirect).
		To(r.Logout).
		Doc("注销用户会话").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	// tokens
	ws.Route(ws.POST(fmt.Sprintf("/admins/token")).
		To(r.CreateAccessToken).
		Doc("生成用户 TOKEN").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(dao.AccessToken{}).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/admins/token")).
		Param(ws.QueryParameter(helper.QueryUsername, helper.QueryUsernameDesc).DataType(helper.ParamTypeString).Required(true)).
		To(r.DeleteAccessTokenByUsername).
		Doc("删除指定用户的 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/admins/token/{%s}", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.DeleteAccessTokenByID).
		Doc("删除指定的用户 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.PUT(fmt.Sprintf("/admins/token")).
		To(r.UpdateAccessToken).
		Doc("更新指定的用户 TOKEN").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(dao.AccessToken{}).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.POST(fmt.Sprintf("/admins/token/invalid")).
		Param(ws.QueryParameter(helper.QueryUsername, helper.QueryUsernameDesc).DataType(helper.ParamTypeString).Required(true)).
		To(r.DisableAccessTokenByUsername).
		Doc("禁用指定用户的 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes([]dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.POST(fmt.Sprintf("/admins/token/{%s}/invalid", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.DisableAccessTokenByID).
		Doc("禁用指定的用户 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET(fmt.Sprintf("/admins/token")).
		Param(ws.QueryParameter(helper.QueryUsername, helper.QueryUsernameDesc).DataType(helper.ParamTypeString).Required(true)).
		To(r.GetAccessTokenByUsername).
		Doc("获取指定用户的 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes([]dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET(fmt.Sprintf("/admins/token/{%s}", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.GetAccessTokenByID).
		Doc("获取指定的用户 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	return ws
}
