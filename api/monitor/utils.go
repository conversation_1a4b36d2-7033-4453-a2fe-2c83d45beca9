package monitor

import (
	"context"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm/utils"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service"
	"transwarp.io/applied-ai/central-auth-service/service/monitor"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
	"transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"
)

const (
	MiB = 1 << 20
	GiB = MiB << 10
)

var (
	defaultFrom = "now-1d"
	defaultTo   = "now"
	defaultStep = "3600000"
)

var defaultStatus = []string{
	monitor.StatusIdle.GetName(),
	monitor.StatusPartiallyLoad.GetName(),
	monitor.StatusResourceBottleneck.GetName(),
	monitor.StatusFullyLoaded.GetName(),
}

func (r *Resource) buildGPUOptions(ctx context.Context) (*monitor.GlobalGPUOptions, error) {

	options := &monitor.GlobalGPUOptions{
		Projects: monitor.ProjectItems{
			Internal: make([]monitor.ProjectItem, 0),
			External: make([]monitor.ProjectItem, 0),
		},
		Nodes:     make([]string, 0),
		Providers: make(map[string][]string),
		Status:    make([]string, 0),
	}
	providerDeviceMap := make(map[string]map[string]struct{})
	current, err := util.GetCurrentNamespace()
	if err != nil {
		stdlog.Errorf("can not get current namespace, err is %s", err.Error())
		return options, err
	}

	nss, err := r.ms.Kc.ListNamespaces(nil)
	if err != nil {
		stdlog.Errorf("can not list namespaces, err is %s", err.Error())
		return options, err
	}

	projects, err := r.ps.QueryList(map[string]any{})
	if err != nil {
		return nil, err
	}
	tenantMap := make(map[string]string)
	for _, proj := range projects {
		tenantMap[proj.TenantUid] = proj.Name
	}

	internal := make([]monitor.ProjectItem, 0)
	external := make([]monitor.ProjectItem, 0)
	for _, ns := range nss {
		item := monitor.ProjectItem{
			ProjectId: ns.Name,
			Name:      ns.Name,
		}
		if ns.Labels[customtypes.NamespaceLableManagedBy] == current {
			if _, ok := tenantMap[ns.Name]; ok {
				item.Name = tenantMap[ns.Name]
			}
			internal = append(internal, item)
		} else {
			external = append(external, item)
		}
	}
	options.Projects.Internal = internal
	options.Projects.External = external

	cluster, err := clients.ExpenseCli.GetCluster(ctx)
	if err != nil {
		stdlog.Errorf("can not list nodes, err is %s", err.Error())
		return options, err
	}
	for _, node := range cluster.Nodes {
		options.Nodes = append(options.Nodes, node.Name)
	}
	// Sort node names
	sort.Strings(options.Nodes)

	xpus, err := r.rgs.ListXpuGroups(ctx)
	xpus = r.filterByParam(xpus, &monitor.RequestFilter{Nodes: options.Nodes})
	for _, xpu := range xpus {
		vendor := xpu.Spec.Vendor
		if _, exists := providerDeviceMap[vendor]; !exists {
			providerDeviceMap[vendor] = make(map[string]struct{})
		}
		deviceType := xpu.Spec.Type
		providerDeviceMap[vendor][deviceType] = struct{}{}
	}
	for vendor, devices := range providerDeviceMap {
		deviceList := make([]string, 0, len(devices))
		for device := range devices {
			deviceList = append(deviceList, device)
		}
		options.Providers[vendor] = deviceList
	}

	options.Status = defaultStatus

	return options, nil
}

func (r *Resource) convertFilter(ctx context.Context, origin *monitor.GlobalGPUFilter) *monitor.RequestFilter {
	realFilter := &monitor.RequestFilter{
		From:        strconv.FormatInt(origin.From, 10),
		To:          strconv.FormatInt(origin.To, 10),
		ProjectIds:  make([]string, 0),
		Nodes:       make([]string, 0),
		Status:      make([]string, 0),
		Providers:   make([]string, 0),
		DeviceTypes: make([]string, 0),
		Step:        strconv.FormatInt(origin.Step, 10),
	}

	if len(origin.ProjectIds) != 0 {
		for _, pro := range origin.ProjectIds {
			realFilter.ProjectIds = append(realFilter.ProjectIds, pro...)
		}
	}

	if len(origin.Providers) != 0 {
		for provider, deviceType := range origin.Providers {
			realFilter.Providers = append(realFilter.Providers, provider)
			realFilter.DeviceTypes = append(realFilter.DeviceTypes, deviceType...)
		}
	}
	if len(origin.Nodes) != 0 {
		realFilter.Nodes = append(realFilter.Nodes, origin.Nodes)
	} else {
		//获取 限定nodes
		cluster, err := clients.ExpenseCli.GetCluster(ctx)
		if err == nil {
			for _, node := range cluster.Nodes {
				realFilter.Nodes = append(realFilter.Nodes, node.Name)
			}
		}
	}

	if len(origin.Status) != 0 {
		realFilter.Status = append(realFilter.Status, origin.Status)
	}

	if len(realFilter.From) == 0 || realFilter.From == "0" {
		realFilter.From = defaultFrom
		realFilter.To = defaultTo
		realFilter.Step = defaultStep
	}

	return realFilter
}

func (r *Resource) newFilter(topK int, uuid string) *monitor.RequestFilter {
	realFilter := &monitor.RequestFilter{
		Topk: topK,
	}
	if len(uuid) != 0 {
		realFilter.DeviceUUID = uuid
	}
	return realFilter
}

func (r *Resource) convert2Filter(ctx context.Context, filter *monitor.RequestFilter, globalType monitor.GlobalPanelType) *monitor.GlobalBaseQueryRequest {
	if filter == nil {
		return globalType.GetGlobalReq("", "", "")
	}

	req := globalType.GetGlobalReq(filter.From, filter.To, filter.Step)

	if len(filter.ProjectIds) != 0 {
		req.NamespaceName = strings.Join(filter.ProjectIds, "|")
	}

	if len(filter.Nodes) != 0 {
		req.Node = strings.Join(filter.Nodes, "|")
	} else {
		//获取 限定nodes
		cluster, err := clients.ExpenseCli.GetCluster(ctx)
		if err == nil {
			arr := make([]string, 0)
			for _, node := range cluster.Nodes {
				arr = append(arr, node.Name)
			}
			req.Node = strings.Join(arr, "|")
		}
	}

	if len(filter.Providers) != 0 {
		req.Provider = strings.Join(filter.Providers, "|")
	}

	if len(filter.DeviceTypes) != 0 {
		req.DeviceType = strings.Join(filter.DeviceTypes, "|")
	}

	if filter.Topk != 0 {
		req.TopK = filter.Topk
	}

	if len(filter.DeviceUUID) != 0 {
		req.DeviceUUID = filter.DeviceUUID
	}

	if len(filter.Step) == 0 || filter.Step == "0" {
		filter.Step = globalType.GetDefaultStep()
	}

	if len(filter.RefName) != 0 {
		req.RefName = "(?i).*" + filter.RefName + ".*"
	}

	if globalType == monitor.GlobalPanelTypeServiceRanking {
		req.Service = defaultType
	}

	return req
}

func (r *Resource) BuildGPUOverview(ctx context.Context, filter *monitor.RequestFilter) *monitor.GlobalGPUOverview {
	overview := &monitor.GlobalGPUOverview{
		ProjectDistribution:    make([]*monitor.Item, 0),
		NodeDistribution:       make([]*monitor.Item, 0),
		DeviceTypeDistribution: make([]*monitor.Item, 0),
	}

	projectMap := make(map[string]int64)
	disMap := make(map[string]int64)
	nodeMap := make(map[string]int64)

	xpus, err := r.rgs.ListXpuGroups(ctx)
	if err != nil {
		stdlog.Errorf("list xpu xpus: %s", err)
		return overview
	}
	xpus = r.filterByParam(xpus, filter)

	used, err := r.queryGPUUsed(ctx, xpus)
	if err != nil {
		return overview
	}

	for _, xpu := range xpus {
		overview.GPUTotal += 1
		if xpu.CardStatus != monitor.StatusIdle.GetName() {
			overview.GPUUsed += 1
		}
		if xpu.CardStatus == monitor.StatusIdle.GetName() {
			overview.IdleGPUCount += 1
		}
		if xpu.CardStatus == monitor.StatusPartiallyLoad.GetName() {
			overview.LoadedGPUCount += 1
		}
		if xpu.CardStatus == monitor.StatusFullyLoaded.GetName() {
			overview.FullyLoadedGPUCount += 1
		}
		if xpu.CardStatus == monitor.StatusResourceBottleneck.GetName() {
			overview.BottleneckGPUCount += 1
		}
		overview.GPUCoresTotal += xpu.Spec.Devcore.Value()
		/*overview.GPUCoresUsed += xpu.Status.Devcore.Usage.Value()*/
		overview.GPUVCoreAllocated += xpu.Status.Devcore.Usage.Value()

		overview.GPUMemoryTotal += xpu.Status.Devmem.Capacity.Value() / GiB
		/*	overview.GPUMemoryUsed += xpu.Status.Devmem.Usage.Value() / GiB*/
		overview.GPUMemoryAllocated += xpu.Status.Devmem.Usage.Value() / GiB

		if len(xpu.Namespace) != 0 {
			for _, ns := range xpu.Namespace {
				if _, ok := projectMap[ns]; !ok {
					projectMap[ns] = 1
				} else {
					projectMap[ns] += 1
				}
			}
		} else {
			if _, ok := projectMap[""]; !ok {
				projectMap[""] = 1
			} else {
				projectMap[""] += 1
			}
		}

		if _, ok := disMap[xpu.Spec.Type]; !ok {
			disMap[xpu.Spec.Type] = 1
		} else {
			disMap[xpu.Spec.Type] += 1
		}
		if _, ok := nodeMap[xpu.Spec.NodeName]; !ok {
			nodeMap[xpu.Spec.NodeName] = 1
		} else {
			nodeMap[xpu.Spec.NodeName] += 1
		}
	}

	overview.GPUCoresUsed = used.GPUCoresUsed
	overview.GPUMemoryUsed = used.GPUMemoryUsed

	// 只保留指定项目
	if len(filter.ProjectIds) > 0 {
		pMap := make(map[string]int64)
		for _, id := range filter.ProjectIds {
			if _, ok := projectMap[id]; ok {
				pMap[id] = projectMap[id]
			}
		}
		projectMap = pMap
	}

	for k, v := range projectMap {
		project, err := r.ps.GetProjectByTenant(k)
		if err != nil || project == nil || project.Name == "" {
			stdlog.Warnf("Failed to get project by tenant: %v", err)
			continue
		}
		delete(projectMap, k)
		projectMap[project.Name] = v
	}

	overview.ProjectDistribution = convert2Item(projectMap)
	overview.NodeDistribution = convert2Item(nodeMap)
	overview.DeviceTypeDistribution = convert2Item(disMap)

	return overview
}

func (r *Resource) queryGPUUsed(ctx context.Context, xpus []*service.XpuAdvanced) (*monitor.GPUsed, error) {
	xpuArr := make([]string, 0)

	if len(xpus) == 0 {
		return &monitor.GPUsed{}, nil
	}
	for _, xpu := range xpus {
		xpuArr = append(xpuArr, xpu.Spec.ID)
	}
	xpuStr := strings.Join(xpuArr, "|")
	xpuF := r.newFilter(0, xpuStr)

	req := r.convert2Filter(ctx, xpuF, monitor.GlobalGpuUsed)

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		return nil, err
	}
	return r.BuildGPUUsed(rsp), nil
}

func (r *Resource) BuildGPUUsed(data *monitor.SvcDashboardData) *monitor.GPUsed {
	res := &monitor.GPUsed{}

	for _, panel := range data.Panels {
		switch panel.Id {
		case "P_GPU_USED":
			if panel.Queries == nil {
				stdlog.Errorln("process base panels failed, panel is nil")
				return res
			}
			for metricID, queryData := range panel.Queries {
				if queryData.Status != 200 || len(queryData.Frames) == 0 {
					continue
				}
				if len(queryData.Frames) == 0 || len(queryData.Frames[0].Values) == 0 || queryData.Frames[0].Values[0] == nil {
					continue
				}

				val, err := queryData.Frames[0].Values[0].Float64()
				if err != nil {
					stdlog.Warnf("Failed to parse metric %s value: %v", metricID, err)
					continue
				}
				switch metricID {
				case "M_GPU_MEMORY_USAGE":
					res.GPUMemoryUsed = int64(val)
				case "M_GPU_VCORE_USAGE":
					res.GPUCoresUsed = int64(val)
				}
			}
		}
	}
	return res
}

func convert2Item(data map[string]int64) []*monitor.Item {
	items := make([]*monitor.Item, 0)
	for key, val := range data {
		if len(key) == 0 {
			key = "default"
		}
		items = append(items, &monitor.Item{
			Name:  key,
			Value: float64(val),
		})
	}
	sort.Slice(items, func(i, j int) bool {
		return (items)[i].Value > (items)[j].Value
	})
	return items
}

func (r *Resource) filterByParam(xpus []*service.XpuAdvanced, filter *monitor.RequestFilter) []*service.XpuAdvanced {
	res := make([]*service.XpuAdvanced, 0)
	tenant := filter.ProjectIds

	for _, xpu := range xpus {

		if len(tenant) != 0 {
			satisfied := false
			nsSet := make(map[string]struct{}, len(xpu.Namespace))
			for _, ns := range xpu.Namespace {
				nsSet[ns] = struct{}{}
			}
			for _, t := range tenant {
				if _, exists := nsSet[t]; exists {
					satisfied = true
					break
				}
			}

			if !satisfied {
				continue
			}

		}

		if len(filter.Nodes) != 0 && !utils.Contains(filter.Nodes, xpu.Spec.NodeName) {
			continue
		}

		if len(filter.Providers) != 0 && !utils.Contains(filter.Providers, xpu.Spec.Vendor) {
			continue
		}

		if len(filter.DeviceTypes) != 0 && !utils.Contains(filter.DeviceTypes, xpu.Spec.Type) {
			continue
		}

		if len(filter.Status) != 0 && !utils.Contains(filter.Status, xpu.CardStatus) {
			continue
		}

		res = append(res, xpu)
	}

	return res
}

func (r *Resource) processDistribution(query *monitor.QueryData, target *[]*monitor.Item) {
	if query == nil || len(query.Frames) == 0 {
		stdlog.Errorln("process base panels failed, frame is nil or frame.values is empty")
		return
	}

	for _, frame := range query.Frames {
		val, err := frame.Values[0].Float64()
		if err != nil {
			stdlog.Warnf("Failed to parse distribution value: %v", err)
			return
		}
		val = math.Round(val*100) / 100

		item := &monitor.Item{
			Name:  r.extractLabel(frame.Legend, frame.Labels),
			Value: val,
		}
		if name, ok := monitor.SourceTypeZh[item.Name]; ok {
			item.Name = name
		}

		*target = append(*target, item)
	}

	sort.Slice(*target, func(i, j int) bool {
		return (*target)[i].Value > (*target)[j].Value
	})
}

func (r *Resource) extractLabel(legend string, labels map[string]string) string {
	current, err := util.GetCurrentNamespace()
	if err != nil {
		return ""
	}

	// fixme 兼容不同指标的namespace数据
	name := ""
	name = r.extractNs(labels, current, "namespace_name")

	if len(name) == 0 {
		name = r.extractNs(labels, current, "ref_pod_namespace")
	}

	if len(name) == 0 {
		name = r.extractNs(labels, current, "exported_namespace")
	}

	if len(name) != 0 {
		return name
	}
	//node
	if node, ok := labels["node"]; ok {
		return node
	}

	// provider
	if provider, ok := labels["provider"]; ok {
		if deviceType, ok := labels["devicetype"]; ok {
			return provider + "-device_type-" + deviceType
		}
		return provider
	}

	if strings.Contains(legend, "租户:") {
		return strings.TrimPrefix(legend, "租户: ")
	}
	if strings.Contains(legend, "节点:") {
		return strings.TrimPrefix(legend, "节点: ")
	}
	if strings.Contains(legend, "卡类型:") {
		return strings.TrimPrefix(legend, "卡类型: ")
	}
	return legend
}

func (r *Resource) extractNs(labels map[string]string, current, key string) string {
	if name, ok := labels[key]; ok {
		if labels["namespace"] == current {
			project, err := r.ps.GetProjectByTenant(name)
			if err != nil || project == nil || project.Name == "" {
				stdlog.Warnf("Failed to get project by tenant: %v", err)
				return name
			} else {
				return project.Name
			}
		} else {
			return name
		}
	}
	return ""
}

func (r *Resource) BuildBaseOverview(data *monitor.SvcDashboardData) *monitor.GlobalBaseOverview {
	overview := &monitor.GlobalBaseOverview{}

	for _, panel := range data.Panels {
		switch panel.Id {
		case "P_BASE_OVERVIEW":
			r.processBasePanels(panel, overview)
		}
	}

	return overview
}

func (r *Resource) processBasePanels(panel *monitor.PanelWithData, overview *monitor.GlobalBaseOverview) {
	if panel == nil || panel.Queries == nil {
		stdlog.Errorln("process base panels failed, panel is nil")
		return
	}

	for metricID, queryData := range panel.Queries {
		if queryData.Status != 200 || len(queryData.Frames) == 0 {
			continue
		}
		if len(queryData.Frames) == 0 || len(queryData.Frames[0].Values) == 0 || queryData.Frames[0].Values[0] == nil {
			continue
		}

		val, err := queryData.Frames[0].Values[0].Float64()
		if err != nil {
			stdlog.Warnf("Failed to parse metric %s value: %v", metricID, err)
			continue
		}

		val = math.Round(val*100) / 100

		switch metricID {
		case "M_BASE_CPU_TOTAL":
			overview.CPUTotal = val
		case "M_BASE_CPU_USAGE":
			overview.CPUUsed = val
		case "M_BASE_MEMORY_TOTAL":
			overview.MemoryTotal = val
		case "M_BASE_MEMORY_USAGE":
			overview.MemoryUsed = val
		case "M_BASE_DISK_TOTAL":
			overview.StorageTotal = val
		case "M_BASE_DISK_USAGE":
			overview.StorageUsed = val
		}
	}
}

func (r *Resource) BuildRankings(panel *monitor.SvcDashboardData) *monitor.GlobalUsageRankings {
	ranking := &monitor.GlobalUsageRankings{
		GPUMem:   make(monitor.UsageRanking, 0),
		GPUCores: make(monitor.UsageRanking, 0),
		CPU:      make(monitor.UsageRanking, 0),
		Memory:   make(monitor.UsageRanking, 0),
		Storage:  make(monitor.UsageRanking, 0),
	}

	for _, p := range panel.Panels {
		switch p.Id {
		case "P_BASE_RANKING", "P_SERVICE_RANKING":
			r.processRankingPanels(p, ranking)
		}
	}

	r.sortRankingDesc(ranking.GPUMem)
	r.sortRankingDesc(ranking.GPUCores)
	r.sortRankingDesc(ranking.CPU)
	r.sortRankingDesc(ranking.Memory)
	r.sortRankingDesc(ranking.Storage)

	return ranking
}

func (r *Resource) sortRankingDesc(ranking monitor.UsageRanking) {
	sort.Slice(ranking, func(i, j int) bool {
		return ranking[i].Value > ranking[j].Value
	})
}

func (r *Resource) processRankingPanels(panel *monitor.PanelWithData, ranking *monitor.GlobalUsageRankings) {
	if panel == nil || panel.Queries == nil {
		stdlog.Errorln("process ranking panels failed, panel is nil")
		return
	}

	for metricID, queryData := range panel.Queries {
		if queryData.Status != 200 || len(queryData.Frames) == 0 {
			continue
		}

		for _, frame := range queryData.Frames {
			val, err := frame.Values[0].Float64()
			if err != nil {
				stdlog.Warnf("Failed to parse metric %s value: %v", metricID, err)
				continue
			}
			val = math.Round(val*100) / 100

			item := &monitor.Item{
				Name:  r.extractLabel(frame.Legend, frame.Labels),
				Value: val,
			}
			p, err := r.ps.GetProjectByTenant(item.Name)
			if err == nil && p != nil && len(p.Name) != 0 {
				item.Name = p.Name
			}

			switch metricID {
			case "M_BASE_RANKING_VCORE", "M_SERVICE_RANKING_VCORE":
				ranking.GPUCores = append(ranking.GPUCores, item)
			case "M_BASE_RANKING_CPU", "M_SERVICE_RANKING_CPU":
				ranking.CPU = append(ranking.CPU, item)
			case "M_BASE_RANKING_DISK":
				ranking.Storage = append(ranking.Storage, item)
			case "M_RANKING_VMEMORY":
				ranking.GPUMem = append(ranking.GPUMem, item)
			case "M_SERVICE_RANKING_MEMORY":
				ranking.Memory = append(ranking.Memory, item)
			}
		}
	}
}

func (r *Resource) BuildGlobalGPUCardBase(ctx context.Context, filter *monitor.RequestFilter) monitor.GlobalNodeGPUCardBase {
	result := monitor.GlobalNodeGPUCardBase{
		Nodes: make(map[string][]*monitor.GPUCard),
	}

	xpus, err := r.rgs.ListXpuGroups(ctx)
	if err != nil {
		stdlog.Errorf("list xpu xpus: %s", err)
		return result
	}
	xpus = r.filterByParam(xpus, filter)

	for _, xpu := range xpus {
		nodeName := xpu.Spec.NodeName
		if _, ok := result.Nodes[nodeName]; !ok {
			result.Nodes[nodeName] = make([]*monitor.GPUCard, 0)
		}

		cUsage := 0.0
		if xpu.Status.Devcore.Capacity.Value() != 0 {
			cUsage = float64(xpu.Status.Devcore.Usage.Value() * 100 / xpu.Status.Devcore.Capacity.Value())
			cUsage = math.Round(cUsage*100) / 100
		}
		mUsage := 0.0
		if xpu.Status.Devmem.Capacity.Value() != 0 {
			mUsage = float64(xpu.Status.Devmem.Usage.Value() * 100 / xpu.Status.Devmem.Capacity.Value())
			mUsage = math.Round(mUsage*100) / 100
		}

		card := &monitor.GPUCard{
			GPUMemoryUsage: mUsage,
			GPUCoresUsage:  cUsage,
			Status:         monitor.GPUStatus(xpu.CardStatus),
			UUID:           xpu.Spec.ID,
			Node:           xpu.Spec.NodeName,
			GPUName:        xpu.Spec.NodeName + "-" + strconv.FormatUint(uint64(xpu.Spec.Index), 10),
		}

		if _, ok := models.DeviceTypeMap[xpu.Spec.Type]; ok {
			card.GPUType = xpu.Spec.Vendor + "-" + models.DeviceTypeMap[xpu.Spec.Type].ShortName
		} else {
			card.GPUType = xpu.Spec.Type
		}

		result.Nodes[nodeName] = append(result.Nodes[nodeName], card)
	}

	nodeNames := make([]string, 0, len(result.Nodes))
	for nodeName := range result.Nodes {
		nodeNames = append(nodeNames, nodeName)
	}
	sort.Strings(nodeNames)

	sortedNodes := make(map[string][]*monitor.GPUCard)
	for _, nodeName := range nodeNames {
		cards := result.Nodes[nodeName]

		sort.Slice(cards, func(i, j int) bool {
			return strings.Compare(cards[i].GPUName, cards[j].GPUName) < 0
		})

		sortedNodes[nodeName] = cards
	}
	result.Nodes = sortedNodes

	return result
}

func (r *Resource) BuildGPUsage(rsp *monitor.SvcDashboardData) *monitor.GPUUsage {
	usage := &monitor.GPUUsage{
		UpdateTime: time.Now(),
	}

	for _, panel := range rsp.Panels {
		switch panel.Id {
		case "P_GPU_USAGE":
			r.processGPUUsagePanels(panel, usage)
		}
	}

	return usage
}

func (r *Resource) processGPUUsagePanels(panel *monitor.PanelWithData, usage *monitor.GPUUsage) {
	if panel == nil || panel.Queries == nil {
		stdlog.Errorln("process GPU usage panels failed, panel is nil")
		return
	}

	for metricID, queryData := range panel.Queries {
		if queryData.Status != 200 || len(queryData.Frames) == 0 {
			continue
		}
		if len(queryData.Frames) == 0 || len(queryData.Frames[0].Values) == 0 || queryData.Frames[0].Values[0] == nil {
			continue
		}

		val, err := queryData.Frames[0].Values[0].Float64()
		if err != nil {
			stdlog.Warnf("Failed to parse metric %s value: %v", metricID, err)
			continue
		}
		val = math.Round(val*100) / 100

		switch metricID {
		case "M_GPU_USED_VCORE":
			usage.GPUCoresUsed = val
		case "M_GPU_USED_VMEMORY":
			usage.GPUMemoryUsed = val
		case "M_GPU_ALLOCATED_VCORE":
			usage.GPUCoresAllocated = val
		case "M_GPU_ALLOCATED_VMEMORY":
			usage.GPUMemoryAllocated = val
		}
	}
}

func (r *Resource) BuildSvcOverview(data *monitor.SvcDashboardData) *monitor.GlobalSvcOverview {
	overview := &monitor.GlobalSvcOverview{
		TenantDistribution:      make([]*monitor.Item, 0),
		ComputeTypeDistribution: make([]*monitor.Item, 0),
		ServiceTypeDistribution: make([]*monitor.Item, 0),
		StatusDistribution:      make([]*monitor.Item, 0),
	}

	for _, panel := range data.Panels {
		switch panel.Id {
		case "P_SVC_OVERVIEW":
			r.processSvcPanels(panel, overview)
		}
	}

	return overview
}

func (r *Resource) processSvcPanels(panel *monitor.PanelWithData, overview *monitor.GlobalSvcOverview) {
	if panel == nil || panel.Queries == nil {
		stdlog.Errorln("process svc panels failed, panel is nil")
		return
	}

	for metricID, queryData := range panel.Queries {
		if queryData.Status != 200 || len(queryData.Frames) == 0 {
			continue
		}

		switch metricID {
		case "M_SVC_TENANT_DISTRIBUTION":
			r.processDistribution(queryData, &overview.TenantDistribution)
		case "M_SVC_COMPUTE_TYPE_DISTRIBUTION":
			r.processDistribution(queryData, &overview.ComputeTypeDistribution)
		case "M_SVC_SOURCE_TYPE_DISTRIBUTION":
			r.processDistribution(queryData, &overview.ServiceTypeDistribution)
		case "M_SVC_SOURCE_STATUS_DISTRIBUTION":
			r.processSvcStatusDistribution(queryData, &overview.StatusDistribution)
		}
	}
}

func (r *Resource) processSvcStatusDistribution(query *monitor.QueryData, target *[]*monitor.Item) {
	if query == nil || len(query.Frames) == 0 {
		stdlog.Errorln("process svc status distribution failed, frame is nil or frame.values is empty")
		return
	}

	for _, frame := range query.Frames {
		val, err := frame.Values[0].Float64()
		if err != nil {
			stdlog.Warnf("Failed to parse distribution value: %v", err)
			return
		}
		val = math.Round(val*100) / 100

		statusStr := frame.Legend
		statusInt, err := strconv.ParseInt(statusStr, 10, 32)
		if name, ok := monitor.StatusEnumCh[int32(statusInt)]; ok {
			statusStr = name
		}
		item := &monitor.Item{
			Name:  statusStr,
			Value: val,
		}
		*target = append(*target, item)
	}

	sort.Slice(*target, func(i, j int) bool {
		return (*target)[i].Value > (*target)[j].Value
	})
}

func (r *Resource) BuildSvcList(ctx context.Context, data *monitor.SvcDashboardData, name string, page, pageSize int) *monitor.ServiceList {
	result := &monitor.ServiceList{
		Services: make([]*monitor.ServiceItem, 0),
	}
	current, err := util.GetCurrentNamespace()
	if err != nil {
		stdlog.Errorf("can not get current namespace, err is %s", err.Error())
		return result
	}

	serviceSet := make(map[string]struct{})

	for _, panel := range data.Panels {
		if panel.Id == "P_SVC_LIST" {
			queryData, ok := panel.Queries["M_SVC_LIST"]
			if !ok || queryData.Status != 200 || len(queryData.Frames) == 0 {
				continue
			}
			for _, frame := range queryData.Frames {

				refId, ok := frame.Labels["ref_id"]
				if !ok {
					continue
				}
				if _, exists := serviceSet[refId]; !exists {
					item := &monitor.ServiceItem{
						Name: frame.Labels["ref_name"],
						Id:   frame.Labels["ref_id"],
						ProjectItem: &monitor.ProjectItem{
							ProjectId: frame.Labels["ref_project_id"],
							Name:      frame.Labels["ref_project_id"],
						},
						Pod:        frame.Labels["ref_pod_name"],
						Creator:    frame.Labels["ref_creator"],
						Type:       frame.Labels["ref_type"],
						IsExternal: current != frame.Labels["namespace"],
					}

					if current == frame.Labels["namespace"] {
						pro, err := r.ps.GetProjectById(item.ProjectItem.ProjectId)
						if err == nil && pro != nil && len(pro.Name) != 0 {
							item.ProjectItem.Name = pro.Name
						}
					}

					v, err := frame.Values[0].Int64()
					if err == nil {
						if name, ok := monitor.StatusEnumCh[int32(v)]; ok {
							item.Status = name
						}
					}
					if len(name) != 0 {
						if !strings.Contains(item.Name, name) ||
							!strings.Contains(item.Pod, name) ||
							!strings.Contains(item.Id, name) {
							continue
						}
					}
					result.Services = append(result.Services, item)
					result.Size += 1

					serviceSet[refId] = struct{}{}
				}
			}
		}
	}

	sort.Slice(result.Services, func(i, j int) bool {
		return result.Services[i].Name < result.Services[j].Name
	})

	return result
}

func (r *Resource) BuildGPUInfo(ctx context.Context, uuid string, usage *monitor.GPUUsage) monitor.GPUDetail {
	result := monitor.GPUDetail{}
	xpus, err := r.rgs.ListXpuGroups(ctx)
	if err != nil {
		stdlog.Errorf("list xpu xpus: %s", err)
		return result
	}
	for _, xpu := range xpus {
		if xpu.Spec.ID == uuid {
			result = monitor.GPUDetail{
				GPUCoresTotal:  xpu.Status.Devcore.Capacity.Value(),
				GPUMemoryTotal: xpu.Status.Devmem.Capacity.Value() / GiB,
				DeviceTypeItem: &monitor.DeviceTypeItem{
					DeviceType: xpu.Spec.Type,
					Provider:   xpu.Spec.Vendor,
				},
				Index:   strconv.Itoa(int(xpu.Spec.Index)),
				Node:    xpu.Spec.NodeName,
				UUID:    uuid,
				Status:  monitor.GPUStatus(monitor.GetStatus(xpu.Status)),
				GPUType: xpu.Spec.Type,
				GPUName: xpu.Spec.NodeName + "-" + strconv.FormatUint(uint64(xpu.Spec.Index), 10),
			}

			break
		}
	}

	return result
}

func (r *Resource) ConvertBaseUsageName(ctx context.Context, data *monitor.SvcDashboardData) {
	if data == nil || data.Panels == nil || len(data.Panels) == 0 {
		return
	}

	for _, panel := range data.Panels {
		if panel.Queries == nil {
			continue
		}
		for _, query := range panel.Queries {
			if query.Status != 200 || len(query.Frames) == 0 {
				continue
			}
			for _, frame := range query.Frames {
				frame.Legend = r.extractLabel(frame.Legend, frame.Labels)
			}
		}
	}
}

func (r *Resource) ConvertLegendName(ctx context.Context, data *monitor.SvcDashboardData) {
	if data == nil || data.Panels == nil || len(data.Panels) == 0 {
		return
	}

	xpus, err := r.rgs.ListXpus(ctx)
	if err != nil {
		stdlog.Errorf("list xpus: %s", err)
		return
	}
	xpuMap := make(map[string]*v1alpha1.NodeXpu)
	for _, xpu := range xpus {
		xpuMap[xpu.Name] = xpu
	}

	npus, err := clients.ExpenseCli.GetNpuInfos(ctx)
	if err != nil {
		stdlog.Errorf("get npus err: %s", err)
		return
	}

	for _, panel := range data.Panels {
		if panel.Queries == nil {
			continue
		}
		for _, query := range panel.Queries {
			if query.Status != 200 || len(query.Frames) == 0 {
				continue
			}
			for _, frame := range query.Frames {
				fi := frame.Legend

				parts := strings.SplitN(fi, "_", 2)
				if len(parts) != 2 {
					continue
				}
				node := parts[0]
				uuid := parts[1]

				for _, npu := range npus.Infos {
					if npu.Node == node && npu.VDIEID == uuid {
						uuid = npu.HamiID
						break
					}
				}

				for k, v := range xpuMap {
					if strings.Contains(strings.ToLower(k), strings.ToLower(node)) &&
						strings.Contains(strings.ToLower(k), strings.ToLower(uuid)) {

						name := v.Spec.NodeName + "-" + strconv.FormatUint(uint64(v.Spec.Index), 10)

						if _, ok := models.DeviceTypeMap[v.Spec.Type]; ok {
							name += "-" + v.Spec.Vendor + "-" + models.DeviceTypeMap[v.Spec.Type].ShortName
						} else {
							name += "-" + v.Spec.Type
						}
						fi = name
						break
					}
				}
				frame.Legend = fi

			}
		}
	}
}

func (r *Resource) QueryNpuInfo(ctx context.Context) []monitor.NPUInfo {
	npuF := r.newFilter(0, "")

	req := r.convert2Filter(ctx, npuF, monitor.GlobalNpuInfo)
	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		stdlog.Errorf("query npu info err : %s", err)
		return []monitor.NPUInfo{}
	}
	return r.BuildNpuInfo(rsp)
}

func (r *Resource) BuildNpuInfo(data *monitor.SvcDashboardData) []monitor.NPUInfo {
	res := make([]monitor.NPUInfo, 0)
	if data == nil || data.Panels == nil || len(data.Panels) == 0 {
		return res
	}
	for _, panel := range data.Panels {
		if panel.Queries == nil {
			continue
		}
		for _, query := range panel.Queries {
			if query.Status != 200 || len(query.Frames) == 0 {
				continue
			}
			for _, frame := range query.Frames {
				// fixme 脏数据 !
				if frame.Labels == nil || frame.Labels["node_name"] == "" {
					continue
				}
				res = append(res, monitor.NPUInfo{
					ID:          frame.Labels["id"],
					Name:        frame.Labels["name"],
					Namespace:   frame.Labels["namespace"],
					Node:        frame.Labels["node"],
					NodeName:    frame.Labels["node_name"],
					PCieBusInfo: frame.Labels["pcie_bus_info"],
					VDieId:      frame.Labels["vdie_id"],
				})

			}

		}
	}
	return res
}
