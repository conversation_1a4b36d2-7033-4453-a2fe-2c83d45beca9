package report

import (
	"context"
	"strconv"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/mlops/expense/models"
	"transwarp.io/mlops/expense/service"
	"transwarp.io/mlops/expense/utils"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
)

func (this *ReportApi) Summary(request *restful.Request, response *restful.Response) {
	tenantId := request.QueryParameter("tenantId")
	if tenantId == "" {
		util.PostResponse(request, response, nil, stderr.ParamMiss.Error("tenantId"))
		return
	}
	rs, err := this.report.QuerySummary(utils.ContextWithToken(request), tenantId)
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}

	r, err := this.getResource(request, response)

	err = exploreResource(r, rs, request.QueryParameter("replicas_type") == "share")
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}

	util.PostResponse(request, response, rs, nil)
}

func (this *ReportApi) getResource(request *restful.Request, response *restful.Response) (*models.CheckResourceReq, error) {
	resource := &models.CheckResourceReq{}

	gpuCore := request.QueryParameter("gpu_core")
	resource.GpuCore = gpuCore

	gpuMemory := request.QueryParameter("gpu_memory")
	resource.GpuMemory = gpuMemory

	cpuLimit := request.QueryParameter("cpu_limit")
	resource.CpuLimit = cpuLimit

	memoryLimit := request.QueryParameter("memory_limit")
	resource.MemoryLimit = memoryLimit

	expenseRuleId := request.QueryParameter("expense_rule_id")
	e, err := strconv.Atoi(expenseRuleId)
	if err != nil {
		e = 0
	}
	resource.ExpenseRuleId = uint(e)

	replicas := request.QueryParameter("replicas")
	r, _ := strconv.Atoi(replicas)
	if r <= 0 {
		r = 1
	}
	resource.Replicas = int64(r)

	err = this.cluster.GetResourceByExpenseRuleId(resource)
	if err != nil {
		return nil, err
	}

	return resource, nil
}

func exploreResource(resource *models.CheckResourceReq, rs *models.SourceUsedInfo, share bool) error {

	replicas := resource.Replicas
	if replicas <= 0 {
		replicas = 1
	}
	if share {
		replicas = 1 // 多个实例共享 GPU 限额
	}

	cpu, err := exploreGib(resource.CpuLimit, resource.Replicas)
	if err != nil {
		return err
	}
	rs.Cpu.Config = cpu

	memory, err := exploreGib(resource.MemoryLimit, resource.Replicas)
	if err != nil {
		return err
	}
	rs.Memory.Config = memory

	gpuMemory, err := exploreGib(resource.GpuMemory, resource.Replicas)
	if err != nil {
		return err
	}
	rs.GpuMemory.Config = gpuMemory

	gpuCore, err := strconv.ParseInt(resource.GpuCore, 10, 64)
	if err != nil {
		gpuCore = 0
	}

	rs.Gpu.Config = float64(gpuCore/100) * float64(replicas)

	return nil
}

// exploreGib 将输入的字符串 val（如 "2000m" 或 "20Gi"）转换为以 GiB 为单位的 float64 值
func exploreGib(val string, replicas int64) (float64, error) {
	if len(val) == 0 {
		return 0, nil
	}

	q, err := resource.ParseQuantity(val)
	if err != nil {
		return 0, err
	}

	q.SetMilli(q.MilliValue() * replicas)

	//cpu 类型
	if q.Format == resource.DecimalSI {
		return utils.RoundTo(float64(q.Value()), 2), nil
	} else {
		return utils.RoundTo(float64(q.Value())/service.GiB, 2), nil
	}

}

func (this *ReportApi) Range(request *restful.Request, response *restful.Response) {
	tenantId := request.QueryParameter("tenantId")
	if tenantId == "" {
		util.PostResponse(request, response, nil, stderr.ParamMiss.Error("tenantId"))
		return
	}
	source := request.QueryParameter("source")
	if source == "" {
		util.PostResponse(request, response, nil, stderr.ParamMiss.Error("source"))
		return
	}

	sampleStep := request.QueryParameter("sampleStep")

	var err error
	start := int64(0)
	startStr := request.QueryParameter("start")
	if startStr != "" {
		start, err = strconv.ParseInt(startStr, 10, 64)
		if err != nil {
			util.PostResponse(request, response, nil, err)
			return
		}
	}

	end := int64(0)
	endStr := request.QueryParameter("end")
	if endStr != "" {
		end, err = strconv.ParseInt(endStr, 10, 64)
		if err != nil {
			util.PostResponse(request, response, nil, err)
			return
		}
	}

	step := 0
	stepStr := request.QueryParameter("step")
	if stepStr != "" {
		step, err = strconv.Atoi(stepStr)
		if err != nil {
			util.PostResponse(request, response, nil, err)
			return
		}
	}
	var node []string
	nodeParam := request.QueryParameter("node")
	if nodeParam != "" {
		node = strings.Split(nodeParam, ",")
	}
	rs, err := this.report.QueryRange(utils.ContextWithToken(request), &models.RangeQueryReq{
		TenantId:   tenantId,
		Start:      start,
		End:        end,
		Step:       step,
		SampleStep: sampleStep,
		Source:     models.QuerySourceType(source),
		Node:       node,
	})
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	util.PostResponse(request, response, rs, nil)
}

func (this *ReportApi) Rank(request *restful.Request, response *restful.Response) {
	tenantId := request.QueryParameter("tenantId")
	if tenantId == "" {
		util.PostResponse(request, response, nil, stderr.ParamMiss.Error("tenantId"))
		return
	}

	diskType := request.QueryParameter("disk_type")
	var diskTypes []string
	if diskType != "" {
		diskTypes = strings.Split(diskType, ",")
	} else {
		diskTypes = []string{service.Model, service.Knowledge, service.Corpus}
	}

	rank, err := this.report.QueryRank(utils.ContextWithToken(request), tenantId, diskTypes)
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	util.PostResponse(request, response, rank, nil)

}

func (this *ReportApi) GPUCoreMetrics(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	GpuId := request.QueryParameter("gpuId")
	Node := request.QueryParameter("node")
	ServiceID := request.QueryParameter("serviceId")
	start, err := strconv.Atoi(request.QueryParameter("start"))
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	end, err := strconv.Atoi(request.QueryParameter("end"))
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	step, err := strconv.Atoi(request.QueryParameter("step"))
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	metrics, err := getMetrics(ctx, &models.QueryGPUMetricsParam{
		Node:      Node,
		GPUID:     GpuId,
		ServiceID: ServiceID,
		TimeStart: int64(start / 1e3),
		TimeEnd:   int64(end / 1e3),
		Step:      int64(step),
	})
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	util.PostResponse(request, response, models.GPUMetricsToCoreMetrics(metrics, util.GetLocale(request)), nil)

}

func getMetrics(ctx context.Context, param *models.QueryGPUMetricsParam) (map[int64]models.GPUMetrics, error) {
	// todo service 转 pod
	gpuIDs := make([]string, 0)
	nodes := make([]string, 0)
	if param.Node != "" {
		nodes = append(nodes, param.Node)
	}
	if param.GPUID != "" {
		gpuIDs = append(gpuIDs, param.GPUID)
	}
	metrics, err := service.GPUMetricsSvc.GetGPUMetrics(&models.QueryGPUMetricsCliParam{
		Nodes:     nodes,
		GPUIDs:    gpuIDs,
		TimeStart: param.TimeStart,
		TimeEnd:   param.TimeEnd,
		Step:      param.Step,
	})
	if err != nil {
		return nil, err
	}
	return metrics.ToGPUMetricsMap(), nil

}

func (this *ReportApi) GPUMemoryMetrics(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	GpuId := request.QueryParameter("gpuId")
	Node := request.QueryParameter("node")
	ServiceID := request.QueryParameter("service-id")
	start, err := strconv.ParseInt(request.QueryParameter("start"), 10, 64)
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	end, err := strconv.ParseInt(request.QueryParameter("end"), 10, 64)
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	step, err := strconv.ParseInt(request.QueryParameter("step"), 10, 64)
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	metrics, err := getMetrics(ctx, &models.QueryGPUMetricsParam{
		Node:      Node,
		GPUID:     GpuId,
		ServiceID: ServiceID,
		TimeStart: int64(start / 1e3),
		TimeEnd:   int64(end / 1e3),
		Step:      step,
	})
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	util.PostResponse(request, response, models.GPUMetricsToMemoryMetrics(metrics, util.GetLocale(request)), nil)

}

func (this *ReportApi) RefreshHippoCache(request *restful.Request, response *restful.Response) {
	err := this.report.RefreshHippoCache(utils.ContextWithToken(request))
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	util.PostResponse(request, response, "success", nil)
}

func (this *ReportApi) Nodes(request *restful.Request, response *restful.Response) {
	rs, err := this.report.GetNodes(utils.ContextWithToken(request))
	if err != nil {
		util.PostResponse(request, response, nil, err)
		return
	}
	util.PostResponse(request, response, rs, nil)
}
