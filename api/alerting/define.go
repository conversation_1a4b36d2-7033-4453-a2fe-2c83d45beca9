package alerting

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	"transwarp.io/applied-ai/central-auth-service/helper"
	almdl "transwarp.io/applied-ai/central-auth-service/models/alerting"
	alsvc "transwarp.io/applied-ai/central-auth-service/service/alerting"
)

const (
	QueryParamProjectId = "project_id"
	QueryParamUser      = "user_id"
	QueryParamServiceId = "service_id"
	QueryParamRuleId    = "rule_id"
)

var (
	RuleNotFoundErr         = stderr.NotFound.Errorf("rule not found")
	RuleNameExistsForSvcErr = stderr.RuleNameExistsErr.Errorf("rule name exists for service")
)

func NewAlertingAPI(as *alsvc.AlertService) *restful.WebService {
	return (&Resource{as}).WebService()
}

type Resource struct {
	ars *alsvc.AlertService
}

func (r *Resource) ListRules(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	serviceID := request.QueryParameter(QueryParamServiceId)
	if serviceID == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("service_id is empty"))
		return
	}
	req := &alsvc.ListRulesReq{
		ServiceId: serviceID,
		ProjectId: helper.GetProjectID(ctx),
	}
	rsp, err := r.ars.ListRules(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) CreateRule(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	rule := new(almdl.AlertRule)
	if err := request.ReadEntity(rule); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}

	user, err := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户信息获取失败！"))
		return
	}
	rule.Creator = user.Name

	ex, err := r.ars.NameExistsForSvc(ctx, rule.ServiceId, rule.Name)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	if ex {
		helper.ErrorResponse(response, RuleNameExistsForSvcErr)
		return
	}

	ret, err := r.ars.CreateRule(ctx, rule)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ret)
}

func (r *Resource) UpdateRule(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	rule := new(almdl.AlertRule)
	if err := request.ReadEntity(rule); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req err"))
		return
	}

	cur, err := r.ars.GetRule(ctx, rule.Id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if cur == nil {
		helper.ErrorResponse(response, RuleNotFoundErr)
		return
	}

	if rule.Name != cur.Name {
		ex, err := r.ars.NameExistsForSvc(ctx, cur.ServiceId, rule.Name)
		if err != nil {
			helper.ErrorResponse(response, err)
			return
		}
		if ex {
			helper.ErrorResponse(response, RuleNameExistsForSvcErr)
			return
		}
	}

	rule = cur.UpdateFrom(rule)
	ret, err := r.ars.UpdateRule(ctx, rule)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ret)
}

func (r *Resource) PatchRulePaused(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	pausedReq := new(PatchRulePausedReq)
	if err := request.ReadEntity(&pausedReq); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req err"))
		return
	}

	if pausedReq.Id <= 0 || pausedReq.IsPaused == nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("invalid param"))
		return
	}

	err := r.ars.PatchRulePaused(ctx, pausedReq.Id, *pausedReq.IsPaused)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, nil)
}

func (r *Resource) DeleteRule(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	ruleIdStr := request.QueryParameter(QueryParamRuleId)
	if ruleIdStr == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("rule_id is empty"))
	}
	// parse ruleIdStr to int64
	ruleId, err := strconv.ParseInt(ruleIdStr, 10, 64)
	if err != nil || ruleId <= 0 {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "invalid rule_id"))
		return
	}

	err = r.ars.DeleteRule(ctx, ruleId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, nil)
}

func (r *Resource) Exists(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	req := new(RuleExistsReq)
	if err := request.ReadEntity(req); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req err"))
		return
	}

	if req.ServiceId == "" || req.Name == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("service_id or name is empty"))
		return
	}

	ex, err := r.ars.NameExistsForSvc(ctx, req.ServiceId, req.Name)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, &RuleExistsRsp{Exists: ex})

}

type PatchRulePausedReq struct {
	Id       int64 `json:"id"`
	IsPaused *bool `json:"is_paused"`
}

type RuleExistsReq struct {
	ServiceId string `json:"service_id"`
	Name      string `json:"name"`
}

type RuleExistsRsp struct {
	Exists bool `json:"exists"`
}
