syntax = "proto3";

package proto;

import "proto/model.proto";

option go_package = "transwarp.io/aip/llmops-common/pb;pb";

// PageReq 资源查询时的一些分页相关请求
// TODO 支持 sort_by & is desc
message PageReq {
  int32  page      = 1;
  int32  page_size = 2;
  string sort_by   = 3;
  bool   is_desc   = 4;
}

enum SortBy {
  Sort_By_UNSPECIFIED    = 0;
  Sort_By_Visit_Times    = 1;
  Sort_By_Clone_Times    = 2;
  Sort_By_Execute_Times  = 3;
  Sort_By_Download_Times = 4;
}
enum UserRole {
  USER_ROLE_UNSPECIFIED = 0;
  USER_ROLE_ADMIN       = 1;
  USER_ROLE_BASIC       = 2;
}
// UserContext 用于grpc之间传输请求调用的用户相关上下文,即发起该请求的用户信息
message UserContext {
           string   user_id    = 1;
           string   user_name  = 2;
  repeated UserRole roles      = 3;
           string   project_id = 4;
           string   token      = 5;
           string   tenant_id  = 6;
}

message Enum {
           int32  id        = 1; // 枚举型的值
           string name      = 2; // 枚举型的名称
  repeated Enum   sub_enums = 3; // 子枚举类型
}

message Enums {
  repeated Enum enums = 1; // 一组枚举类型的定义，主要用于获取枚举型范围
}

message LabelSelector {
  map <string,string> labels = 1;
}

message DomainSelector {
           ModelKind    kind      = 1;
  repeated ModelSubKind sub_kinds = 2;
  repeated ModelType    types     = 3;
  repeated ModelSubType sub_types = 4;
}

enum DocumentFileSource {
  
  // @gotags: description:"本地上传"
  LOCAL_FILE = 0;
  
  // @gotags: description:"语料数据集"
  CORPUS_DATA_SET = 1;
  
  // @gotags: description:"文件资产，2.0 新增，文档知识库默认都是这个来源"
  FILE_ASSET = 2;
}
