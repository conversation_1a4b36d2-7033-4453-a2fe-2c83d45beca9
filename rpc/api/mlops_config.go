package api

import (
	"context"
	llmcommon "transwarp.io/aip/llmops-common/pb/common"
	llmpb "transwarp.io/aip/llmops-common/pb/serving"
	k8s "transwarp.io/mlops/mlops-std/k8s"
)

type MLOpsConfig struct {
	llmpb.UnsafeMLOpsConfigServiceServer
}

func (M MLOpsConfig) ExternalConfig(ctx context.Context, rsp *llmcommon.EmptyRsp) (*llmpb.MlopsExternalConfig, error) {
	return &llmpb.MlopsExternalConfig{
		ExternalConfig: map[string]string{
			"ISTIO_INGRESS": "istio-ingressgateway." + k8s.CurrentNamespaceInCluster() + ".svc:80",
		},
	}, nil
}
