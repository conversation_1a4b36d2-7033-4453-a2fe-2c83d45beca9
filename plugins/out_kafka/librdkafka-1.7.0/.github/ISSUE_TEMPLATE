Read the FAQ first: https://github.com/edenhill/librdkafka/wiki/FAQ



Description
===========
<your issue description goes here>


How to reproduce
================
<your steps how to reproduce goes here, or remove section if not relevant>


**IMPORTANT**: Always try to reproduce the issue on the latest released version (see https://github.com/edenhill/librdkafka/releases), if it can't be reproduced on the latest version the issue has been fixed.


Checklist
=========

**IMPORTANT**: We will close issues where the checklist has not been completed.

Please provide the following information:

 - [x] librdkafka version (release number or git tag): `<REPLACE with e.g., v0.10.5 or a git sha. NOT "latest" or "current">`
 - [ ] Apache Kafka version: `<REPLACE with e.g., ********>`
 - [ ] librdkafka client configuration: `<REPLACE with e.g., message.timeout.ms=123, auto.reset.offset=earliest, ..>`
 - [ ] Operating system: `<REPLACE with e.g., Centos 5 (x64)>`
 - [ ] Provide logs (with `debug=..` as necessary) from librdkafka
 - [ ] Provide broker log excerpts
 - [ ] Critical issue

