librdkafka.so.1 librdkafka1 #MINVER#
* Build-Depends-Package: librdkafka-dev
 rd_kafka_brokers_add@Base 0.8.0
 rd_kafka_conf_destroy@Base 0.8.0
 rd_kafka_conf_dump@Base 0.8.3
 rd_kafka_conf_dump_free@Base 0.8.3
 rd_kafka_conf_dup@Base 0.8.3
 rd_kafka_conf_new@Base 0.8.0
 rd_kafka_conf_properties_show@Base 0.8.0
 rd_kafka_conf_set@Base 0.8.0
 rd_kafka_conf_set_dr_cb@Base 0.8.0
 rd_kafka_conf_set_dr_msg_cb@Base 0.8.4
 rd_kafka_conf_set_error_cb@Base 0.8.0
 rd_kafka_conf_set_log_cb@Base 0.8.4
 rd_kafka_conf_set_opaque@Base 0.8.0
 rd_kafka_conf_set_open_cb@Base 0.8.4
 rd_kafka_conf_set_socket_cb@Base 0.8.4
 rd_kafka_conf_set_stats_cb@Base 0.8.0
 rd_kafka_consume@Base 0.8.0
 rd_kafka_consume_batch@Base 0.8.0
 rd_kafka_consume_batch_queue@Base 0.8.4
 rd_kafka_consume_callback@Base 0.8.0
 rd_kafka_consume_callback_queue@Base 0.8.4
 rd_kafka_consume_queue@Base 0.8.4
 rd_kafka_consume_start@Base 0.8.0
 rd_kafka_consume_start_queue@Base 0.8.4
 rd_kafka_consume_stop@Base 0.8.0
 rd_kafka_destroy@Base 0.8.0
 rd_kafka_dump@Base 0.8.0
 rd_kafka_err2str@Base 0.8.0
 rd_kafka_errno2err@Base 0.8.3
 rd_kafka_log_print@Base 0.8.0
 rd_kafka_log_syslog@Base 0.8.0
 rd_kafka_message_destroy@Base 0.8.0
 rd_kafka_metadata@Base 0.8.4
 rd_kafka_metadata_destroy@Base 0.8.4
 rd_kafka_msg_partitioner_random@Base 0.8.0
 rd_kafka_name@Base 0.8.0
 rd_kafka_new@Base 0.8.0
 rd_kafka_offset_store@Base 0.8.3
 rd_kafka_opaque@Base 0.8.4
 rd_kafka_outq_len@Base 0.8.0
 rd_kafka_poll@Base 0.8.0
 rd_kafka_produce@Base 0.8.0
 rd_kafka_produce_batch@Base 0.8.4
 rd_kafka_queue_destroy@Base 0.8.4
 rd_kafka_queue_new@Base 0.8.4
 rd_kafka_set_log_level@Base 0.8.0
 rd_kafka_set_logger@Base 0.8.0
 rd_kafka_thread_cnt@Base 0.8.0
 rd_kafka_topic_conf_destroy@Base 0.8.0
 rd_kafka_topic_conf_dump@Base 0.8.3
 rd_kafka_topic_conf_dup@Base 0.8.3
 rd_kafka_topic_conf_new@Base 0.8.0
 rd_kafka_topic_conf_set@Base 0.8.0
 rd_kafka_topic_conf_set_opaque@Base 0.8.0
 rd_kafka_topic_conf_set_partitioner_cb@Base 0.8.0
 rd_kafka_topic_destroy@Base 0.8.0
 rd_kafka_topic_name@Base 0.8.0
 rd_kafka_topic_new@Base 0.8.0
 rd_kafka_topic_partition_available@Base 0.8.0
 rd_kafka_version@Base 0.8.1
 rd_kafka_version_str@Base 0.8.1
 rd_kafka_wait_destroyed@Base 0.8.0
