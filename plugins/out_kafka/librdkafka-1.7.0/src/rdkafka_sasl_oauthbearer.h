/*
 * librdkafka - The Apache Kafka C/C++ library
 *
 * Copyright (c) 2019 Magnus <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANT<PERSON>ILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef _RDKAFKA_SASL_OAUTHBEARER_H_
#define _RDKAFKA_SASL_OAUTHBEARER_H_

void rd_kafka_oauthbearer_unsecured_token (rd_kafka_t *rk,
                                           const char *oauthbearer_config,
                                           void *opaque);

rd_kafka_resp_err_t
rd_kafka_oauthbearer_set_token0 (rd_kafka_t *rk,
                                 const char *token_value,
                                 int64_t md_lifetime_ms,
                                 const char *md_principal_name,
                                 const char **extensions,
                                 size_t extension_size,
                                 char *errstr, size_t errstr_size);

rd_kafka_resp_err_t
rd_kafka_oauthbearer_set_token_failure0 (rd_kafka_t *rk, const char *errstr);

int unittest_sasl_oauthbearer (void);


#endif /* _RDKAFKA_SASL_OAUTHBEARER_H_ */
