package util

import (
	"context"
	"google.golang.org/grpc/metadata"
	token "transwarp.io/mlops/mlops-std/token"
	"transwarp.io/mlops/serving/consts"
)

// newContext 向 context 中插入项目 ID
func NewContext(creator, projectId string) context.Context {
	ctx := context.Background()
	info := &token.TokenInfo{}
	info.UserName = creator
	info.Roles = []string{"ADMIN"}
	tokenStr := token.Encode(info)
	md := metadata.New(map[string]string{consts.ProjectId: projectId, token.AUTH_HEADER: tokenStr})
	return metadata.NewIncomingContext(ctx, md)
}
