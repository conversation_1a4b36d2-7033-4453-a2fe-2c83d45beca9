package stdhub

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"

	"gorm.io/driver/mysql"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"

	"gorm.io/gorm"

	stdconf "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type SqlResourceHubConfig struct {
	stdconf.MysqlConfig
}

const (
	DefaultTableName = "resource_hub"
)

type DbRsc struct {
	ID          string    `json:"id" gorm:"column:id;primaryKey"`
	Name        string    `json:"name" gorm:"column:name"`
	RscKey      string    `json:"rsc_key" gorm:"column:rsc_key"`
	Value       string    `json:"value" gorm:"column:value"`
	Description string    `json:"description" gorm:"column:description"`
	Type        string    `json:"type" gorm:"column:type"`
	Owner       string    `json:"owner" gorm:"column:owner"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;default:NULL;<-:create"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;default:NULL"`
	DeletedAt   time.Time `json:"deleted_at" gorm:"column:deleted_at;default:NULL;index;<-:create"`
}

func (d *DbRsc) BeforeCreate(tx *gorm.DB) (err error) {
	d.CreatedAt = time.Now()
	return nil
}

func (d *DbRsc) TableName() string {
	return DefaultTableName
}

type SqlResourceHub struct {
	db        *gorm.DB
	tableName string
	// key: RscKey.KeyString, value: ThingerResource.Value()
	cache        sync.Map
	changeSignal chan *DbRsc
	watchFlags   map[RscType]bool
}

func NewSqlResourceHub(config *SqlResourceHubConfig) (*SqlResourceHub, error) {
	if config == nil {
		return nil, stderr.Internal.Error("sql resource config is nil")
	}
	// 先创建database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=utf8mb4&parseTime=True&loc=Local",
		config.Username, config.Password, config.Host, config.Port)
	d, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, stderr.Wrap(err, "opening sql db as resource hub")
	}
	rawSql := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARSET utf8", config.DBName)
	_, err = d.Exec(rawSql)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to create db.")
	}
	d.Close()

	gormDsn := fmt.Sprintf("%s:%s@(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local", config.Username, config.Password, config.Host, config.Port, config.DBName)
	db, err := gorm.Open(mysql.Open(gormDsn))
	if err != nil {
		return nil, stderr.Wrap(err, "opening sql db as resource hub")
	}
	err = db.AutoMigrate(DbRsc{})
	if err != nil {
		return nil, stderr.Wrap(err, "failed to create table")
	}
	stdlog.Infof("fetch mysql resource hub data to memory cache")
	dbRscs := make([]*DbRsc, 0)
	err = db.Model(&DbRsc{}).Scan(&dbRscs).Error
	if err != nil {
		stdlog.WithError(err).Error("failed to select all data in resource hub")
		return nil, err
	}
	sqlRscHub := &SqlResourceHub{
		db:           db,
		tableName:    DefaultTableName,
		cache:        sync.Map{},
		changeSignal: make(chan *DbRsc),
		watchFlags:   make(map[RscType]bool),
	}
	for _, dbRsc := range dbRscs {
		sqlRscHub.cache.Store(dbRsc.RscKey, []byte(dbRsc.Value))
	}
	return sqlRscHub, nil
}

func (s *SqlResourceHub) Close() error {
	defer close(s.changeSignal)
	sqlDB, err := s.db.DB()
	if err != nil {
		return stderr.Wrap(err, "failed to get db when close it")
	}
	return sqlDB.Close()
}

func (s *SqlResourceHub) Exist(k RscKey) bool {
	var exist int
	err := s.db.Raw(fmt.Sprintf(`SELECT EXISTS(SELECT * FROM %s WHERE rsc_key="%s");`, s.tableName, k.KeyString())).Scan(&exist).Error
	if err != nil {
		return false
	}
	if exist > 0 {
		return true
	}
	return false
}

func (s *SqlResourceHub) Load(k RscKey, r ThingerResource) error {
	var rBytes []byte
	var err error
	rAny, ok := s.cache.Load(k.KeyString())
	if ok {
		rBytes = rAny.([]byte)
	} else {
		//缓存击穿
		rBytes, err = s.Get(k.KeyBytes())
		// 读取资源失败
		if err != nil {
			return err
		}
		s.cache.Store(k.KeyString(), rBytes)
	}
	// 结构体解析失败
	if err = r.SetValue(rBytes); err != nil {
		return setValueErr(err, rBytes)
	}

	// 资源已被删除
	if r == nil || r.IsDeleted() {
		return stderr.ResourceDeleted.Error("%s", k.KeyString())
	}
	return nil
}

var notSupportWatch = stderr.NotAllowed.Error("sql resource hub not supported watching changes yet. please use WatchRsc")

func (s *SqlResourceHub) WatchAll(ctx context.Context, handler RscKVHandler) error {
	return notSupportWatch
}

// WatchKV 观测某个资源类型的变化（FIXME, 基于内存可以实现，但是如果多副本时，可能会导致不一致的问题，因此暂不支持， 后续可考虑引入额外的event机制）
func (s *SqlResourceHub) WatchKV(ctx context.Context, owner string, handler RscKVHandler) error {
	return notSupportWatch
}

func (s *SqlResourceHub) WatchRsc(ctx context.Context, owner string, rscType RscType, rsc ThingerResource, handler RscHandler) error {
	if owner == "" {
		return stderr.Internal.Error("owner can not be empty")
	}
	s.watchFlags[rscType] = true
	defer func() { s.watchFlags[rscType] = false }()
	rt := reflect.TypeOf(rsc).Elem()
	nRsc := reflect.New(rt).Interface().(ThingerResource)
	for {
		select {
		case dbrsc, ok := <-s.changeSignal:
			if !ok {
				continue
			}
			if dbrsc == nil {
				continue
			}
			if dbrsc.Type != string(rscType) || dbrsc.Owner != owner {
				continue
			}
			stdlog.Debugf("%s changes has been observered", rscType)
			if err := nRsc.SetValue([]byte(dbrsc.Value)); err != nil {
				stdlog.WithError(err).Error("set value")
				continue
			}
			key := NewRscKeyFromString(dbrsc.RscKey)
			if key == nil {
				stdlog.Errorf("invalid resource key : %s", dbrsc.RscKey)
				continue
			}
			err := handler(*key, nRsc)
			if err != nil {
				stdlog.WithError(err).Error("invoke handler")
				continue
			}
		}
	}
}

func (s *SqlResourceHub) Count(owner string, rscType RscType, r ThingerResource, selectors ...ResourceSelector) int {
	if owner == "" {
		stdlog.Errorf("owner is necessary while listing resources")
		return -1
	}
	if _, ok := rscTypes[rscType]; !ok {
		stdlog.Errorf("invalid resource type: %s", rscType)
		return -1
	}
	selector := JoinSelectorsByAnd(selectors...)
	rt := reflect.TypeOf(r)
	if rt.Kind() == reflect.Ptr {
		rt = rt.Elem()
	}
	count := 0
	s.cache.Range(func(key, value any) bool {
		keyStr := key.(string)
		rscKey := NewRscKeyFromString(keyStr)
		if rscKey.Owner != owner || rscKey.Type != rscType {
			return true
		}
		bs := value.([]byte)
		rv := reflect.New(rt)
		r := rv.Interface().(ThingerResource)
		if err := r.SetValue(bs); err != nil {
			stdlog.WithError(err).Error("failed to set thinger resource value", string(bs))
			return true
		}
		if selector != nil && !selector(r) {
			return true
		}
		count++
		return true
	})
	return count
}

func (s *SqlResourceHub) CountAll(owner string, rscType RscType) int {
	count := 0
	s.cache.Range(func(key, value any) bool {
		keyStr := key.(string)
		rscKey := NewRscKeyFromString(keyStr)
		if rscKey.Owner != owner || rscKey.Type != rscType {
			return true
		}
		count++
		return true
	})
	return count
}

func (s *SqlResourceHub) List(owner string, rscType RscType, rs interface{}, selectors ...ResourceSelector) error {
	return s.ListPage(owner, rscType, rs, 0, maxListItems, selectors...)
}

func (s *SqlResourceHub) ListPage(owner string, rscType RscType, rs interface{}, skip, limit int, selectors ...ResourceSelector) error {
	if owner == "" {
		return fmt.Errorf("owner is necessary while listing resources")
	}
	if _, ok := rscTypes[rscType]; !ok {
		return invalidRscTypeErr(rscType)
	}
	selector := JoinSelectorsByAnd(selectors...)
	// parse and check resources type
	rst := reflect.TypeOf(rs) // *[]*struct
	if rst.Kind() != reflect.Ptr || rst.Elem().Kind() != reflect.Slice || rst.Elem().Elem().Kind() != reflect.Ptr {
		return stderr.ResourceQueryFailure.Error("the receiver of resources must be a slice ptr of resource ptr")
	}
	rsv := reflect.ValueOf(rs).Elem()
	rt := rst.Elem().Elem().Elem()
	cnt := 0 // 已选择出的元素数目
	s.cache.Range(func(key, value any) bool {
		if skip > 0 {
			// 跳过前K个元素
			skip--
			return true
		}
		keyStr := key.(string)
		rscKey := NewRscKeyFromString(keyStr)
		if rscKey.Owner != owner || rscKey.Type != rscType {
			return true
		}
		rv := reflect.New(rt)
		r := rv.Interface().(ThingerResource)
		bs, _ := value.([]byte)
		if err := r.SetValue(bs); err != nil {
			stdlog.WithError(err).Error("failed to set thinger resource value %s", string(bs))
			return true
		}
		if selector != nil && !selector(r) {
			return true
		}
		rsv.Set(reflect.Append(rsv, rv))
		cnt++
		if cnt >= limit {
			// 已达到最大数量，跳过剩余迭代
			return true
		}
		return true
	})
	return nil
}

func (s *SqlResourceHub) ListKVs(prefix string) ([]*KV, error) {
	return nil, stderr.Internal.Error("not implement")
}

func (s *SqlResourceHub) Get(key []byte) ([]byte, error) {
	var dbrsc DbRsc
	err := s.db.Model(&DbRsc{}).Where("rsc_key = ?", string(key)).First(&dbrsc).Error
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get %s", key)
	}
	return []byte(dbrsc.Value), nil
}

func (s *SqlResourceHub) Set(key, value []byte) error {
	keyStr := string(key)
	rscInfo := strings.SplitN(strings.TrimPrefix(keyStr, keyPrefix), keySep, 3)
	return s.db.Model(&DbRsc{}).Where("rsc_key = ?", keyStr).Updates(map[string]string{"owner": rscInfo[0], "type": rscInfo[1], "id": rscInfo[2], "value": string(value)}).Error
}

func (s *SqlResourceHub) Delete(k RscKey) error {
	s.cache.Delete(k.KeyString())
	return s.db.Model(&DbRsc{}).Where("id = ?", k.ID).Delete(&DbRsc{}).Error
}

func (s *SqlResourceHub) RawDelete(k []byte) error {
	keyStr := string(k)
	return s.db.Where("key = ?", keyStr).Delete(&DbRsc{}).Error
}

func (s *SqlResourceHub) Save(r ThingerResource) error {
	if err := r.Key().Valid(); err != nil {
		return err
	}
	r.SetUpdateTime()
	s.cache.Store(r.Key().KeyString(), r.Value())
	dbrsc := thingerResource2DbRsc(r)
	return s.SaveDbRsc(dbrsc)
}

func (s *SqlResourceHub) SaveKV(kv *KV) error {
	return stderr.Internal.Error("not implement save kv in sql hub")
}

func (s *SqlResourceHub) Clean(owner string, rscType RscType) error {
	// TODO implement me
	panic("implement me")
}
func (s *SqlResourceHub) SaveDbRsc(dbrsc *DbRsc) error {
	err := s.db.Save(dbrsc).Error
	if err != nil {
		return stderr.Wrap(err, "failed to save in sql db")
	}
	if len(s.watchFlags) > 0 {
		if s.watchFlags[RscType(dbrsc.Type)] {
			s.changeSignal <- dbrsc
		}
	}
	return nil
}
func thingerResource2DbRsc(r ThingerResource) *DbRsc {
	return &DbRsc{
		RscKey:      r.KeyString(),
		Value:       string(r.Value()),
		ID:          r.RscID(),
		Name:        r.RscName(),
		Description: r.RscDesc(),
		Type:        string(r.Key().Type),
		Owner:       r.RscOwner()}
}
