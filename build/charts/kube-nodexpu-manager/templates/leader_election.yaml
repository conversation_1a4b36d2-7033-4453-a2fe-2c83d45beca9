apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "kube-nodexpu-manager.name" . }}-leader-election-role
  namespace: {{ .Release.Namespace }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "kube-nodexpu-manager.name" . }}-controller-rolebinding
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "kube-nodexpu-manager.name" . }}-leader-election-role
subjects:
- kind: ServiceAccount
  name: {{ include "kube-nodexpu-manager.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
