{{/*
Expand the name of the chart.
*/}}
{{- define "kube-nodexpu-manager.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "kube-nodexpu-manager.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "kube-nodexpu-manager.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "kube-nodexpu-manager.labels" -}}
helm.sh/chart: {{ include "kube-nodexpu-manager.chart" . }}
app.kubernetes.io/name: {{ include "kube-nodexpu-manager.name" . | quote }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{ include "kube-nodexpu-manager.selectorLabels" . }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "kube-nodexpu-manager.selectorLabels" -}}
control-plane: {{ include "kube-nodexpu-manager.name" . }}
{{- end }}

{{- define "kube-nodexpu-manager.serviceAccountName" -}}
{{ include "kube-nodexpu-manager.name" . }}-serviceaccount
{{- end }}

{{/*
Define the pod anti-affinity definition for leader-election high availability
*/}}
{{- define "kube-nodexpu-manager.defaultPodAntiAffinity" -}}
requiredDuringSchedulingIgnoredDuringExecution:
- labelSelector:
    matchExpressions:
    - key: app.kubernetes.io/name
    operator: In
    values:
    - {{ include "kube-nodexpu-manager.name" . | quote }}
  topologyKey: "kubernetes.io/hostname"
{{- end }}

{{- define "kube-nodexpu-manager.webhooks.objectSelector.matchExpression.use-kube-nodexpu-manager" -}}
- key: transwarp.io/use-kube-nodexpu-manager
  operator: In
  values:
  - "true"
{{- end }}

{{- define "kube-nodexpu-manager.webhooks.namespaceSelector.matchExpression.matchName" -}}
- key: kubernetes.io/metadata.name
  operator: NotIn
  values:
  {{- toYaml .Values.whitelistNamespaces | nindent 2 }}
{{- end }}

{{- define "kube-nodexpu-manager.tls" -}}
{{ include "kube-nodexpu-manager.name" . }}-tls
{{- end }}

{{- define "kube-nodexpu-manager.serviceName" -}}
{{ include "kube-nodexpu-manager.name" . }}-service
{{- end }}

{{- define "kube-nodexpu-manager.webhookPortName" -}}
webhook
{{- end }}

{{- define "kube-nodexpu-manager.healthProbePortName" -}}
health-probe
{{- end }}

{{- define "kube-nodexpu-manager.mutatingWebhookName" -}}
{{ include "kube-nodexpu-manager.name" . }}-mutating-webhook
{{- end }}

{{- define "kube-nodexpu-manager.validatingWebhookName" -}}
{{ include "kube-nodexpu-manager.name" . }}-validating-webhook
{{- end }}


{{/*
Get the docker image repo tag of an container.
返回类似如下形式的镜像模板
 ***********/aip/@:sophon-22.04
 ***********:5000/aip-arm/@:sophon-22.04
后续由具体镜像将 @ 替换为具体的镜像仓库即可
e.g.
 image: {{ include "image-tpl" . | replace "@" "vision-backend" }}
*/}}
{{- define "image-tpl" -}}
{{- printf "%s/%s/@:%s" .Values.global.images.registry .Values.global.images.baseRepo .Values.global.images.baseTag }}
{{- end }}
