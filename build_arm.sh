#！/bin/bash
set -e
export DOCKER_REPO_URL="***********"

name=$1
version=$2
basedir=`pwd`

cd "${name}"

IMAGE="${DOCKER_REPO_URL}/aip/arm64-${name}:${version}"
IMAGE_RELEASE="${DOCKER_REPO_URL}/transwarp/arm64-${name}:${version/branch/mlops}"
echo $IMAGE_RELEASE

commit="$version.`date +%m%d`.`git --no-pager show -s --format=%h`"
docker build -f Dockerfile.arm_64 -t ${IMAGE} -t ${IMAGE_RELEASE} --no-cache=true --label version=${commit} --network host .
docker push ${IMAGE}
docker push ${IMAGE_RELEASE}

echo ${IMAGE}
echo ${IMAGE_RELEASE}

cd ${basedir}
