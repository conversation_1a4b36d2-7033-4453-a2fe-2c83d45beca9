apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "expense.fullname" . }}
  labels:
    {{- include "expense.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "expense.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "expense.labels" . | nindent 8 }}
    spec:
      priorityClassName: {{ include "highPC" . }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ .Values.global.account.adminName }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          env:
            - name: RS_EXPENSE.DATASOURCE.DRIVER
              value: mysql
            - name: RS_EXPENSE.DATASOURCE.HOST
              value: {{ if .Values.global.proxysql.enable }}autocv-gateway-service{{ else }}{{ .Values.global.mysql.host | quote }}{{ end }}
            - name: RS_EXPENSE.DATASOURCE.PORT
              value: {{ if .Values.global.proxysql.enable }}"3306"{{ else }}{{ .Values.global.mysql.port | quote }}{{ end }}
            - name: RS_EXPENSE.DATASOURCE.USERNAME
              valueFrom:
                secretKeyRef:
                  key: mysql_username
                  name: llmops-secret
            - name: RS_EXPENSE.DATASOURCE.PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mysql_password
                  name: llmops-secret
            - name: RS_EXPENSE.REDIS.ADDRS
              value: {{ .Values.global.redis.addrs | quote }}
            - name: RS_EXPENSE.REDIS.USERNAME
              valueFrom:
                secretKeyRef:
                  key: redis_username
                  name: llmops-secret
            - name: RS_EXPENSE.REDIS.PASSWORD
              valueFrom:
                secretKeyRef:
                  key: redis_password
                  name: llmops-secret
            - name: RS_EXPENSE.REDIS.DATABASE
              value: {{ .Values.global.redis.database | quote }}
            - name: RS_EXPENSE.REDIS.MASTERNAME
              value: {{ .Values.global.redis.masterName | quote }}
            - name: RS_EXPENSE.KUBECONFIG.PROMETHEUS
              value: {{ .Values.global.prometheusUrl }}
            - name: RS_EXPENSE.NODECONFIG.LABELSELECTOR
              value: {{ .Values.global.workerLabel}}
            - name: RS_EXPENSE.SFS.TYPE
              value: {{ .Values.global.storage.sfs.type}}
            - name: RS_EXPENSE.SFS.JUICEFS.PATH
              value: {{ .Values.global.storage.sfs.juicefs.path}}
            - name: RS_EXPENSE.SFS.JUICEFS.SECRETNAME
              value: {{ .Values.global.storage.sfs.juicefs.secretName}}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image:  {{ template "expense.image" $ }}
          imagePullPolicy: {{ .Values.global.images.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - mountPath: /hippo
              name: sfs-volume
            - mountPath: /sfs
              name: sfs-volume
          readinessProbe:
            httpGet:
              path: /apidocs/
              port: 9527
            initialDelaySeconds: 1
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 5
          livenessProbe:
            httpGet:
              path: /apidocs/
              port: 9527
            initialDelaySeconds: 1
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 5
          lifecycle:
            preStop:
              exec:
                command:
                  - echo
                  - "1"
      volumes:
        - name: sfs-volume
          persistentVolumeClaim:
            claimName: {{ .Values.global.storage.sfs.pvcName }}

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if and .Values.global .Values.global.affinity }}
      affinity:
        {{- toYaml .Values.global.affinity | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
