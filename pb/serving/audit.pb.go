// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/serving/audit.proto

package serving

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	common "transwarp.io/aip/llmops-common/pb/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumerDetail_Type int32

const (
	ConsumerDetail_INNER   ConsumerDetail_Type = 0
	ConsumerDetail_INCOME  ConsumerDetail_Type = 1
	ConsumerDetail_CONSUME ConsumerDetail_Type = 2
)

// Enum value maps for ConsumerDetail_Type.
var (
	ConsumerDetail_Type_name = map[int32]string{
		0: "INNER",
		1: "INCOME",
		2: "CONSUME",
	}
	ConsumerDetail_Type_value = map[string]int32{
		"INNER":   0,
		"INCOME":  1,
		"CONSUME": 2,
	}
)

func (x ConsumerDetail_Type) Enum() *ConsumerDetail_Type {
	p := new(ConsumerDetail_Type)
	*p = x
	return p
}

func (x ConsumerDetail_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumerDetail_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_audit_proto_enumTypes[0].Descriptor()
}

func (ConsumerDetail_Type) Type() protoreflect.EnumType {
	return &file_proto_serving_audit_proto_enumTypes[0]
}

func (x ConsumerDetail_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsumerDetail_Type.Descriptor instead.
func (ConsumerDetail_Type) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{50, 0}
}

// @gotags:description:"服务id"
type GetTokenBarChartReq_Type int32

const (
	GetTokenBarChartReq_ALL    GetTokenBarChartReq_Type = 0
	GetTokenBarChartReq_INPUT  GetTokenBarChartReq_Type = 1
	GetTokenBarChartReq_OUTPUT GetTokenBarChartReq_Type = 2
)

// Enum value maps for GetTokenBarChartReq_Type.
var (
	GetTokenBarChartReq_Type_name = map[int32]string{
		0: "ALL",
		1: "INPUT",
		2: "OUTPUT",
	}
	GetTokenBarChartReq_Type_value = map[string]int32{
		"ALL":    0,
		"INPUT":  1,
		"OUTPUT": 2,
	}
)

func (x GetTokenBarChartReq_Type) Enum() *GetTokenBarChartReq_Type {
	p := new(GetTokenBarChartReq_Type)
	*p = x
	return p
}

func (x GetTokenBarChartReq_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTokenBarChartReq_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_serving_audit_proto_enumTypes[1].Descriptor()
}

func (GetTokenBarChartReq_Type) Type() protoreflect.EnumType {
	return &file_proto_serving_audit_proto_enumTypes[1]
}

func (x GetTokenBarChartReq_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTokenBarChartReq_Type.Descriptor instead.
func (GetTokenBarChartReq_Type) EnumDescriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{63, 0}
}

type DashboardGlobalOverView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstToken *AvgFirstTimeToken   `protobuf:"bytes,1,opt,name=first_token,json=firstToken,proto3" json:"first_token"`
	Base       *DashboardGlobalBase `protobuf:"bytes,2,opt,name=base,proto3" json:"base"`
}

func (x *DashboardGlobalOverView) Reset() {
	*x = DashboardGlobalOverView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardGlobalOverView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardGlobalOverView) ProtoMessage() {}

func (x *DashboardGlobalOverView) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardGlobalOverView.ProtoReflect.Descriptor instead.
func (*DashboardGlobalOverView) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{0}
}

func (x *DashboardGlobalOverView) GetFirstToken() *AvgFirstTimeToken {
	if x != nil {
		return x.FirstToken
	}
	return nil
}

func (x *DashboardGlobalOverView) GetBase() *DashboardGlobalBase {
	if x != nil {
		return x.Base
	}
	return nil
}

type DashboardGlobalDays struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Days []*Days `protobuf:"bytes,1,rep,name=days,proto3" json:"days"`
}

func (x *DashboardGlobalDays) Reset() {
	*x = DashboardGlobalDays{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardGlobalDays) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardGlobalDays) ProtoMessage() {}

func (x *DashboardGlobalDays) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardGlobalDays.ProtoReflect.Descriptor instead.
func (*DashboardGlobalDays) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{1}
}

func (x *DashboardGlobalDays) GetDays() []*Days {
	if x != nil {
		return x.Days
	}
	return nil
}

type Days struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value" description:"总量"`
	 
	Time string `protobuf:"bytes,2,opt,name=time,proto3" json:"time" description:"时间"`
}

func (x *Days) Reset() {
	*x = Days{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Days) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Days) ProtoMessage() {}

func (x *Days) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Days.ProtoReflect.Descriptor instead.
func (*Days) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{2}
}

func (x *Days) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *Days) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

type DashboardGlobalBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	TodayCount int32 `protobuf:"varint,1,opt,name=today_count,json=todayCount,proto3" json:"today_count" description:"当日访问量"`
	 
	YesterdayCount int32 `protobuf:"varint,2,opt,name=yesterday_count,json=yesterdayCount,proto3" json:"yesterday_count" description:"昨日访问量"`
	 
	TodayRtt string `protobuf:"bytes,3,opt,name=today_rtt,json=todayRtt,proto3" json:"today_rtt" description:"当日访问平均耗时"`
	 
	YesterdayRtt string `protobuf:"bytes,4,opt,name=yesterday_rtt,json=yesterdayRtt,proto3" json:"yesterday_rtt" description:"昨天访问平均耗时"`
}

func (x *DashboardGlobalBase) Reset() {
	*x = DashboardGlobalBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardGlobalBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardGlobalBase) ProtoMessage() {}

func (x *DashboardGlobalBase) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardGlobalBase.ProtoReflect.Descriptor instead.
func (*DashboardGlobalBase) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{3}
}

func (x *DashboardGlobalBase) GetTodayCount() int32 {
	if x != nil {
		return x.TodayCount
	}
	return 0
}

func (x *DashboardGlobalBase) GetYesterdayCount() int32 {
	if x != nil {
		return x.YesterdayCount
	}
	return 0
}

func (x *DashboardGlobalBase) GetTodayRtt() string {
	if x != nil {
		return x.TodayRtt
	}
	return ""
}

func (x *DashboardGlobalBase) GetYesterdayRtt() string {
	if x != nil {
		return x.YesterdayRtt
	}
	return ""
}

type AvgFirstTimeTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"项目id"`
	 
	TenantId string `protobuf:"bytes,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id" description:"租户id"`
}

func (x *AvgFirstTimeTokenReq) Reset() {
	*x = AvgFirstTimeTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvgFirstTimeTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvgFirstTimeTokenReq) ProtoMessage() {}

func (x *AvgFirstTimeTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvgFirstTimeTokenReq.ProtoReflect.Descriptor instead.
func (*AvgFirstTimeTokenReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{4}
}

func (x *AvgFirstTimeTokenReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AvgFirstTimeTokenReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

type ListBillingRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"分页参数"`
}

func (x *ListBillingRuleReq) Reset() {
	*x = ListBillingRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBillingRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBillingRuleReq) ProtoMessage() {}

func (x *ListBillingRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBillingRuleReq.ProtoReflect.Descriptor instead.
func (*ListBillingRuleReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{5}
}

func (x *ListBillingRuleReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type BillingRuleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	BillingRules []*BillingRule `protobuf:"bytes,1,rep,name=billing_rules,json=billingRules,proto3" json:"billing_rules" description:"列表"`
}

func (x *BillingRuleList) Reset() {
	*x = BillingRuleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingRuleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingRuleList) ProtoMessage() {}

func (x *BillingRuleList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingRuleList.ProtoReflect.Descriptor instead.
func (*BillingRuleList) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{6}
}

func (x *BillingRuleList) GetBillingRules() []*BillingRule {
	if x != nil {
		return x.BillingRules
	}
	return nil
}

type BillingRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ServiceId string `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`
	 
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name" description:"服务名称"`
	 
	Desc string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc" description:"描述"`
	 
	BillingConfig *common.BillingConfig `protobuf:"bytes,5,opt,name=billing_config,json=billingConfig,proto3" json:"billing_config" description:"计费单价"`
}

func (x *BillingRule) Reset() {
	*x = BillingRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingRule) ProtoMessage() {}

func (x *BillingRule) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingRule.ProtoReflect.Descriptor instead.
func (*BillingRule) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{7}
}

func (x *BillingRule) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *BillingRule) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *BillingRule) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BillingRule) GetBillingConfig() *common.BillingConfig {
	if x != nil {
		return x.BillingConfig
	}
	return nil
}

type OperateLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PageReq *common.PageReq `protobuf:"bytes,1,opt,name=pageReq,proto3" json:"pageReq" description:"分页参数"`
	 
	Start int64 `protobuf:"varint,2,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,3,opt,name=end,proto3" json:"end" description:"结束时间"`
}

func (x *OperateLogReq) Reset() {
	*x = OperateLogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateLogReq) ProtoMessage() {}

func (x *OperateLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateLogReq.ProtoReflect.Descriptor instead.
func (*OperateLogReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{8}
}

func (x *OperateLogReq) GetPageReq() *common.PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *OperateLogReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *OperateLogReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type OperateLogPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Size int64 `protobuf:"varint,1,opt,name=size,proto3" json:"size" description:"操作日志数量"`
	 
	OperateLog []*OperateLog `protobuf:"bytes,2,rep,name=operateLog,proto3" json:"operateLog" description:"操作日志数组"`
}

func (x *OperateLogPage) Reset() {
	*x = OperateLogPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateLogPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateLogPage) ProtoMessage() {}

func (x *OperateLogPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateLogPage.ProtoReflect.Descriptor instead.
func (*OperateLogPage) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{9}
}

func (x *OperateLogPage) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *OperateLogPage) GetOperateLog() []*OperateLog {
	if x != nil {
		return x.OperateLog
	}
	return nil
}

type OperateLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"唯一标识"`
	 
	Operator string `protobuf:"bytes,2,opt,name=Operator,proto3" json:"Operator" description:"操作人员"`
	 
	OperateTime int64 `protobuf:"varint,3,opt,name=OperateTime,proto3" json:"OperateTime" description:"操作时间"`
	 
	OperateType string `protobuf:"bytes,4,opt,name=OperateType,proto3" json:"OperateType" description:"操作类型"`
	 
	OperateModule string `protobuf:"bytes,5,opt,name=OperateModule,proto3" json:"OperateModule" description:"操作模块"`
	 
	OperateResource string `protobuf:"bytes,6,opt,name=OperateResource,proto3" json:"OperateResource" description:"操作的资源"`
	 
	OperateBehavior string `protobuf:"bytes,7,opt,name=OperateBehavior,proto3" json:"OperateBehavior" description:"操作的具体行为"`
	 
	OperateResult string `protobuf:"bytes,8,opt,name=OperateResult,proto3" json:"OperateResult" description:"操作的结果"`
}

func (x *OperateLog) Reset() {
	*x = OperateLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateLog) ProtoMessage() {}

func (x *OperateLog) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateLog.ProtoReflect.Descriptor instead.
func (*OperateLog) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{10}
}

func (x *OperateLog) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OperateLog) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *OperateLog) GetOperateTime() int64 {
	if x != nil {
		return x.OperateTime
	}
	return 0
}

func (x *OperateLog) GetOperateType() string {
	if x != nil {
		return x.OperateType
	}
	return ""
}

func (x *OperateLog) GetOperateModule() string {
	if x != nil {
		return x.OperateModule
	}
	return ""
}

func (x *OperateLog) GetOperateResource() string {
	if x != nil {
		return x.OperateResource
	}
	return ""
}

func (x *OperateLog) GetOperateBehavior() string {
	if x != nil {
		return x.OperateBehavior
	}
	return ""
}

func (x *OperateLog) GetOperateResult() string {
	if x != nil {
		return x.OperateResult
	}
	return ""
}

type PortalModuleStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelCount         int64 `protobuf:"varint,1,opt,name=modelCount,proto3" json:"modelCount"`
	ModelChange        int64 `protobuf:"varint,2,opt,name=modelChange,proto3" json:"modelChange"`
	GraphCount         int64 `protobuf:"varint,3,opt,name=graphCount,proto3" json:"graphCount"`
	GraphChange        int64 `protobuf:"varint,4,opt,name=graphChange,proto3" json:"graphChange"`
	NodeCount          int64 `protobuf:"varint,5,opt,name=nodeCount,proto3" json:"nodeCount"`
	NodeChange         int64 `protobuf:"varint,6,opt,name=nodeChange,proto3" json:"nodeChange"`
	DataCount          int64 `protobuf:"varint,7,opt,name=dataCount,proto3" json:"dataCount"`
	DataChange         int64 `protobuf:"varint,8,opt,name=dataChange,proto3" json:"dataChange"`
	SuccessView        int64 `protobuf:"varint,9,opt,name=successView,proto3" json:"successView"`
	FailureView        int64 `protobuf:"varint,10,opt,name=failureView,proto3" json:"failureView"`
	BatchPredictCount  int64 `protobuf:"varint,11,opt,name=batchPredictCount,proto3" json:"batchPredictCount"`
	BatchPredictChange int64 `protobuf:"varint,12,opt,name=batchPredictChange,proto3" json:"batchPredictChange"`
}

func (x *PortalModuleStatistics) Reset() {
	*x = PortalModuleStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortalModuleStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortalModuleStatistics) ProtoMessage() {}

func (x *PortalModuleStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortalModuleStatistics.ProtoReflect.Descriptor instead.
func (*PortalModuleStatistics) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{11}
}

func (x *PortalModuleStatistics) GetModelCount() int64 {
	if x != nil {
		return x.ModelCount
	}
	return 0
}

func (x *PortalModuleStatistics) GetModelChange() int64 {
	if x != nil {
		return x.ModelChange
	}
	return 0
}

func (x *PortalModuleStatistics) GetGraphCount() int64 {
	if x != nil {
		return x.GraphCount
	}
	return 0
}

func (x *PortalModuleStatistics) GetGraphChange() int64 {
	if x != nil {
		return x.GraphChange
	}
	return 0
}

func (x *PortalModuleStatistics) GetNodeCount() int64 {
	if x != nil {
		return x.NodeCount
	}
	return 0
}

func (x *PortalModuleStatistics) GetNodeChange() int64 {
	if x != nil {
		return x.NodeChange
	}
	return 0
}

func (x *PortalModuleStatistics) GetDataCount() int64 {
	if x != nil {
		return x.DataCount
	}
	return 0
}

func (x *PortalModuleStatistics) GetDataChange() int64 {
	if x != nil {
		return x.DataChange
	}
	return 0
}

func (x *PortalModuleStatistics) GetSuccessView() int64 {
	if x != nil {
		return x.SuccessView
	}
	return 0
}

func (x *PortalModuleStatistics) GetFailureView() int64 {
	if x != nil {
		return x.FailureView
	}
	return 0
}

func (x *PortalModuleStatistics) GetBatchPredictCount() int64 {
	if x != nil {
		return x.BatchPredictCount
	}
	return 0
}

func (x *PortalModuleStatistics) GetBatchPredictChange() int64 {
	if x != nil {
		return x.BatchPredictChange
	}
	return 0
}

type OverviewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ServiceId string `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId" description:"服务id"`
	 
	ServiceVersion string `protobuf:"bytes,2,opt,name=serviceVersion,proto3" json:"serviceVersion" description:"服务版本id"`
}

func (x *OverviewReq) Reset() {
	*x = OverviewReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverviewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverviewReq) ProtoMessage() {}

func (x *OverviewReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverviewReq.ProtoReflect.Descriptor instead.
func (*OverviewReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{12}
}

func (x *OverviewReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *OverviewReq) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

type FeatureDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Feature string `protobuf:"bytes,1,opt,name=feature,proto3" json:"feature" description:"特征列名称"`
	 
	Min float64 `protobuf:"fixed64,2,opt,name=min,proto3" json:"min" description:"特征列最小值"`
	 
	Max float64 `protobuf:"fixed64,3,opt,name=max,proto3" json:"max" description:"特征列最大值"`
	 
	Count int32 `protobuf:"varint,4,opt,name=count,proto3" json:"count" description:"特征列总数量"`
	 
	Bucket []*FeatureBucket `protobuf:"bytes,5,rep,name=bucket,proto3" json:"bucket" description:"特征分布数组"`
}

func (x *FeatureDistribution) Reset() {
	*x = FeatureDistribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureDistribution) ProtoMessage() {}

func (x *FeatureDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureDistribution.ProtoReflect.Descriptor instead.
func (*FeatureDistribution) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{13}
}

func (x *FeatureDistribution) GetFeature() string {
	if x != nil {
		return x.Feature
	}
	return ""
}

func (x *FeatureDistribution) GetMin() float64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *FeatureDistribution) GetMax() float64 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *FeatureDistribution) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *FeatureDistribution) GetBucket() []*FeatureBucket {
	if x != nil {
		return x.Bucket
	}
	return nil
}

// tips: left close right open
type FeatureBucket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Left float64 `protobuf:"fixed64,1,opt,name=left,proto3" json:"left" description:"特征区间最小值，包含"`
	 
	Right float64 `protobuf:"fixed64,2,opt,name=right,proto3" json:"right" description:"特征区间最大值，不包含"`
	 
	Count int32 `protobuf:"varint,3,opt,name=count,proto3" json:"count" description:"特征区间数据量"`
}

func (x *FeatureBucket) Reset() {
	*x = FeatureBucket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureBucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureBucket) ProtoMessage() {}

func (x *FeatureBucket) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureBucket.ProtoReflect.Descriptor instead.
func (*FeatureBucket) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{14}
}

func (x *FeatureBucket) GetLeft() float64 {
	if x != nil {
		return x.Left
	}
	return 0
}

func (x *FeatureBucket) GetRight() float64 {
	if x != nil {
		return x.Right
	}
	return 0
}

func (x *FeatureBucket) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type TrainingDataDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Distributions []*FeatureDistribution `protobuf:"bytes,1,rep,name=distributions,proto3" json:"distributions" description:"训练数据特征分布"`
}

func (x *TrainingDataDistribution) Reset() {
	*x = TrainingDataDistribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainingDataDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainingDataDistribution) ProtoMessage() {}

func (x *TrainingDataDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainingDataDistribution.ProtoReflect.Descriptor instead.
func (*TrainingDataDistribution) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{15}
}

func (x *TrainingDataDistribution) GetDistributions() []*FeatureDistribution {
	if x != nil {
		return x.Distributions
	}
	return nil
}

type DeviationIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ServiceId string `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId" description:"服务id"`
	 
	ServiceVersion string `protobuf:"bytes,2,opt,name=serviceVersion,proto3" json:"serviceVersion" description:"服务版本id"`
	 
	Feature string `protobuf:"bytes,3,opt,name=feature,proto3" json:"feature" description:"特征列名称"`
	 
	Start int64 `protobuf:"varint,4,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,5,opt,name=end,proto3" json:"end" description:"结束时间"`
}

func (x *DeviationIndexReq) Reset() {
	*x = DeviationIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviationIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviationIndexReq) ProtoMessage() {}

func (x *DeviationIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviationIndexReq.ProtoReflect.Descriptor instead.
func (*DeviationIndexReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{16}
}

func (x *DeviationIndexReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *DeviationIndexReq) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

func (x *DeviationIndexReq) GetFeature() string {
	if x != nil {
		return x.Feature
	}
	return ""
}

func (x *DeviationIndexReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *DeviationIndexReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type DeviationStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId"`
}

func (x *DeviationStateReq) Reset() {
	*x = DeviationStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviationStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviationStateReq) ProtoMessage() {}

func (x *DeviationStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviationStateReq.ProtoReflect.Descriptor instead.
func (*DeviationStateReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{17}
}

func (x *DeviationStateReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type ServiceVersionDeviationForShow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Date []string `protobuf:"bytes,1,rep,name=date,proto3" json:"date" description:"日期"`
	 
	InputStatistic []*ServiceInputStatistic `protobuf:"bytes,2,rep,name=inputStatistic,proto3" json:"inputStatistic" description:"输入数据统计"`
	 
	OutputStatistic []*ServiceOutputStatistic `protobuf:"bytes,3,rep,name=outputStatistic,proto3" json:"outputStatistic" description:"输出数据统计"`
	 
	InputDistributionStatistic map[string]*InputDistributionStatistic `protobuf:"bytes,4,rep,name=inputDistributionStatistic,proto3" json:"inputDistributionStatistic" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"输入参数分布对比"`
	 
	InputDeviation []*ServiceInputDeviationStatistic `protobuf:"bytes,5,rep,name=inputDeviation,proto3" json:"inputDeviation" description:"输入参数偏移指标"`
}

func (x *ServiceVersionDeviationForShow) Reset() {
	*x = ServiceVersionDeviationForShow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceVersionDeviationForShow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceVersionDeviationForShow) ProtoMessage() {}

func (x *ServiceVersionDeviationForShow) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceVersionDeviationForShow.ProtoReflect.Descriptor instead.
func (*ServiceVersionDeviationForShow) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceVersionDeviationForShow) GetDate() []string {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ServiceVersionDeviationForShow) GetInputStatistic() []*ServiceInputStatistic {
	if x != nil {
		return x.InputStatistic
	}
	return nil
}

func (x *ServiceVersionDeviationForShow) GetOutputStatistic() []*ServiceOutputStatistic {
	if x != nil {
		return x.OutputStatistic
	}
	return nil
}

func (x *ServiceVersionDeviationForShow) GetInputDistributionStatistic() map[string]*InputDistributionStatistic {
	if x != nil {
		return x.InputDistributionStatistic
	}
	return nil
}

func (x *ServiceVersionDeviationForShow) GetInputDeviation() []*ServiceInputDeviationStatistic {
	if x != nil {
		return x.InputDeviation
	}
	return nil
}

type ServiceVersionDeviationForSave struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date" description:"日期"`
	 
	InputStatistic *ServiceInputStatistic `protobuf:"bytes,2,opt,name=inputStatistic,proto3" json:"inputStatistic" description:"输入数据统计"`
	 
	OutputStatistic *ServiceOutputStatistic `protobuf:"bytes,3,opt,name=outputStatistic,proto3" json:"outputStatistic" description:"输出数据统计"`
	 
	InputDistributionStatistic map[string]*InputDistributionStatistic `protobuf:"bytes,4,rep,name=inputDistributionStatistic,proto3" json:"inputDistributionStatistic" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"输入参数分布对比"`
	 
	InputDeviation *ServiceInputDeviationStatistic `protobuf:"bytes,5,opt,name=inputDeviation,proto3" json:"inputDeviation" description:"输入参数各特征列偏移指标"`
}

func (x *ServiceVersionDeviationForSave) Reset() {
	*x = ServiceVersionDeviationForSave{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceVersionDeviationForSave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceVersionDeviationForSave) ProtoMessage() {}

func (x *ServiceVersionDeviationForSave) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceVersionDeviationForSave.ProtoReflect.Descriptor instead.
func (*ServiceVersionDeviationForSave) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{19}
}

func (x *ServiceVersionDeviationForSave) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *ServiceVersionDeviationForSave) GetInputStatistic() *ServiceInputStatistic {
	if x != nil {
		return x.InputStatistic
	}
	return nil
}

func (x *ServiceVersionDeviationForSave) GetOutputStatistic() *ServiceOutputStatistic {
	if x != nil {
		return x.OutputStatistic
	}
	return nil
}

func (x *ServiceVersionDeviationForSave) GetInputDistributionStatistic() map[string]*InputDistributionStatistic {
	if x != nil {
		return x.InputDistributionStatistic
	}
	return nil
}

func (x *ServiceVersionDeviationForSave) GetInputDeviation() *ServiceInputDeviationStatistic {
	if x != nil {
		return x.InputDeviation
	}
	return nil
}

type ServiceInputStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Input map[string]*InputStatistic `protobuf:"bytes,1,rep,name=input,proto3" json:"input" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"输入特征数据统计"`
}

func (x *ServiceInputStatistic) Reset() {
	*x = ServiceInputStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInputStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInputStatistic) ProtoMessage() {}

func (x *ServiceInputStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInputStatistic.ProtoReflect.Descriptor instead.
func (*ServiceInputStatistic) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{20}
}

func (x *ServiceInputStatistic) GetInput() map[string]*InputStatistic {
	if x != nil {
		return x.Input
	}
	return nil
}

type ServiceOutputStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Count map[string]int32 `protobuf:"bytes,1,rep,name=count,proto3" json:"count" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3" description:"输出数据统计"`
}

func (x *ServiceOutputStatistic) Reset() {
	*x = ServiceOutputStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceOutputStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOutputStatistic) ProtoMessage() {}

func (x *ServiceOutputStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOutputStatistic.ProtoReflect.Descriptor instead.
func (*ServiceOutputStatistic) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{21}
}

func (x *ServiceOutputStatistic) GetCount() map[string]int32 {
	if x != nil {
		return x.Count
	}
	return nil
}

type InputDistributionStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Area []string `protobuf:"bytes,1,rep,name=area,proto3" json:"area" description:"输入特征分布范围"`
	 
	Train []int32 `protobuf:"varint,2,rep,packed,name=train,proto3" json:"train" description:"训练集输入特征范围内数量"`
	 
	Predict []int32 `protobuf:"varint,3,rep,packed,name=predict,proto3" json:"predict" description:"预测集输入特征范围内数量"`
}

func (x *InputDistributionStatistic) Reset() {
	*x = InputDistributionStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputDistributionStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputDistributionStatistic) ProtoMessage() {}

func (x *InputDistributionStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputDistributionStatistic.ProtoReflect.Descriptor instead.
func (*InputDistributionStatistic) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{22}
}

func (x *InputDistributionStatistic) GetArea() []string {
	if x != nil {
		return x.Area
	}
	return nil
}

func (x *InputDistributionStatistic) GetTrain() []int32 {
	if x != nil {
		return x.Train
	}
	return nil
}

func (x *InputDistributionStatistic) GetPredict() []int32 {
	if x != nil {
		return x.Predict
	}
	return nil
}

type InputDeviationStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Psi float64 `protobuf:"fixed64,1,opt,name=psi,proto3" json:"psi"`
	Js  float64 `protobuf:"fixed64,2,opt,name=js,proto3" json:"js"`
}

func (x *InputDeviationStatistic) Reset() {
	*x = InputDeviationStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputDeviationStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputDeviationStatistic) ProtoMessage() {}

func (x *InputDeviationStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputDeviationStatistic.ProtoReflect.Descriptor instead.
func (*InputDeviationStatistic) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{23}
}

func (x *InputDeviationStatistic) GetPsi() float64 {
	if x != nil {
		return x.Psi
	}
	return 0
}

func (x *InputDeviationStatistic) GetJs() float64 {
	if x != nil {
		return x.Js
	}
	return 0
}

type ServiceInputDeviationStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	DeviationStatistic map[string]*InputDeviationStatistic `protobuf:"bytes,1,rep,name=deviationStatistic,proto3" json:"deviationStatistic" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"输入参数各特征列偏移指标"`
}

func (x *ServiceInputDeviationStatistic) Reset() {
	*x = ServiceInputDeviationStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInputDeviationStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInputDeviationStatistic) ProtoMessage() {}

func (x *ServiceInputDeviationStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInputDeviationStatistic.ProtoReflect.Descriptor instead.
func (*ServiceInputDeviationStatistic) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{24}
}

func (x *ServiceInputDeviationStatistic) GetDeviationStatistic() map[string]*InputDeviationStatistic {
	if x != nil {
		return x.DeviationStatistic
	}
	return nil
}

type InputStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min               float64 `protobuf:"fixed64,1,opt,name=min,proto3" json:"min"`
	Max               float64 `protobuf:"fixed64,2,opt,name=max,proto3" json:"max"`
	Avg               float64 `protobuf:"fixed64,3,opt,name=avg,proto3" json:"avg"`
	StandardDeviation float64 `protobuf:"fixed64,4,opt,name=standardDeviation,proto3" json:"standardDeviation"`
	Count             int32   `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
}

func (x *InputStatistic) Reset() {
	*x = InputStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputStatistic) ProtoMessage() {}

func (x *InputStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputStatistic.ProtoReflect.Descriptor instead.
func (*InputStatistic) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{25}
}

func (x *InputStatistic) GetMin() float64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *InputStatistic) GetMax() float64 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *InputStatistic) GetAvg() float64 {
	if x != nil {
		return x.Avg
	}
	return 0
}

func (x *InputStatistic) GetStandardDeviation() float64 {
	if x != nil {
		return x.StandardDeviation
	}
	return 0
}

func (x *InputStatistic) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type DeviationIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Psi float32 `protobuf:"fixed32,1,opt,name=psi,proto3" json:"psi"`
	Js  float32 `protobuf:"fixed32,2,opt,name=js,proto3" json:"js"`
}

func (x *DeviationIndex) Reset() {
	*x = DeviationIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviationIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviationIndex) ProtoMessage() {}

func (x *DeviationIndex) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviationIndex.ProtoReflect.Descriptor instead.
func (*DeviationIndex) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{26}
}

func (x *DeviationIndex) GetPsi() float32 {
	if x != nil {
		return x.Psi
	}
	return 0
}

func (x *DeviationIndex) GetJs() float32 {
	if x != nil {
		return x.Js
	}
	return 0
}

type ResourceUsageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type" description:"资源利用率类别, cpu/memory"`
	 
	TimeReq *TimeReq `protobuf:"bytes,2,opt,name=timeReq,proto3" json:"timeReq" description:"时间参数"`
}

func (x *ResourceUsageReq) Reset() {
	*x = ResourceUsageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceUsageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUsageReq) ProtoMessage() {}

func (x *ResourceUsageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUsageReq.ProtoReflect.Descriptor instead.
func (*ResourceUsageReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{27}
}

func (x *ResourceUsageReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResourceUsageReq) GetTimeReq() *TimeReq {
	if x != nil {
		return x.TimeReq
	}
	return nil
}

type GetSvcMlopsGpusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeAndGpus map[string]*structpb.ListValue `protobuf:"bytes,1,rep,name=NodeAndGpus,proto3" json:"NodeAndGpus" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetSvcMlopsGpusRsp) Reset() {
	*x = GetSvcMlopsGpusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSvcMlopsGpusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSvcMlopsGpusRsp) ProtoMessage() {}

func (x *GetSvcMlopsGpusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSvcMlopsGpusRsp.ProtoReflect.Descriptor instead.
func (*GetSvcMlopsGpusRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{28}
}

func (x *GetSvcMlopsGpusRsp) GetNodeAndGpus() map[string]*structpb.ListValue {
	if x != nil {
		return x.NodeAndGpus
	}
	return nil
}

type GpuResourceUsageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      string   `protobuf:"bytes,1,opt,name=type,proto3" json:"type"`
	TimeReq   *TimeReq `protobuf:"bytes,2,opt,name=timeReq,proto3" json:"timeReq"`
	ClusterId string   `protobuf:"bytes,3,opt,name=clusterId,proto3" json:"clusterId"`
	GpuId     string   `protobuf:"bytes,4,opt,name=gpuId,proto3" json:"gpuId"`
	Node      string   `protobuf:"bytes,5,opt,name=node,proto3" json:"node"`
}

func (x *GpuResourceUsageReq) Reset() {
	*x = GpuResourceUsageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GpuResourceUsageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GpuResourceUsageReq) ProtoMessage() {}

func (x *GpuResourceUsageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GpuResourceUsageReq.ProtoReflect.Descriptor instead.
func (*GpuResourceUsageReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{29}
}

func (x *GpuResourceUsageReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GpuResourceUsageReq) GetTimeReq() *TimeReq {
	if x != nil {
		return x.TimeReq
	}
	return nil
}

func (x *GpuResourceUsageReq) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *GpuResourceUsageReq) GetGpuId() string {
	if x != nil {
		return x.GpuId
	}
	return ""
}

func (x *GpuResourceUsageReq) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

type GpuCurveChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*GpuCurveChart_Value `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	XAxis []string               `protobuf:"bytes,3,rep,name=xAxis,proto3" json:"xAxis"`
}

func (x *GpuCurveChart) Reset() {
	*x = GpuCurveChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GpuCurveChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GpuCurveChart) ProtoMessage() {}

func (x *GpuCurveChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GpuCurveChart.ProtoReflect.Descriptor instead.
func (*GpuCurveChart) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{30}
}

func (x *GpuCurveChart) GetData() []*GpuCurveChart_Value {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GpuCurveChart) GetXAxis() []string {
	if x != nil {
		return x.XAxis
	}
	return nil
}

type TimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end" description:"结束时间"`
	 
	Step int64 `protobuf:"varint,3,opt,name=step,proto3" json:"step" description:"查询步长,单位s"`
	 
	ServiceId string `protobuf:"bytes,4,opt,name=serviceId,proto3" json:"serviceId" description:"服务id"`
	 
	ServiceVersion string `protobuf:"bytes,5,opt,name=serviceVersion,proto3" json:"serviceVersion" description:"服务版本id"`
}

func (x *TimeReq) Reset() {
	*x = TimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeReq) ProtoMessage() {}

func (x *TimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeReq.ProtoReflect.Descriptor instead.
func (*TimeReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{31}
}

func (x *TimeReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *TimeReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *TimeReq) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *TimeReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *TimeReq) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

type DashboardOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	OnlineService int32 `protobuf:"varint,1,opt,name=onlineService,proto3" json:"onlineService" description:"上线中的服务数量"`
	 
	OfflineService int32 `protobuf:"varint,2,opt,name=offlineService,proto3" json:"offlineService" description:"同比昨日上线中的服务数量"`
	 
	NewOnlineService int32 `protobuf:"varint,3,opt,name=newOnlineService,proto3" json:"newOnlineService" description:"下线中的服务数量"`
	 
	NewOfflineService int32 `protobuf:"varint,4,opt,name=newOfflineService,proto3" json:"newOfflineService" description:"同比昨日下线中的服务数量"`
}

func (x *DashboardOverview) Reset() {
	*x = DashboardOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardOverview) ProtoMessage() {}

func (x *DashboardOverview) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardOverview.ProtoReflect.Descriptor instead.
func (*DashboardOverview) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{32}
}

func (x *DashboardOverview) GetOnlineService() int32 {
	if x != nil {
		return x.OnlineService
	}
	return 0
}

func (x *DashboardOverview) GetOfflineService() int32 {
	if x != nil {
		return x.OfflineService
	}
	return 0
}

func (x *DashboardOverview) GetNewOnlineService() int32 {
	if x != nil {
		return x.NewOnlineService
	}
	return 0
}

func (x *DashboardOverview) GetNewOfflineService() int32 {
	if x != nil {
		return x.NewOfflineService
	}
	return 0
}

type DashboardResourceUsageCurveChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Value []float32 `protobuf:"fixed32,1,rep,packed,name=value,proto3" json:"value" description:"服务资源利用率纵坐标"`
	 
	XAxis []string `protobuf:"bytes,2,rep,name=xAxis,proto3" json:"xAxis" description:"服务资源利用率横坐标"`
}

func (x *DashboardResourceUsageCurveChart) Reset() {
	*x = DashboardResourceUsageCurveChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResourceUsageCurveChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResourceUsageCurveChart) ProtoMessage() {}

func (x *DashboardResourceUsageCurveChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResourceUsageCurveChart.ProtoReflect.Descriptor instead.
func (*DashboardResourceUsageCurveChart) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{33}
}

func (x *DashboardResourceUsageCurveChart) GetValue() []float32 {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *DashboardResourceUsageCurveChart) GetXAxis() []string {
	if x != nil {
		return x.XAxis
	}
	return nil
}

type DashboardCurveChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Data []*DashboardCurveChart_Value `protobuf:"bytes,2,rep,name=data,proto3" json:"data" description:"服务运行趋势纵坐标"`
	 
	XAxis []string `protobuf:"bytes,3,rep,name=xAxis,proto3" json:"xAxis" description:"服务运行趋势横坐标"`
}

func (x *DashboardCurveChart) Reset() {
	*x = DashboardCurveChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardCurveChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardCurveChart) ProtoMessage() {}

func (x *DashboardCurveChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardCurveChart.ProtoReflect.Descriptor instead.
func (*DashboardCurveChart) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{34}
}

func (x *DashboardCurveChart) GetData() []*DashboardCurveChart_Value {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DashboardCurveChart) GetXAxis() []string {
	if x != nil {
		return x.XAxis
	}
	return nil
}

type DashboardRequestOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Pv int32 `protobuf:"varint,1,opt,name=pv,proto3" json:"pv" description:"当日访问量"`
	 
	NewPV int32 `protobuf:"varint,2,opt,name=newPV,proto3" json:"newPV" description:"同比昨日访问量"`
	 
	Rtt string `protobuf:"bytes,3,opt,name=rtt,proto3" json:"rtt" description:"当日访问平均耗时"`
	 
	Trend string `protobuf:"bytes,4,opt,name=trend,proto3" json:"trend" description:"同比昨日访问耗时百分比"`
}

func (x *DashboardRequestOverview) Reset() {
	*x = DashboardRequestOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardRequestOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardRequestOverview) ProtoMessage() {}

func (x *DashboardRequestOverview) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardRequestOverview.ProtoReflect.Descriptor instead.
func (*DashboardRequestOverview) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{35}
}

func (x *DashboardRequestOverview) GetPv() int32 {
	if x != nil {
		return x.Pv
	}
	return 0
}

func (x *DashboardRequestOverview) GetNewPV() int32 {
	if x != nil {
		return x.NewPV
	}
	return 0
}

func (x *DashboardRequestOverview) GetRtt() string {
	if x != nil {
		return x.Rtt
	}
	return ""
}

func (x *DashboardRequestOverview) GetTrend() string {
	if x != nil {
		return x.Trend
	}
	return ""
}

type DashboardRequestBarChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Data []*DashboardRequestBarChart_Value `protobuf:"bytes,1,rep,name=data,proto3" json:"data" description:"访问量/平均耗时柱状图数据"`
}

func (x *DashboardRequestBarChart) Reset() {
	*x = DashboardRequestBarChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardRequestBarChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardRequestBarChart) ProtoMessage() {}

func (x *DashboardRequestBarChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardRequestBarChart.ProtoReflect.Descriptor instead.
func (*DashboardRequestBarChart) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{36}
}

func (x *DashboardRequestBarChart) GetData() []*DashboardRequestBarChart_Value {
	if x != nil {
		return x.Data
	}
	return nil
}

type DashboardVisitRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Visit []*DashboardVisitRank_Value `protobuf:"bytes,1,rep,name=visit,proto3" json:"visit" description:"访问量排行"`
	 
	Time []*DashboardVisitRank_RequestTime `protobuf:"bytes,2,rep,name=time,proto3" json:"time" description:"访问耗时排行"`
}

func (x *DashboardVisitRank) Reset() {
	*x = DashboardVisitRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardVisitRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardVisitRank) ProtoMessage() {}

func (x *DashboardVisitRank) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardVisitRank.ProtoReflect.Descriptor instead.
func (*DashboardVisitRank) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{37}
}

func (x *DashboardVisitRank) GetVisit() []*DashboardVisitRank_Value {
	if x != nil {
		return x.Visit
	}
	return nil
}

func (x *DashboardVisitRank) GetTime() []*DashboardVisitRank_RequestTime {
	if x != nil {
		return x.Time
	}
	return nil
}

type DashboardVisitHotGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	HotGraph []*DashboardVisitHotGraph_HotGraph `protobuf:"bytes,1,rep,name=hotGraph,proto3" json:"hotGraph" description:"热点图数据"`
}

func (x *DashboardVisitHotGraph) Reset() {
	*x = DashboardVisitHotGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardVisitHotGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardVisitHotGraph) ProtoMessage() {}

func (x *DashboardVisitHotGraph) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardVisitHotGraph.ProtoReflect.Descriptor instead.
func (*DashboardVisitHotGraph) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{38}
}

func (x *DashboardVisitHotGraph) GetHotGraph() []*DashboardVisitHotGraph_HotGraph {
	if x != nil {
		return x.HotGraph
	}
	return nil
}

type ServiceVisitRecordPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Size int32 `protobuf:"varint,1,opt,name=size,proto3" json:"size" description:"总数量"`
	 
	Records []*ServiceVisitRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records" description:"访问记录数组"`
	 
	ServiceNameMap map[string]string `protobuf:"bytes,3,rep,name=serviceNameMap,proto3" json:"serviceNameMap" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"服务id-name映射"`
}

func (x *ServiceVisitRecordPage) Reset() {
	*x = ServiceVisitRecordPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceVisitRecordPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceVisitRecordPage) ProtoMessage() {}

func (x *ServiceVisitRecordPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceVisitRecordPage.ProtoReflect.Descriptor instead.
func (*ServiceVisitRecordPage) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{39}
}

func (x *ServiceVisitRecordPage) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ServiceVisitRecordPage) GetRecords() []*ServiceVisitRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ServiceVisitRecordPage) GetServiceNameMap() map[string]string {
	if x != nil {
		return x.ServiceNameMap
	}
	return nil
}

type StatsVisitRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Total *VisitCount `protobuf:"bytes,1,opt,name=total,proto3" json:"total" description:"记录该时间段的访问总量"`
	 
	Detail []*VisitCount `protobuf:"bytes,2,rep,name=detail,proto3" json:"detail" description:"记录该时间段内每天的访问量"`
}

func (x *StatsVisitRecordRsp) Reset() {
	*x = StatsVisitRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsVisitRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsVisitRecordRsp) ProtoMessage() {}

func (x *StatsVisitRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsVisitRecordRsp.ProtoReflect.Descriptor instead.
func (*StatsVisitRecordRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{40}
}

func (x *StatsVisitRecordRsp) GetTotal() *VisitCount {
	if x != nil {
		return x.Total
	}
	return nil
}

func (x *StatsVisitRecordRsp) GetDetail() []*VisitCount {
	if x != nil {
		return x.Detail
	}
	return nil
}

type VisitCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelCube    int32  `protobuf:"varint,1,opt,name=modelCube,proto3" json:"modelCube"`
	AppCube      int32  `protobuf:"varint,2,opt,name=appCube,proto3" json:"appCube"`
	CustomWidget int32  `protobuf:"varint,3,opt,name=customWidget,proto3" json:"customWidget"`
	DayInfo      string `protobuf:"bytes,4,opt,name=dayInfo,proto3" json:"dayInfo"`
	Sort         int64  `protobuf:"varint,5,opt,name=sort,proto3" json:"sort"`
}

func (x *VisitCount) Reset() {
	*x = VisitCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisitCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisitCount) ProtoMessage() {}

func (x *VisitCount) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisitCount.ProtoReflect.Descriptor instead.
func (*VisitCount) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{41}
}

func (x *VisitCount) GetModelCube() int32 {
	if x != nil {
		return x.ModelCube
	}
	return 0
}

func (x *VisitCount) GetAppCube() int32 {
	if x != nil {
		return x.AppCube
	}
	return 0
}

func (x *VisitCount) GetCustomWidget() int32 {
	if x != nil {
		return x.CustomWidget
	}
	return 0
}

func (x *VisitCount) GetDayInfo() string {
	if x != nil {
		return x.DayInfo
	}
	return ""
}

func (x *VisitCount) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

type ServiceVisitRecordPageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PageReq *common.PageReq `protobuf:"bytes,1,opt,name=pageReq,proto3" json:"pageReq" description:"分页参数"`
	 
	ServiceName string `protobuf:"bytes,2,opt,name=serviceName,proto3" json:"serviceName" description:"服务名称"`
	 
	Start int64 `protobuf:"varint,3,opt,name=start,proto3" json:"start" description:"访问开始时间"`
	 
	End int64 `protobuf:"varint,4,opt,name=end,proto3" json:"end" description:"访问结束时间"`
	 
	Ip string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip" description:"客户端ip"`
	 
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state" description:"访问状态"`
	 
	ServiceId string `protobuf:"bytes,7,opt,name=serviceId,proto3" json:"serviceId" description:"服务id"`
	 
	ServiceVersion string `protobuf:"bytes,8,opt,name=serviceVersion,proto3" json:"serviceVersion" description:"服务版本"`
	 
	VisitType string `protobuf:"bytes,9,opt,name=visitType,proto3" json:"visitType" description:"访问类型，预测/解释"`
}

func (x *ServiceVisitRecordPageReq) Reset() {
	*x = ServiceVisitRecordPageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceVisitRecordPageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceVisitRecordPageReq) ProtoMessage() {}

func (x *ServiceVisitRecordPageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceVisitRecordPageReq.ProtoReflect.Descriptor instead.
func (*ServiceVisitRecordPageReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{42}
}

func (x *ServiceVisitRecordPageReq) GetPageReq() *common.PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ServiceVisitRecordPageReq) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceVisitRecordPageReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *ServiceVisitRecordPageReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *ServiceVisitRecordPageReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ServiceVisitRecordPageReq) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ServiceVisitRecordPageReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceVisitRecordPageReq) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

func (x *ServiceVisitRecordPageReq) GetVisitType() string {
	if x != nil {
		return x.VisitType
	}
	return ""
}

type DownloadServiceVisitRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ServiceIds []string `protobuf:"bytes,1,rep,name=serviceIds,proto3" json:"serviceIds" description:"服务id数组"`
	 
	Start int64 `protobuf:"varint,2,opt,name=start,proto3" json:"start" description:"访问开始时间"`
	 
	End int64 `protobuf:"varint,3,opt,name=end,proto3" json:"end" description:"访问结束时间"`
}

func (x *DownloadServiceVisitRecordReq) Reset() {
	*x = DownloadServiceVisitRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadServiceVisitRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadServiceVisitRecordReq) ProtoMessage() {}

func (x *DownloadServiceVisitRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadServiceVisitRecordReq.ProtoReflect.Descriptor instead.
func (*DownloadServiceVisitRecordReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{43}
}

func (x *DownloadServiceVisitRecordReq) GetServiceIds() []string {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *DownloadServiceVisitRecordReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *DownloadServiceVisitRecordReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type DownloadServiceVisitRecordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data" description:"访问记录字节数据"`
	 
	FileName string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName" description:"访问记录文件名称"`
}

func (x *DownloadServiceVisitRecordRes) Reset() {
	*x = DownloadServiceVisitRecordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadServiceVisitRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadServiceVisitRecordRes) ProtoMessage() {}

func (x *DownloadServiceVisitRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadServiceVisitRecordRes.ProtoReflect.Descriptor instead.
func (*DownloadServiceVisitRecordRes) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{44}
}

func (x *DownloadServiceVisitRecordRes) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DownloadServiceVisitRecordRes) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type ServiceVisitRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" description:"唯一标识"`
	 
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"服务名称"`
	 
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version" description:"服务版本"`
	 
	VisitTime string `protobuf:"bytes,4,opt,name=visitTime,proto3" json:"visitTime" description:"访问时间"`
	 
	State string `protobuf:"bytes,5,opt,name=state,proto3" json:"state" description:"访问状态"`
	 
	ServiceIp string `protobuf:"bytes,6,opt,name=serviceIp,proto3" json:"serviceIp" description:"请求路径"`
	 
	Duration  int64             `protobuf:"varint,7,opt,name=duration,proto3" json:"duration" description:"访问耗时"`
	Champion  map[string]string `protobuf:"bytes,9,rep,name=champion,proto3" json:"champion" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Candidate map[string]string `protobuf:"bytes,10,rep,name=candidate,proto3" json:"candidate" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	 
	RefType string `protobuf:"bytes,11,opt,name=refType,proto3" json:"refType" description:"服务类型"`
}

func (x *ServiceVisitRecord) Reset() {
	*x = ServiceVisitRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceVisitRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceVisitRecord) ProtoMessage() {}

func (x *ServiceVisitRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceVisitRecord.ProtoReflect.Descriptor instead.
func (*ServiceVisitRecord) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{45}
}

func (x *ServiceVisitRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ServiceVisitRecord) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceVisitRecord) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServiceVisitRecord) GetVisitTime() string {
	if x != nil {
		return x.VisitTime
	}
	return ""
}

func (x *ServiceVisitRecord) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ServiceVisitRecord) GetServiceIp() string {
	if x != nil {
		return x.ServiceIp
	}
	return ""
}

func (x *ServiceVisitRecord) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceVisitRecord) GetChampion() map[string]string {
	if x != nil {
		return x.Champion
	}
	return nil
}

func (x *ServiceVisitRecord) GetCandidate() map[string]string {
	if x != nil {
		return x.Candidate
	}
	return nil
}

func (x *ServiceVisitRecord) GetRefType() string {
	if x != nil {
		return x.RefType
	}
	return ""
}

type Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonInfo     *Detail_CommonInfo     `protobuf:"bytes,1,opt,name=commonInfo,proto3" json:"commonInfo"`
	RequestHeader  *Detail_RequestHeader  `protobuf:"bytes,2,opt,name=requestHeader,proto3" json:"requestHeader"`
	RequestBody    string                 `protobuf:"bytes,3,opt,name=requestBody,proto3" json:"requestBody"`
	ResponseHeader *Detail_ResponseHeader `protobuf:"bytes,4,opt,name=responseHeader,proto3" json:"responseHeader"`
	ResponseBody   string                 `protobuf:"bytes,5,opt,name=responseBody,proto3" json:"responseBody"`
	Id             string                 `protobuf:"bytes,6,opt,name=id,proto3" json:"id"`
}

func (x *Detail) Reset() {
	*x = Detail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail) ProtoMessage() {}

func (x *Detail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail.ProtoReflect.Descriptor instead.
func (*Detail) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{46}
}

func (x *Detail) GetCommonInfo() *Detail_CommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *Detail) GetRequestHeader() *Detail_RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *Detail) GetRequestBody() string {
	if x != nil {
		return x.RequestBody
	}
	return ""
}

func (x *Detail) GetResponseHeader() *Detail_ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *Detail) GetResponseBody() string {
	if x != nil {
		return x.ResponseBody
	}
	return ""
}

func (x *Detail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AsyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Response string `protobuf:"bytes,2,opt,name=response,proto3" json:"response"`
	EndTime  int64  `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime"`
	State    string `protobuf:"bytes,4,opt,name=state,proto3" json:"state"`
}

func (x *AsyncResponse) Reset() {
	*x = AsyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AsyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncResponse) ProtoMessage() {}

func (x *AsyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncResponse.ProtoReflect.Descriptor instead.
func (*AsyncResponse) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{47}
}

func (x *AsyncResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AsyncResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *AsyncResponse) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *AsyncResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type ListConsumerDetailsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PageReq *common.PageReq `protobuf:"bytes,1,opt,name=page_req,json=pageReq,proto3" json:"page_req" description:"分页参数"`
	 
	ProjectId string `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"项目id"`
}

func (x *ListConsumerDetailsReq) Reset() {
	*x = ListConsumerDetailsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConsumerDetailsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConsumerDetailsReq) ProtoMessage() {}

func (x *ListConsumerDetailsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConsumerDetailsReq.ProtoReflect.Descriptor instead.
func (*ListConsumerDetailsReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{48}
}

func (x *ListConsumerDetailsReq) GetPageReq() *common.PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ListConsumerDetailsReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type ConsumerDetailsPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Size int32 `protobuf:"varint,1,opt,name=size,proto3" json:"size" description:"总数量"`
	 
	ConsumerDetails []*ConsumerDetail `protobuf:"bytes,2,rep,name=consumer_details,json=consumerDetails,proto3" json:"consumer_details" description:"消费明细列表"`
}

func (x *ConsumerDetailsPage) Reset() {
	*x = ConsumerDetailsPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumerDetailsPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerDetailsPage) ProtoMessage() {}

func (x *ConsumerDetailsPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerDetailsPage.ProtoReflect.Descriptor instead.
func (*ConsumerDetailsPage) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{49}
}

func (x *ConsumerDetailsPage) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ConsumerDetailsPage) GetConsumerDetails() []*ConsumerDetail {
	if x != nil {
		return x.ConsumerDetails
	}
	return nil
}

type ConsumerDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	XRequestId string `protobuf:"bytes,1,opt,name=x_request_id,json=xRequestId,proto3" json:"x_request_id" description:"请求request id"`
	 
	ServiceId string `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`
	 
	ServiceName string `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name" description:"服务名称"`
	 
	TokenUsage *TokenUsage `protobuf:"bytes,4,opt,name=token_usage,json=tokenUsage,proto3" json:"token_usage" description:"消费token"`
	 
	BillingConfig *common.BillingConfig `protobuf:"bytes,5,opt,name=billing_config,json=billingConfig,proto3" json:"billing_config" description:"单价"`
	 
	TotalPrice float32 `protobuf:"fixed32,6,opt,name=total_price,json=totalPrice,proto3" json:"total_price" description:"总价"`
	 
	Username string `protobuf:"bytes,7,opt,name=username,proto3" json:"username" description:"用户名"`
	 
	ApiKey string `protobuf:"bytes,8,opt,name=api_key,json=apiKey,proto3" json:"api_key" description:"api key"`
	 
	CreateTime int64 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time" description:"创建时间"`
	 
	Type ConsumerDetail_Type `protobuf:"varint,10,opt,name=type,proto3,enum=serving.ConsumerDetail_Type" json:"type" description:"类型: 0:内部调用 1:收入 2:消费"`
}

func (x *ConsumerDetail) Reset() {
	*x = ConsumerDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumerDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerDetail) ProtoMessage() {}

func (x *ConsumerDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerDetail.ProtoReflect.Descriptor instead.
func (*ConsumerDetail) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{50}
}

func (x *ConsumerDetail) GetXRequestId() string {
	if x != nil {
		return x.XRequestId
	}
	return ""
}

func (x *ConsumerDetail) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ConsumerDetail) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ConsumerDetail) GetTokenUsage() *TokenUsage {
	if x != nil {
		return x.TokenUsage
	}
	return nil
}

func (x *ConsumerDetail) GetBillingConfig() *common.BillingConfig {
	if x != nil {
		return x.BillingConfig
	}
	return nil
}

func (x *ConsumerDetail) GetTotalPrice() float32 {
	if x != nil {
		return x.TotalPrice
	}
	return 0
}

func (x *ConsumerDetail) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ConsumerDetail) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *ConsumerDetail) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ConsumerDetail) GetType() ConsumerDetail_Type {
	if x != nil {
		return x.Type
	}
	return ConsumerDetail_INNER
}

type CountConsumptionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end" description:"结束时间"`
	 
	ProjectId string `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"项目id"`
}

func (x *CountConsumptionReq) Reset() {
	*x = CountConsumptionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountConsumptionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountConsumptionReq) ProtoMessage() {}

func (x *CountConsumptionReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountConsumptionReq.ProtoReflect.Descriptor instead.
func (*CountConsumptionReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{51}
}

func (x *CountConsumptionReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *CountConsumptionReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *CountConsumptionReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type CountConsumptionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Chart *ConsumerDetailsChart `protobuf:"bytes,1,opt,name=chart,proto3" json:"chart" description:"消费图表"`
	 
	TotalConsumption *TotalBilling `protobuf:"bytes,2,opt,name=total_consumption,json=totalConsumption,proto3" json:"total_consumption" description:"总消费"`
	 
	TotalIncome *TotalBilling `protobuf:"bytes,3,opt,name=total_income,json=totalIncome,proto3" json:"total_income" description:"总收入"`
}

func (x *CountConsumptionRsp) Reset() {
	*x = CountConsumptionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountConsumptionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountConsumptionRsp) ProtoMessage() {}

func (x *CountConsumptionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountConsumptionRsp.ProtoReflect.Descriptor instead.
func (*CountConsumptionRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{52}
}

func (x *CountConsumptionRsp) GetChart() *ConsumerDetailsChart {
	if x != nil {
		return x.Chart
	}
	return nil
}

func (x *CountConsumptionRsp) GetTotalConsumption() *TotalBilling {
	if x != nil {
		return x.TotalConsumption
	}
	return nil
}

func (x *CountConsumptionRsp) GetTotalIncome() *TotalBilling {
	if x != nil {
		return x.TotalIncome
	}
	return nil
}

// 统计累计(消费/收入)占比
type CountRatioReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id" description:"项目id"`
	 
	Types []string `protobuf:"bytes,2,rep,name=types,proto3" json:"types" description:"筛选字段 消费类型: 可选值 0:内部调用 1:收入 2:消费，可多选逗号分隔"`
	 
	Start int64 `protobuf:"varint,3,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,4,opt,name=end,proto3" json:"end" description:"结束时间"`
}

func (x *CountRatioReq) Reset() {
	*x = CountRatioReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountRatioReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountRatioReq) ProtoMessage() {}

func (x *CountRatioReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountRatioReq.ProtoReflect.Descriptor instead.
func (*CountRatioReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{53}
}

func (x *CountRatioReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CountRatioReq) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *CountRatioReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *CountRatioReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type CountRatioRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Model float32 `protobuf:"fixed32,1,opt,name=model,proto3" json:"model" description:"模型"`
	 
	Application float32 `protobuf:"fixed32,2,opt,name=application,proto3" json:"application" description:"应用"`
	 
	Knowledge float32 `protobuf:"fixed32,3,opt,name=knowledge,proto3" json:"knowledge" description:"知识"`
}

func (x *CountRatioRsp) Reset() {
	*x = CountRatioRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountRatioRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountRatioRsp) ProtoMessage() {}

func (x *CountRatioRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountRatioRsp.ProtoReflect.Descriptor instead.
func (*CountRatioRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{54}
}

func (x *CountRatioRsp) GetModel() float32 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *CountRatioRsp) GetApplication() float32 {
	if x != nil {
		return x.Application
	}
	return 0
}

func (x *CountRatioRsp) GetKnowledge() float32 {
	if x != nil {
		return x.Knowledge
	}
	return 0
}

type ListServiceGroupPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int64               `protobuf:"varint,1,opt,name=size,proto3" json:"size"`
	List []*ListServiceGroup `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ListServiceGroupPage) Reset() {
	*x = ListServiceGroupPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceGroupPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceGroupPage) ProtoMessage() {}

func (x *ListServiceGroupPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceGroupPage.ProtoReflect.Descriptor instead.
func (*ListServiceGroupPage) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{55}
}

func (x *ListServiceGroupPage) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListServiceGroupPage) GetList() []*ListServiceGroup {
	if x != nil {
		return x.List
	}
	return nil
}

type ListServiceGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username" description:"调用方"`
	 
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type" description:"类型 1:内部调用 2:收入 3:消费"`
	 
	Total float32 `protobuf:"fixed32,3,opt,name=total,proto3" json:"total" description:"总金额"`
	 
	CallTimes int64 `protobuf:"varint,4,opt,name=call_times,json=callTimes,proto3" json:"call_times" description:"调用次数"`
	 
	TotalTokens int64 `protobuf:"varint,5,opt,name=total_tokens,json=totalTokens,proto3" json:"total_tokens" description:"总token"`
}

func (x *ListServiceGroup) Reset() {
	*x = ListServiceGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceGroup) ProtoMessage() {}

func (x *ListServiceGroup) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceGroup.ProtoReflect.Descriptor instead.
func (*ListServiceGroup) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{56}
}

func (x *ListServiceGroup) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ListServiceGroup) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ListServiceGroup) GetTotal() float32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListServiceGroup) GetCallTimes() int64 {
	if x != nil {
		return x.CallTimes
	}
	return 0
}

func (x *ListServiceGroup) GetTotalTokens() int64 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

type IncomeGraphItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Time      int64   `protobuf:"varint,1,opt,name=time,proto3" json:"time" description:"秒时间戳"`
	Model     float32 `protobuf:"fixed32,2,opt,name=model,proto3" json:"model"`
	App       float32 `protobuf:"fixed32,3,opt,name=app,proto3" json:"app"`
	Knowledge float32 `protobuf:"fixed32,4,opt,name=knowledge,proto3" json:"knowledge"`
}

func (x *IncomeGraphItem) Reset() {
	*x = IncomeGraphItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncomeGraphItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncomeGraphItem) ProtoMessage() {}

func (x *IncomeGraphItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncomeGraphItem.ProtoReflect.Descriptor instead.
func (*IncomeGraphItem) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{57}
}

func (x *IncomeGraphItem) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *IncomeGraphItem) GetModel() float32 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *IncomeGraphItem) GetApp() float32 {
	if x != nil {
		return x.App
	}
	return 0
}

func (x *IncomeGraphItem) GetKnowledge() float32 {
	if x != nil {
		return x.Knowledge
	}
	return 0
}

type TotalBilling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Day float32 `protobuf:"fixed32,1,opt,name=day,proto3" json:"day" description:"日总计费"`
	 
	Month float32 `protobuf:"fixed32,2,opt,name=month,proto3" json:"month" description:"月总计费"`
	 
	Total float32 `protobuf:"fixed32,3,opt,name=total,proto3" json:"total" description:"总计费"`
}

func (x *TotalBilling) Reset() {
	*x = TotalBilling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TotalBilling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TotalBilling) ProtoMessage() {}

func (x *TotalBilling) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TotalBilling.ProtoReflect.Descriptor instead.
func (*TotalBilling) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{58}
}

func (x *TotalBilling) GetDay() float32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *TotalBilling) GetMonth() float32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *TotalBilling) GetTotal() float32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ConsumerDetailsChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Data []*ConsumerDetailsChart_Value `protobuf:"bytes,1,rep,name=data,proto3" json:"data" description:"消费图表数据"`
}

func (x *ConsumerDetailsChart) Reset() {
	*x = ConsumerDetailsChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumerDetailsChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerDetailsChart) ProtoMessage() {}

func (x *ConsumerDetailsChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerDetailsChart.ProtoReflect.Descriptor instead.
func (*ConsumerDetailsChart) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{59}
}

func (x *ConsumerDetailsChart) GetData() []*ConsumerDetailsChart_Value {
	if x != nil {
		return x.Data
	}
	return nil
}

type TokenUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PromptTokens int64 `protobuf:"varint,1,opt,name=prompt_tokens,json=promptTokens,proto3" json:"prompt_tokens" description:"输入提示token数"`
	 
	CompletionTokens int64 `protobuf:"varint,2,opt,name=completion_tokens,json=completionTokens,proto3" json:"completion_tokens" description:"响应token数"`
	 
	TotalTokens int64 `protobuf:"varint,3,opt,name=total_tokens,json=totalTokens,proto3" json:"total_tokens" description:"总token数"`
}

func (x *TokenUsage) Reset() {
	*x = TokenUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenUsage) ProtoMessage() {}

func (x *TokenUsage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenUsage.ProtoReflect.Descriptor instead.
func (*TokenUsage) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{60}
}

func (x *TokenUsage) GetPromptTokens() int64 {
	if x != nil {
		return x.PromptTokens
	}
	return 0
}

func (x *TokenUsage) GetCompletionTokens() int64 {
	if x != nil {
		return x.CompletionTokens
	}
	return 0
}

func (x *TokenUsage) GetTotalTokens() int64 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

type EvaluateAnswerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id" description:"X-Request-ID"`
	 
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question" description:"question"`
	 
	Answer string `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer" description:"answer"`
	 
	Rating int32 `protobuf:"varint,5,opt,name=rating,proto3" json:"rating" description:"rating, like is 1, unlike is -1"`
	 
	SourceType SourceType `protobuf:"varint,6,opt,name=source_type,json=sourceType,proto3,enum=serving.SourceType" json:"source_type" description:"来源类型; 0:SOURCE_TYPE_UNKNOW,1:SOURCE_TYPE_MODEL_CUBE,2:SOURCE_TYPE_APP_CUBE,3:SOURCE_TYPE_VLAB,4:SOURCE_TYPE_REMOTE,5:SOURCE_TYPE_KNOWLEDGE,11:SOURCE_TYPE_CUSTOM"`
	 
	SourceId string `protobuf:"bytes,7,opt,name=source_id,json=sourceId,proto3" json:"source_id" description:"来源id"`
}

func (x *EvaluateAnswerReq) Reset() {
	*x = EvaluateAnswerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateAnswerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateAnswerReq) ProtoMessage() {}

func (x *EvaluateAnswerReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateAnswerReq.ProtoReflect.Descriptor instead.
func (*EvaluateAnswerReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{61}
}

func (x *EvaluateAnswerReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *EvaluateAnswerReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *EvaluateAnswerReq) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *EvaluateAnswerReq) GetRating() int32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *EvaluateAnswerReq) GetSourceType() SourceType {
	if x != nil {
		return x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNKNOW
}

func (x *EvaluateAnswerReq) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

type CountTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end" description:"结束时间"`
	 
	ServiceId string `protobuf:"bytes,4,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`
}

func (x *CountTokenReq) Reset() {
	*x = CountTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountTokenReq) ProtoMessage() {}

func (x *CountTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountTokenReq.ProtoReflect.Descriptor instead.
func (*CountTokenReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{62}
}

func (x *CountTokenReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *CountTokenReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *CountTokenReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

type GetTokenBarChartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start" description:"开始时间"`
	 
	End int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end" description:"结束时间"`
	 
	Step int64 `protobuf:"varint,3,opt,name=step,proto3" json:"step" description:"查询步长,单位s"`
	 
	ServiceId string                   `protobuf:"bytes,4,opt,name=service_id,json=serviceId,proto3" json:"service_id" description:"服务id"`
	Type      GetTokenBarChartReq_Type `protobuf:"varint,5,opt,name=type,proto3,enum=serving.GetTokenBarChartReq_Type" json:"type"`
}

func (x *GetTokenBarChartReq) Reset() {
	*x = GetTokenBarChartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenBarChartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenBarChartReq) ProtoMessage() {}

func (x *GetTokenBarChartReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenBarChartReq.ProtoReflect.Descriptor instead.
func (*GetTokenBarChartReq) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{63}
}

func (x *GetTokenBarChartReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *GetTokenBarChartReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *GetTokenBarChartReq) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *GetTokenBarChartReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *GetTokenBarChartReq) GetType() GetTokenBarChartReq_Type {
	if x != nil {
		return x.Type
	}
	return GetTokenBarChartReq_ALL
}

type DashboardTokenBarChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Data []*DashboardTokenBarChart_Value `protobuf:"bytes,1,rep,name=data,proto3" json:"data" description:"访问量/平均耗时柱状图数据"`
}

func (x *DashboardTokenBarChart) Reset() {
	*x = DashboardTokenBarChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardTokenBarChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardTokenBarChart) ProtoMessage() {}

func (x *DashboardTokenBarChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardTokenBarChart.ProtoReflect.Descriptor instead.
func (*DashboardTokenBarChart) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{64}
}

func (x *DashboardTokenBarChart) GetData() []*DashboardTokenBarChart_Value {
	if x != nil {
		return x.Data
	}
	return nil
}

type CountTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	InputToken int32 `protobuf:"varint,1,opt,name=input_token,json=inputToken,proto3" json:"input_token" description:"输入token数量"`
	 
	OutputToken int32 `protobuf:"varint,2,opt,name=output_token,json=outputToken,proto3" json:"output_token" description:"输出token数量"`
}

func (x *CountTokenRsp) Reset() {
	*x = CountTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountTokenRsp) ProtoMessage() {}

func (x *CountTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountTokenRsp.ProtoReflect.Descriptor instead.
func (*CountTokenRsp) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{65}
}

func (x *CountTokenRsp) GetInputToken() int32 {
	if x != nil {
		return x.InputToken
	}
	return 0
}

func (x *CountTokenRsp) GetOutputToken() int32 {
	if x != nil {
		return x.OutputToken
	}
	return 0
}

type ServiceRecordCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total" description:"调用总量"`
	 
	FailCount int64 `protobuf:"varint,2,opt,name=fail_count,json=failCount,proto3" json:"fail_count" description:"调用失败次数"`
	 
	FailRate float64 `protobuf:"fixed64,3,opt,name=fail_rate,json=failRate,proto3" json:"fail_rate" description:"调用失败率"`
}

func (x *ServiceRecordCount) Reset() {
	*x = ServiceRecordCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceRecordCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceRecordCount) ProtoMessage() {}

func (x *ServiceRecordCount) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceRecordCount.ProtoReflect.Descriptor instead.
func (*ServiceRecordCount) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{66}
}

func (x *ServiceRecordCount) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ServiceRecordCount) GetFailCount() int64 {
	if x != nil {
		return x.FailCount
	}
	return 0
}

func (x *ServiceRecordCount) GetFailRate() float64 {
	if x != nil {
		return x.FailRate
	}
	return 0
}

type AvgFirstTimeToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	TodayAvg float64 `protobuf:"fixed64,1,opt,name=todayAvg,proto3" json:"todayAvg" description:"当日服务平均值"`
	 
	YesterdayAvg float64 `protobuf:"fixed64,2,opt,name=yesterdayAvg,proto3" json:"yesterdayAvg" description:"昨日服务平均值"`
	 
	Trend string `protobuf:"bytes,3,opt,name=trend,proto3" json:"trend" description:"趋势百分比"`
}

func (x *AvgFirstTimeToken) Reset() {
	*x = AvgFirstTimeToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvgFirstTimeToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvgFirstTimeToken) ProtoMessage() {}

func (x *AvgFirstTimeToken) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvgFirstTimeToken.ProtoReflect.Descriptor instead.
func (*AvgFirstTimeToken) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{67}
}

func (x *AvgFirstTimeToken) GetTodayAvg() float64 {
	if x != nil {
		return x.TodayAvg
	}
	return 0
}

func (x *AvgFirstTimeToken) GetYesterdayAvg() float64 {
	if x != nil {
		return x.YesterdayAvg
	}
	return 0
}

func (x *AvgFirstTimeToken) GetTrend() string {
	if x != nil {
		return x.Trend
	}
	return ""
}

type GpuCurveChart_Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []float32 `protobuf:"fixed32,1,rep,packed,name=value,proto3" json:"value"`
}

func (x *GpuCurveChart_Value) Reset() {
	*x = GpuCurveChart_Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GpuCurveChart_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GpuCurveChart_Value) ProtoMessage() {}

func (x *GpuCurveChart_Value) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GpuCurveChart_Value.ProtoReflect.Descriptor instead.
func (*GpuCurveChart_Value) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{30, 0}
}

func (x *GpuCurveChart_Value) GetValue() []float32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type DashboardCurveChart_Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Value []int32 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value" description:"服务数量：总量/上线/下线"`
}

func (x *DashboardCurveChart_Value) Reset() {
	*x = DashboardCurveChart_Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardCurveChart_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardCurveChart_Value) ProtoMessage() {}

func (x *DashboardCurveChart_Value) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardCurveChart_Value.ProtoReflect.Descriptor instead.
func (*DashboardCurveChart_Value) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{34, 0}
}

func (x *DashboardCurveChart_Value) GetValue() []int32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type DashboardRequestBarChart_Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"服务名称"`
	 
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num" description:"访问数量"`
	 
	Cost float32 `protobuf:"fixed32,5,opt,name=cost,proto3" json:"cost" description:"访问耗时"`
}

func (x *DashboardRequestBarChart_Value) Reset() {
	*x = DashboardRequestBarChart_Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardRequestBarChart_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardRequestBarChart_Value) ProtoMessage() {}

func (x *DashboardRequestBarChart_Value) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardRequestBarChart_Value.ProtoReflect.Descriptor instead.
func (*DashboardRequestBarChart_Value) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{36, 0}
}

func (x *DashboardRequestBarChart_Value) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DashboardRequestBarChart_Value) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *DashboardRequestBarChart_Value) GetCost() float32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

type DashboardVisitRank_Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ServiceId string `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId" description:"服务id"`
	 
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"服务名称"`
	 
	Number int32 `protobuf:"varint,3,opt,name=number,proto3" json:"number" description:"访问数量"`
}

func (x *DashboardVisitRank_Value) Reset() {
	*x = DashboardVisitRank_Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardVisitRank_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardVisitRank_Value) ProtoMessage() {}

func (x *DashboardVisitRank_Value) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardVisitRank_Value.ProtoReflect.Descriptor instead.
func (*DashboardVisitRank_Value) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{37, 0}
}

func (x *DashboardVisitRank_Value) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *DashboardVisitRank_Value) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DashboardVisitRank_Value) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

type DashboardVisitRank_RequestTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ServiceId string `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId" description:"服务id"`
	 
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name" description:"服务名称"`
	 
	Cost float32 `protobuf:"fixed32,3,opt,name=cost,proto3" json:"cost" description:"访问耗时"`
}

func (x *DashboardVisitRank_RequestTime) Reset() {
	*x = DashboardVisitRank_RequestTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardVisitRank_RequestTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardVisitRank_RequestTime) ProtoMessage() {}

func (x *DashboardVisitRank_RequestTime) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardVisitRank_RequestTime.ProtoReflect.Descriptor instead.
func (*DashboardVisitRank_RequestTime) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{37, 1}
}

func (x *DashboardVisitRank_RequestTime) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *DashboardVisitRank_RequestTime) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DashboardVisitRank_RequestTime) GetCost() float32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

type DashboardVisitHotGraph_HotGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date" description:"日期"`
	 
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count" description:"数量"`
}

func (x *DashboardVisitHotGraph_HotGraph) Reset() {
	*x = DashboardVisitHotGraph_HotGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardVisitHotGraph_HotGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardVisitHotGraph_HotGraph) ProtoMessage() {}

func (x *DashboardVisitHotGraph_HotGraph) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardVisitHotGraph_HotGraph.ProtoReflect.Descriptor instead.
func (*DashboardVisitHotGraph_HotGraph) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{38, 0}
}

func (x *DashboardVisitHotGraph_HotGraph) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DashboardVisitHotGraph_HotGraph) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type Detail_CommonInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	RequestPath string `protobuf:"bytes,1,opt,name=requestPath,proto3" json:"requestPath" description:"请求路径"`
	 
	RequestMethod string `protobuf:"bytes,2,opt,name=requestMethod,proto3" json:"requestMethod" description:"请求方法"`
	 
	RemoteAddress string `protobuf:"bytes,3,opt,name=remoteAddress,proto3" json:"remoteAddress" description:"服务器地址"`
	 
	StatusCode string `protobuf:"bytes,4,opt,name=statusCode,proto3" json:"statusCode" description:"请求状态码"`
}

func (x *Detail_CommonInfo) Reset() {
	*x = Detail_CommonInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail_CommonInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail_CommonInfo) ProtoMessage() {}

func (x *Detail_CommonInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail_CommonInfo.ProtoReflect.Descriptor instead.
func (*Detail_CommonInfo) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{46, 0}
}

func (x *Detail_CommonInfo) GetRequestPath() string {
	if x != nil {
		return x.RequestPath
	}
	return ""
}

func (x *Detail_CommonInfo) GetRequestMethod() string {
	if x != nil {
		return x.RequestMethod
	}
	return ""
}

func (x *Detail_CommonInfo) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

func (x *Detail_CommonInfo) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

type Detail_RequestHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Mime string `protobuf:"bytes,1,opt,name=mime,proto3" json:"mime" description:"请求媒体类型"`
	 
	ClientInfo string `protobuf:"bytes,2,opt,name=clientInfo,proto3" json:"clientInfo" description:"客户端信息"`
	 
	TargetServer string `protobuf:"bytes,3,opt,name=targetServer,proto3" json:"targetServer" description:"目标服务器"`
}

func (x *Detail_RequestHeader) Reset() {
	*x = Detail_RequestHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail_RequestHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail_RequestHeader) ProtoMessage() {}

func (x *Detail_RequestHeader) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail_RequestHeader.ProtoReflect.Descriptor instead.
func (*Detail_RequestHeader) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{46, 1}
}

func (x *Detail_RequestHeader) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *Detail_RequestHeader) GetClientInfo() string {
	if x != nil {
		return x.ClientInfo
	}
	return ""
}

func (x *Detail_RequestHeader) GetTargetServer() string {
	if x != nil {
		return x.TargetServer
	}
	return ""
}

type Detail_ResponseHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseBytes int64  `protobuf:"varint,1,opt,name=responseBytes,proto3" json:"responseBytes"`
	StatusCode    string `protobuf:"bytes,2,opt,name=statusCode,proto3" json:"statusCode"`
	ProxyServer   string `protobuf:"bytes,3,opt,name=proxyServer,proto3" json:"proxyServer"`
	RemoteAddress string `protobuf:"bytes,4,opt,name=remoteAddress,proto3" json:"remoteAddress"`
}

func (x *Detail_ResponseHeader) Reset() {
	*x = Detail_ResponseHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail_ResponseHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail_ResponseHeader) ProtoMessage() {}

func (x *Detail_ResponseHeader) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail_ResponseHeader.ProtoReflect.Descriptor instead.
func (*Detail_ResponseHeader) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{46, 2}
}

func (x *Detail_ResponseHeader) GetResponseBytes() int64 {
	if x != nil {
		return x.ResponseBytes
	}
	return 0
}

func (x *Detail_ResponseHeader) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *Detail_ResponseHeader) GetProxyServer() string {
	if x != nil {
		return x.ProxyServer
	}
	return ""
}

func (x *Detail_ResponseHeader) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

type Detail_ExplainerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response string `protobuf:"bytes,1,opt,name=response,proto3" json:"response"`
	EndTime  int64  `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime"`
	State    string `protobuf:"bytes,3,opt,name=state,proto3" json:"state"`
}

func (x *Detail_ExplainerResponse) Reset() {
	*x = Detail_ExplainerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail_ExplainerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail_ExplainerResponse) ProtoMessage() {}

func (x *Detail_ExplainerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail_ExplainerResponse.ProtoReflect.Descriptor instead.
func (*Detail_ExplainerResponse) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{46, 3}
}

func (x *Detail_ExplainerResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *Detail_ExplainerResponse) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Detail_ExplainerResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type ConsumerDetailsChart_Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Time int64 `protobuf:"varint,1,opt,name=time,proto3" json:"time" description:"时间节点"`
	 
	Price float32 `protobuf:"fixed32,2,opt,name=price,proto3" json:"price" description:"消费金额"`
}

func (x *ConsumerDetailsChart_Value) Reset() {
	*x = ConsumerDetailsChart_Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumerDetailsChart_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerDetailsChart_Value) ProtoMessage() {}

func (x *ConsumerDetailsChart_Value) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerDetailsChart_Value.ProtoReflect.Descriptor instead.
func (*ConsumerDetailsChart_Value) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{59, 0}
}

func (x *ConsumerDetailsChart_Value) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *ConsumerDetailsChart_Value) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

type DashboardTokenBarChart_Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"时间节点"`
	 
	Num int64 `protobuf:"varint,4,opt,name=num,proto3" json:"num" description:"输出token数量"`
}

func (x *DashboardTokenBarChart_Value) Reset() {
	*x = DashboardTokenBarChart_Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_serving_audit_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardTokenBarChart_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardTokenBarChart_Value) ProtoMessage() {}

func (x *DashboardTokenBarChart_Value) ProtoReflect() protoreflect.Message {
	mi := &file_proto_serving_audit_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardTokenBarChart_Value.ProtoReflect.Descriptor instead.
func (*DashboardTokenBarChart_Value) Descriptor() ([]byte, []int) {
	return file_proto_serving_audit_proto_rawDescGZIP(), []int{64, 0}
}

func (x *DashboardTokenBarChart_Value) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DashboardTokenBarChart_Value) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

var File_proto_serving_audit_proto protoreflect.FileDescriptor

var file_proto_serving_audit_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f,
	0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x88, 0x01, 0x0a, 0x17, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x47,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4f, 0x76, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x3b, 0x0a,
	0x0b, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x76, 0x67,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x0a, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x42, 0x61, 0x73, 0x65, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x38, 0x0a, 0x13,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x44,
	0x61, 0x79, 0x73, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x79, 0x73,
	0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x22, 0x30, 0x0a, 0x04, 0x44, 0x61, 0x79, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x13, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x61, 0x73, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x79, 0x65, 0x73, 0x74, 0x65, 0x72, 0x64, 0x61, 0x79, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x79, 0x65, 0x73, 0x74,
	0x65, 0x72, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f,
	0x64, 0x61, 0x79, 0x5f, 0x72, 0x74, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x6f, 0x64, 0x61, 0x79, 0x52, 0x74, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x79, 0x65, 0x73, 0x74, 0x65,
	0x72, 0x64, 0x61, 0x79, 0x5f, 0x72, 0x74, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x79, 0x65, 0x73, 0x74, 0x65, 0x72, 0x64, 0x61, 0x79, 0x52, 0x74, 0x74, 0x22, 0x52, 0x0a, 0x14,
	0x41, 0x76, 0x67, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x33, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x0f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x0b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3d, 0x0a, 0x0e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x63, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x07, 0x70, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x59, 0x0a,
	0x0e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x22, 0x9c, 0x02, 0x0a, 0x0a, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f,
	0x72, 0x12, 0x24, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xba, 0x03, 0x0a, 0x16, 0x50, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x70, 0x68, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x70, 0x68, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x70, 0x68, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x67, 0x72, 0x61, 0x70, 0x68,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x56, 0x69, 0x65,
	0x77, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2c, 0x0a, 0x11, 0x62, 0x61, 0x74, 0x63, 0x68, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x11, 0x62, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x12, 0x62, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x22, 0x53, 0x0a, 0x0b, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x99, 0x01, 0x0a, 0x13, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x4f, 0x0a, 0x0d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5e, 0x0a, 0x18, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x65, 0x6e, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x96, 0x04, 0x0a, 0x1e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x46,
	0x0a, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x49, 0x0a, 0x0f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x52, 0x0f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x6f, 0x77, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x1a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x4f, 0x0a, 0x0e, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x0e, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x72, 0x0a, 0x1f,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x96, 0x04, 0x0a, 0x1e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x53,
	0x61, 0x76, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52,
	0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12,
	0x49, 0x0a, 0x0f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x0f, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x47, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x53, 0x61, 0x76, 0x65, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x12, 0x4f, 0x0a, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x72, 0x0a, 0x1f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xab, 0x01, 0x0a, 0x15, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x12, 0x3f, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x1a, 0x51, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x94, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x12, 0x40, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x38, 0x0a, 0x0a, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x60,
	0x0a, 0x1a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04,
	0x61, 0x72, 0x65, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x65, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x22, 0x3b, 0x0a, 0x17, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x70,
	0x73, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x70, 0x73, 0x69, 0x12, 0x0e, 0x0a,
	0x02, 0x6a, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02, 0x6a, 0x73, 0x22, 0xfa, 0x01,
	0x0a, 0x1e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x65,
	0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x12, 0x6f, 0x0a, 0x12, 0x64, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x64,
	0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x1a, 0x67, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8a, 0x01, 0x0a, 0x0e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6d, 0x61,
	0x78, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x76, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x61, 0x76, 0x67, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x44, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x32, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x73, 0x69,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x70, 0x73, 0x69, 0x12, 0x0e, 0x0a, 0x02, 0x6a,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x02, 0x6a, 0x73, 0x22, 0x52, 0x0a, 0x10, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x22,
	0xc0, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x76, 0x63, 0x4d, 0x6c, 0x6f, 0x70, 0x73, 0x47,
	0x70, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0b, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x6e,
	0x64, 0x47, 0x70, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x76, 0x63, 0x4d, 0x6c, 0x6f, 0x70,
	0x73, 0x47, 0x70, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x6e, 0x64,
	0x47, 0x70, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x4e, 0x6f, 0x64, 0x65, 0x41,
	0x6e, 0x64, 0x47, 0x70, 0x75, 0x73, 0x1a, 0x5a, 0x0a, 0x10, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x6e,
	0x64, 0x47, 0x70, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x9d, 0x01, 0x0a, 0x13, 0x47, 0x70, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x70, 0x75, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x70, 0x75, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x64, 0x65, 0x22, 0x76, 0x0a, 0x0d, 0x47, 0x70, 0x75, 0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x70, 0x75, 0x43,
	0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x78, 0x41, 0x78, 0x69, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x78, 0x41, 0x78, 0x69, 0x73, 0x1a, 0x1d, 0x0a, 0x05, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x07, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x74,
	0x65, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xbb, 0x01, 0x0a, 0x11, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x24,
	0x0a, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x10,
	0x6e, 0x65, 0x77, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6e, 0x65, 0x77, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x6e, 0x65, 0x77, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x11, 0x6e, 0x65, 0x77, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x4e, 0x0a, 0x20, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x78, 0x41, 0x78, 0x69, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x78, 0x41, 0x78, 0x69, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x13, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x36,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x78, 0x41, 0x78, 0x69, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x78, 0x41, 0x78, 0x69, 0x73, 0x1a, 0x1d, 0x0a, 0x05,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x68, 0x0a, 0x18, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x76, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x70, 0x76, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x65, 0x77, 0x50, 0x56,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6e, 0x65, 0x77, 0x50, 0x56, 0x12, 0x10, 0x0a,
	0x03, 0x72, 0x74, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x74, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x72, 0x65, 0x6e, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x18, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x61, 0x72, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a,
	0x41, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x63, 0x6f,
	0x73, 0x74, 0x22, 0xb2, 0x02, 0x0a, 0x12, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x69, 0x73,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x69, 0x73, 0x69,
	0x74, 0x52, 0x61, 0x6e, 0x6b, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x69, 0x73,
	0x69, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x1a,
	0x51, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x1a, 0x53, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x16, 0x44, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x48, 0x6f, 0x74, 0x47, 0x72, 0x61,
	0x70, 0x68, 0x12, 0x44, 0x0a, 0x08, 0x68, 0x6f, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x48, 0x6f, 0x74,
	0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x48, 0x6f, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x08,
	0x68, 0x6f, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x1a, 0x34, 0x0a, 0x08, 0x48, 0x6f, 0x74, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x83,
	0x02, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x35, 0x0a,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x5b, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69,
	0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x70, 0x1a, 0x41, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x6d, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x73, 0x56, 0x69, 0x73,
	0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x56, 0x69, 0x73, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x06, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x22, 0x96, 0x01, 0x0a, 0x0a, 0x56, 0x69, 0x73, 0x69, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x75, 0x62, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x75, 0x62, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x43, 0x75, 0x62, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x61, 0x70, 0x70, 0x43, 0x75, 0x62, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x22, 0x9b, 0x02, 0x0a,
	0x19, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x76, 0x69, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x76, 0x69, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x67, 0x0a, 0x1d, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73,
	0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x65, 0x6e, 0x64, 0x22, 0x4f, 0x0a, 0x1d, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe6, 0x03, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x73,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69,
	0x73, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x6d, 0x70,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43, 0x68, 0x61, 0x6d, 0x70, 0x69, 0x6f, 0x6e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6d, 0x70, 0x69, 0x6f, 0x6e, 0x12, 0x48,
	0x0a, 0x09, 0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43,
	0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x63,
	0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x66, 0x54, 0x79,
	0x70, 0x65, 0x1a, 0x3b, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x6d, 0x70, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x3c, 0x0a, 0x0e, 0x43, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xaf, 0x06,
	0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x46, 0x0a, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x1a, 0x9a, 0x01, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x1a, 0x67, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x9e, 0x01,
	0x0a, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x5f,
	0x0a, 0x11, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22,
	0x6b, 0x0a, 0x0d, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x64, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x2b, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x22, 0x6d, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x42, 0x0a,
	0x10, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0xbe, 0x03, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x78, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3d,
	0x0a, 0x0e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73,
	0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70,
	0x69, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x2a, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09,
	0x0a, 0x05, 0x49, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x43,
	0x4f, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x45,
	0x10, 0x02, 0x22, 0x5c, 0x0a, 0x13, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x22, 0xc8, 0x01, 0x0a, 0x13, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x05, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x05, 0x63, 0x68, 0x61, 0x72, 0x74, 0x12, 0x42, 0x0a,
	0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52,
	0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x38, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0x6c, 0x0a, 0x0d, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x65, 0x0a, 0x0d, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x22, 0x59, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2d, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x61, 0x6c, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x6b, 0x0a, 0x0f, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x47, 0x72, 0x61, 0x70, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x22, 0x4c, 0x0a, 0x0c, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0x82, 0x01, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x37, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x43, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x31, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x0a, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x11,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0xd1, 0x01, 0x0a,
	0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x56, 0x0a, 0x0d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0xcf, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x72, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x26, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c,
	0x4c, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x4f, 0x55, 0x54, 0x50, 0x55, 0x54, 0x10, 0x02, 0x22, 0x82, 0x01, 0x0a, 0x16, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x72, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x2d, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22,
	0x53, 0x0a, 0x0d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x66, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x22, 0x69, 0x0a, 0x11,
	0x41, 0x76, 0x67, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x41, 0x76, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x08, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x41, 0x76, 0x67, 0x12, 0x22, 0x0a,
	0x0c, 0x79, 0x65, 0x73, 0x74, 0x65, 0x72, 0x64, 0x61, 0x79, 0x41, 0x76, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x79, 0x65, 0x73, 0x74, 0x65, 0x72, 0x64, 0x61, 0x79, 0x41, 0x76,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x32, 0xc0, 0x0a, 0x0a, 0x0e, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x56, 0x32, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x14, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x46, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x10, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x62, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x43, 0x75,
	0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x4d,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x14, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x49, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x61, 0x72, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x12, 0x10, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x43, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x12, 0x10, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x3d, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x10, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x49, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x56,
	0x69, 0x73, 0x69, 0x74, 0x12, 0x10, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x69, 0x73, 0x69, 0x74, 0x48,
	0x6f, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x60, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x12, 0x6c, 0x0a, 0x1a, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e,
	0x67, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x73, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x47, 0x50,
	0x55, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x75, 0x72, 0x76, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x12, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x70,
	0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x70, 0x75, 0x43,
	0x75, 0x72, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x51, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x3c, 0x0a, 0x0a,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x1a, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x41, 0x76, 0x67, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x76,
	0x67, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x76, 0x67,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x48,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x12, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x4f, 0x76, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x40, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x47,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x44, 0x61, 0x79, 0x73, 0x12, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x44, 0x61, 0x79, 0x73, 0x42, 0x33, 0x5a, 0x31, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c,
	0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x3b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_serving_audit_proto_rawDescOnce sync.Once
	file_proto_serving_audit_proto_rawDescData = file_proto_serving_audit_proto_rawDesc
)

func file_proto_serving_audit_proto_rawDescGZIP() []byte {
	file_proto_serving_audit_proto_rawDescOnce.Do(func() {
		file_proto_serving_audit_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_serving_audit_proto_rawDescData)
	})
	return file_proto_serving_audit_proto_rawDescData
}

var file_proto_serving_audit_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_serving_audit_proto_msgTypes = make([]protoimpl.MessageInfo, 89)
var file_proto_serving_audit_proto_goTypes = []interface{}{
	(ConsumerDetail_Type)(0),                 // 0: serving.ConsumerDetail.Type
	(GetTokenBarChartReq_Type)(0),            // 1: serving.GetTokenBarChartReq.Type
	(*DashboardGlobalOverView)(nil),          // 2: serving.DashboardGlobalOverView
	(*DashboardGlobalDays)(nil),              // 3: serving.DashboardGlobalDays
	(*Days)(nil),                             // 4: serving.Days
	(*DashboardGlobalBase)(nil),              // 5: serving.DashboardGlobalBase
	(*AvgFirstTimeTokenReq)(nil),             // 6: serving.AvgFirstTimeTokenReq
	(*ListBillingRuleReq)(nil),               // 7: serving.ListBillingRuleReq
	(*BillingRuleList)(nil),                  // 8: serving.BillingRuleList
	(*BillingRule)(nil),                      // 9: serving.BillingRule
	(*OperateLogReq)(nil),                    // 10: serving.OperateLogReq
	(*OperateLogPage)(nil),                   // 11: serving.OperateLogPage
	(*OperateLog)(nil),                       // 12: serving.OperateLog
	(*PortalModuleStatistics)(nil),           // 13: serving.PortalModuleStatistics
	(*OverviewReq)(nil),                      // 14: serving.OverviewReq
	(*FeatureDistribution)(nil),              // 15: serving.FeatureDistribution
	(*FeatureBucket)(nil),                    // 16: serving.FeatureBucket
	(*TrainingDataDistribution)(nil),         // 17: serving.TrainingDataDistribution
	(*DeviationIndexReq)(nil),                // 18: serving.DeviationIndexReq
	(*DeviationStateReq)(nil),                // 19: serving.DeviationStateReq
	(*ServiceVersionDeviationForShow)(nil),   // 20: serving.ServiceVersionDeviationForShow
	(*ServiceVersionDeviationForSave)(nil),   // 21: serving.ServiceVersionDeviationForSave
	(*ServiceInputStatistic)(nil),            // 22: serving.ServiceInputStatistic
	(*ServiceOutputStatistic)(nil),           // 23: serving.ServiceOutputStatistic
	(*InputDistributionStatistic)(nil),       // 24: serving.InputDistributionStatistic
	(*InputDeviationStatistic)(nil),          // 25: serving.InputDeviationStatistic
	(*ServiceInputDeviationStatistic)(nil),   // 26: serving.ServiceInputDeviationStatistic
	(*InputStatistic)(nil),                   // 27: serving.InputStatistic
	(*DeviationIndex)(nil),                   // 28: serving.DeviationIndex
	(*ResourceUsageReq)(nil),                 // 29: serving.ResourceUsageReq
	(*GetSvcMlopsGpusRsp)(nil),               // 30: serving.GetSvcMlopsGpusRsp
	(*GpuResourceUsageReq)(nil),              // 31: serving.GpuResourceUsageReq
	(*GpuCurveChart)(nil),                    // 32: serving.GpuCurveChart
	(*TimeReq)(nil),                          // 33: serving.TimeReq
	(*DashboardOverview)(nil),                // 34: serving.DashboardOverview
	(*DashboardResourceUsageCurveChart)(nil), // 35: serving.DashboardResourceUsageCurveChart
	(*DashboardCurveChart)(nil),              // 36: serving.DashboardCurveChart
	(*DashboardRequestOverview)(nil),         // 37: serving.DashboardRequestOverview
	(*DashboardRequestBarChart)(nil),         // 38: serving.DashboardRequestBarChart
	(*DashboardVisitRank)(nil),               // 39: serving.DashboardVisitRank
	(*DashboardVisitHotGraph)(nil),           // 40: serving.DashboardVisitHotGraph
	(*ServiceVisitRecordPage)(nil),           // 41: serving.ServiceVisitRecordPage
	(*StatsVisitRecordRsp)(nil),              // 42: serving.StatsVisitRecordRsp
	(*VisitCount)(nil),                       // 43: serving.VisitCount
	(*ServiceVisitRecordPageReq)(nil),        // 44: serving.ServiceVisitRecordPageReq
	(*DownloadServiceVisitRecordReq)(nil),    // 45: serving.DownloadServiceVisitRecordReq
	(*DownloadServiceVisitRecordRes)(nil),    // 46: serving.DownloadServiceVisitRecordRes
	(*ServiceVisitRecord)(nil),               // 47: serving.ServiceVisitRecord
	(*Detail)(nil),                           // 48: serving.Detail
	(*AsyncResponse)(nil),                    // 49: serving.AsyncResponse
	(*ListConsumerDetailsReq)(nil),           // 50: serving.ListConsumerDetailsReq
	(*ConsumerDetailsPage)(nil),              // 51: serving.ConsumerDetailsPage
	(*ConsumerDetail)(nil),                   // 52: serving.ConsumerDetail
	(*CountConsumptionReq)(nil),              // 53: serving.CountConsumptionReq
	(*CountConsumptionRsp)(nil),              // 54: serving.CountConsumptionRsp
	(*CountRatioReq)(nil),                    // 55: serving.CountRatioReq
	(*CountRatioRsp)(nil),                    // 56: serving.CountRatioRsp
	(*ListServiceGroupPage)(nil),             // 57: serving.ListServiceGroupPage
	(*ListServiceGroup)(nil),                 // 58: serving.ListServiceGroup
	(*IncomeGraphItem)(nil),                  // 59: serving.IncomeGraphItem
	(*TotalBilling)(nil),                     // 60: serving.TotalBilling
	(*ConsumerDetailsChart)(nil),             // 61: serving.ConsumerDetailsChart
	(*TokenUsage)(nil),                       // 62: serving.TokenUsage
	(*EvaluateAnswerReq)(nil),                // 63: serving.EvaluateAnswerReq
	(*CountTokenReq)(nil),                    // 64: serving.CountTokenReq
	(*GetTokenBarChartReq)(nil),              // 65: serving.GetTokenBarChartReq
	(*DashboardTokenBarChart)(nil),           // 66: serving.DashboardTokenBarChart
	(*CountTokenRsp)(nil),                    // 67: serving.CountTokenRsp
	(*ServiceRecordCount)(nil),               // 68: serving.ServiceRecordCount
	(*AvgFirstTimeToken)(nil),                // 69: serving.AvgFirstTimeToken
	nil,                                      // 70: serving.ServiceVersionDeviationForShow.InputDistributionStatisticEntry
	nil,                                      // 71: serving.ServiceVersionDeviationForSave.InputDistributionStatisticEntry
	nil,                                      // 72: serving.ServiceInputStatistic.InputEntry
	nil,                                      // 73: serving.ServiceOutputStatistic.CountEntry
	nil,                                      // 74: serving.ServiceInputDeviationStatistic.DeviationStatisticEntry
	nil,                                      // 75: serving.GetSvcMlopsGpusRsp.NodeAndGpusEntry
	(*GpuCurveChart_Value)(nil),              // 76: serving.GpuCurveChart.Value
	(*DashboardCurveChart_Value)(nil),        // 77: serving.DashboardCurveChart.Value
	(*DashboardRequestBarChart_Value)(nil),   // 78: serving.DashboardRequestBarChart.Value
	(*DashboardVisitRank_Value)(nil),         // 79: serving.DashboardVisitRank.Value
	(*DashboardVisitRank_RequestTime)(nil),   // 80: serving.DashboardVisitRank.RequestTime
	(*DashboardVisitHotGraph_HotGraph)(nil),  // 81: serving.DashboardVisitHotGraph.HotGraph
	nil,                                      // 82: serving.ServiceVisitRecordPage.ServiceNameMapEntry
	nil,                                      // 83: serving.ServiceVisitRecord.ChampionEntry
	nil,                                      // 84: serving.ServiceVisitRecord.CandidateEntry
	(*Detail_CommonInfo)(nil),                // 85: serving.Detail.CommonInfo
	(*Detail_RequestHeader)(nil),             // 86: serving.Detail.RequestHeader
	(*Detail_ResponseHeader)(nil),            // 87: serving.Detail.ResponseHeader
	(*Detail_ExplainerResponse)(nil),         // 88: serving.Detail.ExplainerResponse
	(*ConsumerDetailsChart_Value)(nil),       // 89: serving.ConsumerDetailsChart.Value
	(*DashboardTokenBarChart_Value)(nil),     // 90: serving.DashboardTokenBarChart.Value
	(*common.BillingConfig)(nil),             // 91: commons.BillingConfig
	(*common.PageReq)(nil),                   // 92: commons.PageReq
	(SourceType)(0),                          // 93: serving.SourceType
	(*structpb.ListValue)(nil),               // 94: google.protobuf.ListValue
	(*common.EmptyReq)(nil),                  // 95: commons.EmptyReq
	(*common.DashboardChart)(nil),            // 96: commons.DashboardChart
	(*common.EmptyRsp)(nil),                  // 97: commons.EmptyRsp
}
var file_proto_serving_audit_proto_depIdxs = []int32{
	69, // 0: serving.DashboardGlobalOverView.first_token:type_name -> serving.AvgFirstTimeToken
	5,  // 1: serving.DashboardGlobalOverView.base:type_name -> serving.DashboardGlobalBase
	4,  // 2: serving.DashboardGlobalDays.days:type_name -> serving.Days
	9,  // 3: serving.BillingRuleList.billing_rules:type_name -> serving.BillingRule
	91, // 4: serving.BillingRule.billing_config:type_name -> commons.BillingConfig
	92, // 5: serving.OperateLogReq.pageReq:type_name -> commons.PageReq
	12, // 6: serving.OperateLogPage.operateLog:type_name -> serving.OperateLog
	16, // 7: serving.FeatureDistribution.bucket:type_name -> serving.FeatureBucket
	15, // 8: serving.TrainingDataDistribution.distributions:type_name -> serving.FeatureDistribution
	22, // 9: serving.ServiceVersionDeviationForShow.inputStatistic:type_name -> serving.ServiceInputStatistic
	23, // 10: serving.ServiceVersionDeviationForShow.outputStatistic:type_name -> serving.ServiceOutputStatistic
	70, // 11: serving.ServiceVersionDeviationForShow.inputDistributionStatistic:type_name -> serving.ServiceVersionDeviationForShow.InputDistributionStatisticEntry
	26, // 12: serving.ServiceVersionDeviationForShow.inputDeviation:type_name -> serving.ServiceInputDeviationStatistic
	22, // 13: serving.ServiceVersionDeviationForSave.inputStatistic:type_name -> serving.ServiceInputStatistic
	23, // 14: serving.ServiceVersionDeviationForSave.outputStatistic:type_name -> serving.ServiceOutputStatistic
	71, // 15: serving.ServiceVersionDeviationForSave.inputDistributionStatistic:type_name -> serving.ServiceVersionDeviationForSave.InputDistributionStatisticEntry
	26, // 16: serving.ServiceVersionDeviationForSave.inputDeviation:type_name -> serving.ServiceInputDeviationStatistic
	72, // 17: serving.ServiceInputStatistic.input:type_name -> serving.ServiceInputStatistic.InputEntry
	73, // 18: serving.ServiceOutputStatistic.count:type_name -> serving.ServiceOutputStatistic.CountEntry
	74, // 19: serving.ServiceInputDeviationStatistic.deviationStatistic:type_name -> serving.ServiceInputDeviationStatistic.DeviationStatisticEntry
	33, // 20: serving.ResourceUsageReq.timeReq:type_name -> serving.TimeReq
	75, // 21: serving.GetSvcMlopsGpusRsp.NodeAndGpus:type_name -> serving.GetSvcMlopsGpusRsp.NodeAndGpusEntry
	33, // 22: serving.GpuResourceUsageReq.timeReq:type_name -> serving.TimeReq
	76, // 23: serving.GpuCurveChart.data:type_name -> serving.GpuCurveChart.Value
	77, // 24: serving.DashboardCurveChart.data:type_name -> serving.DashboardCurveChart.Value
	78, // 25: serving.DashboardRequestBarChart.data:type_name -> serving.DashboardRequestBarChart.Value
	79, // 26: serving.DashboardVisitRank.visit:type_name -> serving.DashboardVisitRank.Value
	80, // 27: serving.DashboardVisitRank.time:type_name -> serving.DashboardVisitRank.RequestTime
	81, // 28: serving.DashboardVisitHotGraph.hotGraph:type_name -> serving.DashboardVisitHotGraph.HotGraph
	47, // 29: serving.ServiceVisitRecordPage.records:type_name -> serving.ServiceVisitRecord
	82, // 30: serving.ServiceVisitRecordPage.serviceNameMap:type_name -> serving.ServiceVisitRecordPage.ServiceNameMapEntry
	43, // 31: serving.StatsVisitRecordRsp.total:type_name -> serving.VisitCount
	43, // 32: serving.StatsVisitRecordRsp.detail:type_name -> serving.VisitCount
	92, // 33: serving.ServiceVisitRecordPageReq.pageReq:type_name -> commons.PageReq
	83, // 34: serving.ServiceVisitRecord.champion:type_name -> serving.ServiceVisitRecord.ChampionEntry
	84, // 35: serving.ServiceVisitRecord.candidate:type_name -> serving.ServiceVisitRecord.CandidateEntry
	85, // 36: serving.Detail.commonInfo:type_name -> serving.Detail.CommonInfo
	86, // 37: serving.Detail.requestHeader:type_name -> serving.Detail.RequestHeader
	87, // 38: serving.Detail.responseHeader:type_name -> serving.Detail.ResponseHeader
	92, // 39: serving.ListConsumerDetailsReq.page_req:type_name -> commons.PageReq
	52, // 40: serving.ConsumerDetailsPage.consumer_details:type_name -> serving.ConsumerDetail
	62, // 41: serving.ConsumerDetail.token_usage:type_name -> serving.TokenUsage
	91, // 42: serving.ConsumerDetail.billing_config:type_name -> commons.BillingConfig
	0,  // 43: serving.ConsumerDetail.type:type_name -> serving.ConsumerDetail.Type
	61, // 44: serving.CountConsumptionRsp.chart:type_name -> serving.ConsumerDetailsChart
	60, // 45: serving.CountConsumptionRsp.total_consumption:type_name -> serving.TotalBilling
	60, // 46: serving.CountConsumptionRsp.total_income:type_name -> serving.TotalBilling
	58, // 47: serving.ListServiceGroupPage.list:type_name -> serving.ListServiceGroup
	89, // 48: serving.ConsumerDetailsChart.data:type_name -> serving.ConsumerDetailsChart.Value
	93, // 49: serving.EvaluateAnswerReq.source_type:type_name -> serving.SourceType
	1,  // 50: serving.GetTokenBarChartReq.type:type_name -> serving.GetTokenBarChartReq.Type
	90, // 51: serving.DashboardTokenBarChart.data:type_name -> serving.DashboardTokenBarChart.Value
	24, // 52: serving.ServiceVersionDeviationForShow.InputDistributionStatisticEntry.value:type_name -> serving.InputDistributionStatistic
	24, // 53: serving.ServiceVersionDeviationForSave.InputDistributionStatisticEntry.value:type_name -> serving.InputDistributionStatistic
	27, // 54: serving.ServiceInputStatistic.InputEntry.value:type_name -> serving.InputStatistic
	25, // 55: serving.ServiceInputDeviationStatistic.DeviationStatisticEntry.value:type_name -> serving.InputDeviationStatistic
	94, // 56: serving.GetSvcMlopsGpusRsp.NodeAndGpusEntry.value:type_name -> google.protobuf.ListValue
	14, // 57: serving.AuditV2Service.GetServiceOverview:input_type -> serving.OverviewReq
	33, // 58: serving.AuditV2Service.GetServiceCurveChart:input_type -> serving.TimeReq
	29, // 59: serving.AuditV2Service.GetResourceUsageCurveChart:input_type -> serving.ResourceUsageReq
	14, // 60: serving.AuditV2Service.GetRequestOverview:input_type -> serving.OverviewReq
	33, // 61: serving.AuditV2Service.GetRequestBarChart:input_type -> serving.TimeReq
	33, // 62: serving.AuditV2Service.GetFirstTokenTimeChart:input_type -> serving.TimeReq
	33, // 63: serving.AuditV2Service.GetVisitRank:input_type -> serving.TimeReq
	33, // 64: serving.AuditV2Service.GetServiceDailyVisit:input_type -> serving.TimeReq
	44, // 65: serving.AuditV2Service.GetServiceVisitRecordList:input_type -> serving.ServiceVisitRecordPageReq
	45, // 66: serving.AuditV2Service.DownloadServiceVisitRecord:input_type -> serving.DownloadServiceVisitRecordReq
	31, // 67: serving.AuditV2Service.GetGPUResourceCurveChart:input_type -> serving.GpuResourceUsageReq
	65, // 68: serving.AuditV2Service.GetTokenBarChart:input_type -> serving.GetTokenBarChartReq
	64, // 69: serving.AuditV2Service.CountToken:input_type -> serving.CountTokenReq
	63, // 70: serving.AuditV2Service.EvaluateAnswer:input_type -> serving.EvaluateAnswerReq
	6,  // 71: serving.AuditV2Service.GetAvgFirstTokenTime:input_type -> serving.AvgFirstTimeTokenReq
	95, // 72: serving.AuditV2Service.GetGlobalOverview:input_type -> commons.EmptyReq
	95, // 73: serving.AuditV2Service.GetGlobalDays:input_type -> commons.EmptyReq
	34, // 74: serving.AuditV2Service.GetServiceOverview:output_type -> serving.DashboardOverview
	36, // 75: serving.AuditV2Service.GetServiceCurveChart:output_type -> serving.DashboardCurveChart
	35, // 76: serving.AuditV2Service.GetResourceUsageCurveChart:output_type -> serving.DashboardResourceUsageCurveChart
	37, // 77: serving.AuditV2Service.GetRequestOverview:output_type -> serving.DashboardRequestOverview
	38, // 78: serving.AuditV2Service.GetRequestBarChart:output_type -> serving.DashboardRequestBarChart
	96, // 79: serving.AuditV2Service.GetFirstTokenTimeChart:output_type -> commons.DashboardChart
	39, // 80: serving.AuditV2Service.GetVisitRank:output_type -> serving.DashboardVisitRank
	40, // 81: serving.AuditV2Service.GetServiceDailyVisit:output_type -> serving.DashboardVisitHotGraph
	41, // 82: serving.AuditV2Service.GetServiceVisitRecordList:output_type -> serving.ServiceVisitRecordPage
	46, // 83: serving.AuditV2Service.DownloadServiceVisitRecord:output_type -> serving.DownloadServiceVisitRecordRes
	32, // 84: serving.AuditV2Service.GetGPUResourceCurveChart:output_type -> serving.GpuCurveChart
	66, // 85: serving.AuditV2Service.GetTokenBarChart:output_type -> serving.DashboardTokenBarChart
	67, // 86: serving.AuditV2Service.CountToken:output_type -> serving.CountTokenRsp
	97, // 87: serving.AuditV2Service.EvaluateAnswer:output_type -> commons.EmptyRsp
	69, // 88: serving.AuditV2Service.GetAvgFirstTokenTime:output_type -> serving.AvgFirstTimeToken
	2,  // 89: serving.AuditV2Service.GetGlobalOverview:output_type -> serving.DashboardGlobalOverView
	3,  // 90: serving.AuditV2Service.GetGlobalDays:output_type -> serving.DashboardGlobalDays
	74, // [74:91] is the sub-list for method output_type
	57, // [57:74] is the sub-list for method input_type
	57, // [57:57] is the sub-list for extension type_name
	57, // [57:57] is the sub-list for extension extendee
	0,  // [0:57] is the sub-list for field type_name
}

func init() { file_proto_serving_audit_proto_init() }
func file_proto_serving_audit_proto_init() {
	if File_proto_serving_audit_proto != nil {
		return
	}
	file_proto_serving_mlops_service_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_serving_audit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardGlobalOverView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardGlobalDays); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Days); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardGlobalBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvgFirstTimeTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBillingRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingRuleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateLogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateLogPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortalModuleStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverviewReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureDistribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureBucket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainingDataDistribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviationIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviationStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceVersionDeviationForShow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceVersionDeviationForSave); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInputStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceOutputStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputDistributionStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputDeviationStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInputDeviationStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviationIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceUsageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSvcMlopsGpusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GpuResourceUsageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GpuCurveChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResourceUsageCurveChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardCurveChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardRequestOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardRequestBarChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardVisitRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardVisitHotGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceVisitRecordPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatsVisitRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisitCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceVisitRecordPageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadServiceVisitRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadServiceVisitRecordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceVisitRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AsyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConsumerDetailsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumerDetailsPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumerDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountConsumptionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountConsumptionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountRatioReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountRatioRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceGroupPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncomeGraphItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TotalBilling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumerDetailsChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateAnswerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenBarChartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardTokenBarChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceRecordCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvgFirstTimeToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GpuCurveChart_Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardCurveChart_Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardRequestBarChart_Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardVisitRank_Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardVisitRank_RequestTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardVisitHotGraph_HotGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detail_CommonInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detail_RequestHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detail_ResponseHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detail_ExplainerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumerDetailsChart_Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_serving_audit_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardTokenBarChart_Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_serving_audit_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   89,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_serving_audit_proto_goTypes,
		DependencyIndexes: file_proto_serving_audit_proto_depIdxs,
		EnumInfos:         file_proto_serving_audit_proto_enumTypes,
		MessageInfos:      file_proto_serving_audit_proto_msgTypes,
	}.Build()
	File_proto_serving_audit_proto = out.File
	file_proto_serving_audit_proto_rawDesc = nil
	file_proto_serving_audit_proto_goTypes = nil
	file_proto_serving_audit_proto_depIdxs = nil
}
