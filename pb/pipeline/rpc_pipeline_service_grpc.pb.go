// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/pipeline/rpc_pipeline_service.proto

package pipeline

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "transwarp.io/aip/llmops-common/pb/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PipelineService_CreatePipeline_FullMethodName                 = "/pipeline.PipelineService/CreatePipeline"
	PipelineService_ModifyPipeline_FullMethodName                 = "/pipeline.PipelineService/ModifyPipeline"
	PipelineService_CreatePipelineVersion_FullMethodName          = "/pipeline.PipelineService/CreatePipelineVersion"
	PipelineService_SavePipelineVersion_FullMethodName            = "/pipeline.PipelineService/SavePipelineVersion"
	PipelineService_SavePipelineVersionV2_FullMethodName          = "/pipeline.PipelineService/SavePipelineVersionV2"
	PipelineService_GetPipeline_FullMethodName                    = "/pipeline.PipelineService/GetPipeline"
	PipelineService_GetPipelineVersion_FullMethodName             = "/pipeline.PipelineService/GetPipelineVersion"
	PipelineService_GetPipelineVersionV2_FullMethodName           = "/pipeline.PipelineService/GetPipelineVersionV2"
	PipelineService_DeletePipeline_FullMethodName                 = "/pipeline.PipelineService/DeletePipeline"
	PipelineService_DeletePipelines_FullMethodName                = "/pipeline.PipelineService/DeletePipelines"
	PipelineService_DeletePipelineVersion_FullMethodName          = "/pipeline.PipelineService/DeletePipelineVersion"
	PipelineService_ListPipelines_FullMethodName                  = "/pipeline.PipelineService/ListPipelines"
	PipelineService_ListPipelineVersions_FullMethodName           = "/pipeline.PipelineService/ListPipelineVersions"
	PipelineService_DeletePipelineVersions_FullMethodName         = "/pipeline.PipelineService/DeletePipelineVersions"
	PipelineService_StartPipelineVersion_FullMethodName           = "/pipeline.PipelineService/StartPipelineVersion"
	PipelineService_StopPipelineVersion_FullMethodName            = "/pipeline.PipelineService/StopPipelineVersion"
	PipelineService_OncePipelineVersion_FullMethodName            = "/pipeline.PipelineService/OncePipelineVersion"
	PipelineService_CreatePipelineVersionByYaml_FullMethodName    = "/pipeline.PipelineService/CreatePipelineVersionByYaml"
	PipelineService_ExportPipelineVersion_FullMethodName          = "/pipeline.PipelineService/ExportPipelineVersion"
	PipelineService_GetComponents_FullMethodName                  = "/pipeline.PipelineService/GetComponents"
	PipelineService_CheckPipelineNameUnique_FullMethodName        = "/pipeline.PipelineService/CheckPipelineNameUnique"
	PipelineService_CheckPipelineVersionNameUnique_FullMethodName = "/pipeline.PipelineService/CheckPipelineVersionNameUnique"
)

// PipelineServiceClient is the client API for PipelineService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PipelineServiceClient interface {
	CreatePipeline(ctx context.Context, in *CreatePipelineReq, opts ...grpc.CallOption) (*PipelineId, error)
	ModifyPipeline(ctx context.Context, in *ModifyPipelineReq, opts ...grpc.CallOption) (*PipelineId, error)
	CreatePipelineVersion(ctx context.Context, in *CreatePipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error)
	SavePipelineVersion(ctx context.Context, in *SavePipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error)
	SavePipelineVersionV2(ctx context.Context, in *SavePipelineVersionReqV2, opts ...grpc.CallOption) (*PipelineVersionId, error)
	GetPipeline(ctx context.Context, in *GetPipelineReq, opts ...grpc.CallOption) (*Pipeline, error)
	GetPipelineVersion(ctx context.Context, in *GetPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersion, error)
	GetPipelineVersionV2(ctx context.Context, in *GetPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionV2, error)
	DeletePipeline(ctx context.Context, in *DeletePipelineReq, opts ...grpc.CallOption) (*common.DeleteRsp, error)
	DeletePipelines(ctx context.Context, in *DeletePipelinesReq, opts ...grpc.CallOption) (*common.DeleteRsp, error)
	DeletePipelineVersion(ctx context.Context, in *DeletePipelineVersionReq, opts ...grpc.CallOption) (*common.DeleteRsp, error)
	ListPipelines(ctx context.Context, in *ListPipelinesReq, opts ...grpc.CallOption) (*PipelinePage, error)
	ListPipelineVersions(ctx context.Context, in *ListPipelineVersionsReq, opts ...grpc.CallOption) (*PipelineVersionPage, error)
	DeletePipelineVersions(ctx context.Context, in *DeletePipelineVersionsReq, opts ...grpc.CallOption) (*common.DeleteRsp, error)
	StartPipelineVersion(ctx context.Context, in *StartPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error)
	StopPipelineVersion(ctx context.Context, in *StopPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error)
	OncePipelineVersion(ctx context.Context, in *OncePipelineVersionReq, opts ...grpc.CallOption) (*OncePipelineVersionRsp, error)
	CreatePipelineVersionByYaml(ctx context.Context, in *CreatePipelineVersionByYamlReq, opts ...grpc.CallOption) (*PipelineVersionId, error)
	ExportPipelineVersion(ctx context.Context, in *ExportPipelineVersionReq, opts ...grpc.CallOption) (*ExportPipelineVersionRsp, error)
	GetComponents(ctx context.Context, in *GetComponentsReq, opts ...grpc.CallOption) (*ComponentPage, error)
	CheckPipelineNameUnique(ctx context.Context, in *CheckPipelineReq, opts ...grpc.CallOption) (*CheckPipelineRsp, error)
	CheckPipelineVersionNameUnique(ctx context.Context, in *CheckPipelineVersionReq, opts ...grpc.CallOption) (*CheckPipelineVersionRsp, error)
}

type pipelineServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPipelineServiceClient(cc grpc.ClientConnInterface) PipelineServiceClient {
	return &pipelineServiceClient{cc}
}

func (c *pipelineServiceClient) CreatePipeline(ctx context.Context, in *CreatePipelineReq, opts ...grpc.CallOption) (*PipelineId, error) {
	out := new(PipelineId)
	err := c.cc.Invoke(ctx, PipelineService_CreatePipeline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) ModifyPipeline(ctx context.Context, in *ModifyPipelineReq, opts ...grpc.CallOption) (*PipelineId, error) {
	out := new(PipelineId)
	err := c.cc.Invoke(ctx, PipelineService_ModifyPipeline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) CreatePipelineVersion(ctx context.Context, in *CreatePipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error) {
	out := new(PipelineVersionId)
	err := c.cc.Invoke(ctx, PipelineService_CreatePipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) SavePipelineVersion(ctx context.Context, in *SavePipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error) {
	out := new(PipelineVersionId)
	err := c.cc.Invoke(ctx, PipelineService_SavePipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) SavePipelineVersionV2(ctx context.Context, in *SavePipelineVersionReqV2, opts ...grpc.CallOption) (*PipelineVersionId, error) {
	out := new(PipelineVersionId)
	err := c.cc.Invoke(ctx, PipelineService_SavePipelineVersionV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetPipeline(ctx context.Context, in *GetPipelineReq, opts ...grpc.CallOption) (*Pipeline, error) {
	out := new(Pipeline)
	err := c.cc.Invoke(ctx, PipelineService_GetPipeline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetPipelineVersion(ctx context.Context, in *GetPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersion, error) {
	out := new(PipelineVersion)
	err := c.cc.Invoke(ctx, PipelineService_GetPipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetPipelineVersionV2(ctx context.Context, in *GetPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionV2, error) {
	out := new(PipelineVersionV2)
	err := c.cc.Invoke(ctx, PipelineService_GetPipelineVersionV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) DeletePipeline(ctx context.Context, in *DeletePipelineReq, opts ...grpc.CallOption) (*common.DeleteRsp, error) {
	out := new(common.DeleteRsp)
	err := c.cc.Invoke(ctx, PipelineService_DeletePipeline_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) DeletePipelines(ctx context.Context, in *DeletePipelinesReq, opts ...grpc.CallOption) (*common.DeleteRsp, error) {
	out := new(common.DeleteRsp)
	err := c.cc.Invoke(ctx, PipelineService_DeletePipelines_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) DeletePipelineVersion(ctx context.Context, in *DeletePipelineVersionReq, opts ...grpc.CallOption) (*common.DeleteRsp, error) {
	out := new(common.DeleteRsp)
	err := c.cc.Invoke(ctx, PipelineService_DeletePipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) ListPipelines(ctx context.Context, in *ListPipelinesReq, opts ...grpc.CallOption) (*PipelinePage, error) {
	out := new(PipelinePage)
	err := c.cc.Invoke(ctx, PipelineService_ListPipelines_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) ListPipelineVersions(ctx context.Context, in *ListPipelineVersionsReq, opts ...grpc.CallOption) (*PipelineVersionPage, error) {
	out := new(PipelineVersionPage)
	err := c.cc.Invoke(ctx, PipelineService_ListPipelineVersions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) DeletePipelineVersions(ctx context.Context, in *DeletePipelineVersionsReq, opts ...grpc.CallOption) (*common.DeleteRsp, error) {
	out := new(common.DeleteRsp)
	err := c.cc.Invoke(ctx, PipelineService_DeletePipelineVersions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) StartPipelineVersion(ctx context.Context, in *StartPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error) {
	out := new(PipelineVersionId)
	err := c.cc.Invoke(ctx, PipelineService_StartPipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) StopPipelineVersion(ctx context.Context, in *StopPipelineVersionReq, opts ...grpc.CallOption) (*PipelineVersionId, error) {
	out := new(PipelineVersionId)
	err := c.cc.Invoke(ctx, PipelineService_StopPipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) OncePipelineVersion(ctx context.Context, in *OncePipelineVersionReq, opts ...grpc.CallOption) (*OncePipelineVersionRsp, error) {
	out := new(OncePipelineVersionRsp)
	err := c.cc.Invoke(ctx, PipelineService_OncePipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) CreatePipelineVersionByYaml(ctx context.Context, in *CreatePipelineVersionByYamlReq, opts ...grpc.CallOption) (*PipelineVersionId, error) {
	out := new(PipelineVersionId)
	err := c.cc.Invoke(ctx, PipelineService_CreatePipelineVersionByYaml_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) ExportPipelineVersion(ctx context.Context, in *ExportPipelineVersionReq, opts ...grpc.CallOption) (*ExportPipelineVersionRsp, error) {
	out := new(ExportPipelineVersionRsp)
	err := c.cc.Invoke(ctx, PipelineService_ExportPipelineVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) GetComponents(ctx context.Context, in *GetComponentsReq, opts ...grpc.CallOption) (*ComponentPage, error) {
	out := new(ComponentPage)
	err := c.cc.Invoke(ctx, PipelineService_GetComponents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) CheckPipelineNameUnique(ctx context.Context, in *CheckPipelineReq, opts ...grpc.CallOption) (*CheckPipelineRsp, error) {
	out := new(CheckPipelineRsp)
	err := c.cc.Invoke(ctx, PipelineService_CheckPipelineNameUnique_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pipelineServiceClient) CheckPipelineVersionNameUnique(ctx context.Context, in *CheckPipelineVersionReq, opts ...grpc.CallOption) (*CheckPipelineVersionRsp, error) {
	out := new(CheckPipelineVersionRsp)
	err := c.cc.Invoke(ctx, PipelineService_CheckPipelineVersionNameUnique_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PipelineServiceServer is the server API for PipelineService service.
// All implementations must embed UnimplementedPipelineServiceServer
// for forward compatibility
type PipelineServiceServer interface {
	CreatePipeline(context.Context, *CreatePipelineReq) (*PipelineId, error)
	ModifyPipeline(context.Context, *ModifyPipelineReq) (*PipelineId, error)
	CreatePipelineVersion(context.Context, *CreatePipelineVersionReq) (*PipelineVersionId, error)
	SavePipelineVersion(context.Context, *SavePipelineVersionReq) (*PipelineVersionId, error)
	SavePipelineVersionV2(context.Context, *SavePipelineVersionReqV2) (*PipelineVersionId, error)
	GetPipeline(context.Context, *GetPipelineReq) (*Pipeline, error)
	GetPipelineVersion(context.Context, *GetPipelineVersionReq) (*PipelineVersion, error)
	GetPipelineVersionV2(context.Context, *GetPipelineVersionReq) (*PipelineVersionV2, error)
	DeletePipeline(context.Context, *DeletePipelineReq) (*common.DeleteRsp, error)
	DeletePipelines(context.Context, *DeletePipelinesReq) (*common.DeleteRsp, error)
	DeletePipelineVersion(context.Context, *DeletePipelineVersionReq) (*common.DeleteRsp, error)
	ListPipelines(context.Context, *ListPipelinesReq) (*PipelinePage, error)
	ListPipelineVersions(context.Context, *ListPipelineVersionsReq) (*PipelineVersionPage, error)
	DeletePipelineVersions(context.Context, *DeletePipelineVersionsReq) (*common.DeleteRsp, error)
	StartPipelineVersion(context.Context, *StartPipelineVersionReq) (*PipelineVersionId, error)
	StopPipelineVersion(context.Context, *StopPipelineVersionReq) (*PipelineVersionId, error)
	OncePipelineVersion(context.Context, *OncePipelineVersionReq) (*OncePipelineVersionRsp, error)
	CreatePipelineVersionByYaml(context.Context, *CreatePipelineVersionByYamlReq) (*PipelineVersionId, error)
	ExportPipelineVersion(context.Context, *ExportPipelineVersionReq) (*ExportPipelineVersionRsp, error)
	GetComponents(context.Context, *GetComponentsReq) (*ComponentPage, error)
	CheckPipelineNameUnique(context.Context, *CheckPipelineReq) (*CheckPipelineRsp, error)
	CheckPipelineVersionNameUnique(context.Context, *CheckPipelineVersionReq) (*CheckPipelineVersionRsp, error)
	mustEmbedUnimplementedPipelineServiceServer()
}

// UnimplementedPipelineServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPipelineServiceServer struct {
}

func (UnimplementedPipelineServiceServer) CreatePipeline(context.Context, *CreatePipelineReq) (*PipelineId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePipeline not implemented")
}
func (UnimplementedPipelineServiceServer) ModifyPipeline(context.Context, *ModifyPipelineReq) (*PipelineId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyPipeline not implemented")
}
func (UnimplementedPipelineServiceServer) CreatePipelineVersion(context.Context, *CreatePipelineVersionReq) (*PipelineVersionId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) SavePipelineVersion(context.Context, *SavePipelineVersionReq) (*PipelineVersionId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SavePipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) SavePipelineVersionV2(context.Context, *SavePipelineVersionReqV2) (*PipelineVersionId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SavePipelineVersionV2 not implemented")
}
func (UnimplementedPipelineServiceServer) GetPipeline(context.Context, *GetPipelineReq) (*Pipeline, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipeline not implemented")
}
func (UnimplementedPipelineServiceServer) GetPipelineVersion(context.Context, *GetPipelineVersionReq) (*PipelineVersion, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) GetPipelineVersionV2(context.Context, *GetPipelineVersionReq) (*PipelineVersionV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineVersionV2 not implemented")
}
func (UnimplementedPipelineServiceServer) DeletePipeline(context.Context, *DeletePipelineReq) (*common.DeleteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePipeline not implemented")
}
func (UnimplementedPipelineServiceServer) DeletePipelines(context.Context, *DeletePipelinesReq) (*common.DeleteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePipelines not implemented")
}
func (UnimplementedPipelineServiceServer) DeletePipelineVersion(context.Context, *DeletePipelineVersionReq) (*common.DeleteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) ListPipelines(context.Context, *ListPipelinesReq) (*PipelinePage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPipelines not implemented")
}
func (UnimplementedPipelineServiceServer) ListPipelineVersions(context.Context, *ListPipelineVersionsReq) (*PipelineVersionPage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPipelineVersions not implemented")
}
func (UnimplementedPipelineServiceServer) DeletePipelineVersions(context.Context, *DeletePipelineVersionsReq) (*common.DeleteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePipelineVersions not implemented")
}
func (UnimplementedPipelineServiceServer) StartPipelineVersion(context.Context, *StartPipelineVersionReq) (*PipelineVersionId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) StopPipelineVersion(context.Context, *StopPipelineVersionReq) (*PipelineVersionId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopPipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) OncePipelineVersion(context.Context, *OncePipelineVersionReq) (*OncePipelineVersionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OncePipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) CreatePipelineVersionByYaml(context.Context, *CreatePipelineVersionByYamlReq) (*PipelineVersionId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePipelineVersionByYaml not implemented")
}
func (UnimplementedPipelineServiceServer) ExportPipelineVersion(context.Context, *ExportPipelineVersionReq) (*ExportPipelineVersionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportPipelineVersion not implemented")
}
func (UnimplementedPipelineServiceServer) GetComponents(context.Context, *GetComponentsReq) (*ComponentPage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetComponents not implemented")
}
func (UnimplementedPipelineServiceServer) CheckPipelineNameUnique(context.Context, *CheckPipelineReq) (*CheckPipelineRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPipelineNameUnique not implemented")
}
func (UnimplementedPipelineServiceServer) CheckPipelineVersionNameUnique(context.Context, *CheckPipelineVersionReq) (*CheckPipelineVersionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPipelineVersionNameUnique not implemented")
}
func (UnimplementedPipelineServiceServer) mustEmbedUnimplementedPipelineServiceServer() {}

// UnsafePipelineServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PipelineServiceServer will
// result in compilation errors.
type UnsafePipelineServiceServer interface {
	mustEmbedUnimplementedPipelineServiceServer()
}

func RegisterPipelineServiceServer(s grpc.ServiceRegistrar, srv PipelineServiceServer) {
	s.RegisterService(&PipelineService_ServiceDesc, srv)
}

func _PipelineService_CreatePipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).CreatePipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_CreatePipeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).CreatePipeline(ctx, req.(*CreatePipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_ModifyPipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).ModifyPipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_ModifyPipeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).ModifyPipeline(ctx, req.(*ModifyPipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_CreatePipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).CreatePipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_CreatePipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).CreatePipelineVersion(ctx, req.(*CreatePipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_SavePipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SavePipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).SavePipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_SavePipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).SavePipelineVersion(ctx, req.(*SavePipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_SavePipelineVersionV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SavePipelineVersionReqV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).SavePipelineVersionV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_SavePipelineVersionV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).SavePipelineVersionV2(ctx, req.(*SavePipelineVersionReqV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetPipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetPipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetPipeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetPipeline(ctx, req.(*GetPipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetPipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetPipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetPipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetPipelineVersion(ctx, req.(*GetPipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetPipelineVersionV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetPipelineVersionV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetPipelineVersionV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetPipelineVersionV2(ctx, req.(*GetPipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_DeletePipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).DeletePipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_DeletePipeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).DeletePipeline(ctx, req.(*DeletePipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_DeletePipelines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePipelinesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).DeletePipelines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_DeletePipelines_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).DeletePipelines(ctx, req.(*DeletePipelinesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_DeletePipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).DeletePipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_DeletePipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).DeletePipelineVersion(ctx, req.(*DeletePipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_ListPipelines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPipelinesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).ListPipelines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_ListPipelines_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).ListPipelines(ctx, req.(*ListPipelinesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_ListPipelineVersions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPipelineVersionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).ListPipelineVersions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_ListPipelineVersions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).ListPipelineVersions(ctx, req.(*ListPipelineVersionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_DeletePipelineVersions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePipelineVersionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).DeletePipelineVersions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_DeletePipelineVersions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).DeletePipelineVersions(ctx, req.(*DeletePipelineVersionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_StartPipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).StartPipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_StartPipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).StartPipelineVersion(ctx, req.(*StartPipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_StopPipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopPipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).StopPipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_StopPipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).StopPipelineVersion(ctx, req.(*StopPipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_OncePipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OncePipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).OncePipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_OncePipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).OncePipelineVersion(ctx, req.(*OncePipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_CreatePipelineVersionByYaml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePipelineVersionByYamlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).CreatePipelineVersionByYaml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_CreatePipelineVersionByYaml_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).CreatePipelineVersionByYaml(ctx, req.(*CreatePipelineVersionByYamlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_ExportPipelineVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportPipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).ExportPipelineVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_ExportPipelineVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).ExportPipelineVersion(ctx, req.(*ExportPipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_GetComponents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetComponentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).GetComponents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_GetComponents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).GetComponents(ctx, req.(*GetComponentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_CheckPipelineNameUnique_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).CheckPipelineNameUnique(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_CheckPipelineNameUnique_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).CheckPipelineNameUnique(ctx, req.(*CheckPipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PipelineService_CheckPipelineVersionNameUnique_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPipelineVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PipelineServiceServer).CheckPipelineVersionNameUnique(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PipelineService_CheckPipelineVersionNameUnique_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PipelineServiceServer).CheckPipelineVersionNameUnique(ctx, req.(*CheckPipelineVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PipelineService_ServiceDesc is the grpc.ServiceDesc for PipelineService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PipelineService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pipeline.PipelineService",
	HandlerType: (*PipelineServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePipeline",
			Handler:    _PipelineService_CreatePipeline_Handler,
		},
		{
			MethodName: "ModifyPipeline",
			Handler:    _PipelineService_ModifyPipeline_Handler,
		},
		{
			MethodName: "CreatePipelineVersion",
			Handler:    _PipelineService_CreatePipelineVersion_Handler,
		},
		{
			MethodName: "SavePipelineVersion",
			Handler:    _PipelineService_SavePipelineVersion_Handler,
		},
		{
			MethodName: "SavePipelineVersionV2",
			Handler:    _PipelineService_SavePipelineVersionV2_Handler,
		},
		{
			MethodName: "GetPipeline",
			Handler:    _PipelineService_GetPipeline_Handler,
		},
		{
			MethodName: "GetPipelineVersion",
			Handler:    _PipelineService_GetPipelineVersion_Handler,
		},
		{
			MethodName: "GetPipelineVersionV2",
			Handler:    _PipelineService_GetPipelineVersionV2_Handler,
		},
		{
			MethodName: "DeletePipeline",
			Handler:    _PipelineService_DeletePipeline_Handler,
		},
		{
			MethodName: "DeletePipelines",
			Handler:    _PipelineService_DeletePipelines_Handler,
		},
		{
			MethodName: "DeletePipelineVersion",
			Handler:    _PipelineService_DeletePipelineVersion_Handler,
		},
		{
			MethodName: "ListPipelines",
			Handler:    _PipelineService_ListPipelines_Handler,
		},
		{
			MethodName: "ListPipelineVersions",
			Handler:    _PipelineService_ListPipelineVersions_Handler,
		},
		{
			MethodName: "DeletePipelineVersions",
			Handler:    _PipelineService_DeletePipelineVersions_Handler,
		},
		{
			MethodName: "StartPipelineVersion",
			Handler:    _PipelineService_StartPipelineVersion_Handler,
		},
		{
			MethodName: "StopPipelineVersion",
			Handler:    _PipelineService_StopPipelineVersion_Handler,
		},
		{
			MethodName: "OncePipelineVersion",
			Handler:    _PipelineService_OncePipelineVersion_Handler,
		},
		{
			MethodName: "CreatePipelineVersionByYaml",
			Handler:    _PipelineService_CreatePipelineVersionByYaml_Handler,
		},
		{
			MethodName: "ExportPipelineVersion",
			Handler:    _PipelineService_ExportPipelineVersion_Handler,
		},
		{
			MethodName: "GetComponents",
			Handler:    _PipelineService_GetComponents_Handler,
		},
		{
			MethodName: "CheckPipelineNameUnique",
			Handler:    _PipelineService_CheckPipelineNameUnique_Handler,
		},
		{
			MethodName: "CheckPipelineVersionNameUnique",
			Handler:    _PipelineService_CheckPipelineVersionNameUnique_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/pipeline/rpc_pipeline_service.proto",
}
