// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/audit_record.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuditRecordOperateType int32

const (
	AuditRecordOperateType_CREATE AuditRecordOperateType = 0
	AuditRecordOperateType_UPDATE AuditRecordOperateType = 1
	AuditRecordOperateType_DELETE AuditRecordOperateType = 2
)

// Enum value maps for AuditRecordOperateType.
var (
	AuditRecordOperateType_name = map[int32]string{
		0: "CREATE",
		1: "UPDATE",
		2: "DELETE",
	}
	AuditRecordOperateType_value = map[string]int32{
		"CREATE": 0,
		"UPDATE": 1,
		"DELETE": 2,
	}
)

func (x AuditRecordOperateType) Enum() *AuditRecordOperateType {
	p := new(AuditRecordOperateType)
	*p = x
	return p
}

func (x AuditRecordOperateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditRecordOperateType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_audit_record_proto_enumTypes[0].Descriptor()
}

func (AuditRecordOperateType) Type() protoreflect.EnumType {
	return &file_proto_audit_record_proto_enumTypes[0]
}

func (x AuditRecordOperateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditRecordOperateType.Descriptor instead.
func (AuditRecordOperateType) EnumDescriptor() ([]byte, []int) {
	return file_proto_audit_record_proto_rawDescGZIP(), []int{0}
}

type AuditRecordModule int32

const (
	// @gotags: description:"空间管理"
	AuditRecordModule_AuditRecordModule_SPACE AuditRecordModule = 0
	// @gotags: description:"模型"
	AuditRecordModule_AuditRecordModule_MODEL AuditRecordModule = 1
	// @gotags: description:"应用"
	AuditRecordModule_AuditRecordModule_APP AuditRecordModule = 2
	// @gotags: description:"知识"
	AuditRecordModule_AuditRecordModule_KNOWLEDGE AuditRecordModule = 3
	// @gotags: description:"语料"
	AuditRecordModule_AuditRecordModule_CORPUS AuditRecordModule = 4
	// @gotags: description:"运维工具"
	AuditRecordModule_AuditRecordModule_TOOL AuditRecordModule = 5
)

// Enum value maps for AuditRecordModule.
var (
	AuditRecordModule_name = map[int32]string{
		0: "AuditRecordModule_SPACE",
		1: "AuditRecordModule_MODEL",
		2: "AuditRecordModule_APP",
		3: "AuditRecordModule_KNOWLEDGE",
		4: "AuditRecordModule_CORPUS",
		5: "AuditRecordModule_TOOL",
	}
	AuditRecordModule_value = map[string]int32{
		"AuditRecordModule_SPACE":     0,
		"AuditRecordModule_MODEL":     1,
		"AuditRecordModule_APP":       2,
		"AuditRecordModule_KNOWLEDGE": 3,
		"AuditRecordModule_CORPUS":    4,
		"AuditRecordModule_TOOL":      5,
	}
)

func (x AuditRecordModule) Enum() *AuditRecordModule {
	p := new(AuditRecordModule)
	*p = x
	return p
}

func (x AuditRecordModule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditRecordModule) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_audit_record_proto_enumTypes[1].Descriptor()
}

func (AuditRecordModule) Type() protoreflect.EnumType {
	return &file_proto_audit_record_proto_enumTypes[1]
}

func (x AuditRecordModule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditRecordModule.Descriptor instead.
func (AuditRecordModule) EnumDescriptor() ([]byte, []int) {
	return file_proto_audit_record_proto_rawDescGZIP(), []int{1}
}

// 功能菜单 包含了空间信息, 成员管理, 模型管理, 模型体验, 模型训练, 模型评估, 提示工程, 应用管理, 应用体验, 应用插件, 自定义算子, 应用评估, 知识管理, 服务部署, 安全中心, 代码实例, 工作流
type AuditRecordSubModule int32

const (
	// Space
	// @gotags: description:"空间信息管理"
	AuditRecordSubModule_AuditRecordSubModule_SPACE_INFO_MANAGEMENT AuditRecordSubModule = 0
	// @gotags: description:"成员管理"
	AuditRecordSubModule_AuditRecordSubModule_SPACE_MEMBER_MANAGEMENT AuditRecordSubModule = 1
	// Model
	// @gotags: description:"模型管理"
	AuditRecordSubModule_AuditRecordSubModule_MODEL_MANAGEMENT AuditRecordSubModule = 10
	// @gotags: description:"模型体验"
	AuditRecordSubModule_AuditRecordSubModule_MODEL_EXPERIENCE AuditRecordSubModule = 11
	// @gotags: description:"模型训练"
	AuditRecordSubModule_AuditRecordSubModule_MODEL_TRAINING AuditRecordSubModule = 12
	// @gotags: description:"模型评估"
	AuditRecordSubModule_AuditRecordSubModule_MODEL_EVALUATION AuditRecordSubModule = 13
	// @gotags: description:"提示工程"
	AuditRecordSubModule_AuditRecordSubModule_PROMPT_ENGINEERING AuditRecordSubModule = 14
	// App
	// @gotags: description:"应用管理"
	AuditRecordSubModule_AuditRecordSubModule_APP_MANAGEMENT AuditRecordSubModule = 20
	// @gotags: description:"应用体验"
	AuditRecordSubModule_AuditRecordSubModule_APP_EXPERIENCE AuditRecordSubModule = 21
	// @gotags: description:"应用插件管理"
	AuditRecordSubModule_AuditRecordSubModule_APP_PLUGIN_MANAGEMENT AuditRecordSubModule = 22
	// @gotags: description:"自定义算子"
	AuditRecordSubModule_AuditRecordSubModule_CUSTOM_OPERATOR AuditRecordSubModule = 23
	// @gotags: description:"应用评估"
	AuditRecordSubModule_AuditRecordSubModule_APP_EVALUATION AuditRecordSubModule = 24
	// Knowledge
	// @gotags: description:"知识管理"
	AuditRecordSubModule_AuditRecordSubModule_KNOWLEDGE_MANAGEMENT AuditRecordSubModule = 30
	// @gotags: description:"知识体验"
	AuditRecordSubModule_AuditRecordSubModule_KNOWLEDGE_EXPERIENCE AuditRecordSubModule = 31
	// Corpus
	// @gotags: description:"语料管理"
	AuditRecordSubModule_AuditRecordSubModule_CORPUS_MANAGEMENT AuditRecordSubModule = 40
	// @gotags: description:"语料处理"
	AuditRecordSubModule_AuditRecordSubModule_CORPUS_PROCESSING AuditRecordSubModule = 41
	// @gotags: description:"语料标注"
	AuditRecordSubModule_AuditRecordSubModule_CORPUS_LABELING AuditRecordSubModule = 42
	// @gotags: description:"语料评测"
	AuditRecordSubModule_AuditRecordSubModule_CORPUS_EVALUATING AuditRecordSubModule = 43
	// Tool
	// @gotags: description:"服务部署"
	AuditRecordSubModule_AuditRecordSubModule_SERVICE_DEPLOYMENT AuditRecordSubModule = 50
	// @gotags: description:"安全中心"
	AuditRecordSubModule_AuditRecordSubModule_SECURITY_CENTER AuditRecordSubModule = 51
	// @gotags: description:"代码实例"
	AuditRecordSubModule_AuditRecordSubModule_CODE_EXAMPLES AuditRecordSubModule = 52
	// @gotags: description:"工作流管理"
	AuditRecordSubModule_AuditRecordSubModule_WORKFLOW_MANAGEMENT AuditRecordSubModule = 53
)

// Enum value maps for AuditRecordSubModule.
var (
	AuditRecordSubModule_name = map[int32]string{
		0:  "AuditRecordSubModule_SPACE_INFO_MANAGEMENT",
		1:  "AuditRecordSubModule_SPACE_MEMBER_MANAGEMENT",
		10: "AuditRecordSubModule_MODEL_MANAGEMENT",
		11: "AuditRecordSubModule_MODEL_EXPERIENCE",
		12: "AuditRecordSubModule_MODEL_TRAINING",
		13: "AuditRecordSubModule_MODEL_EVALUATION",
		14: "AuditRecordSubModule_PROMPT_ENGINEERING",
		20: "AuditRecordSubModule_APP_MANAGEMENT",
		21: "AuditRecordSubModule_APP_EXPERIENCE",
		22: "AuditRecordSubModule_APP_PLUGIN_MANAGEMENT",
		23: "AuditRecordSubModule_CUSTOM_OPERATOR",
		24: "AuditRecordSubModule_APP_EVALUATION",
		30: "AuditRecordSubModule_KNOWLEDGE_MANAGEMENT",
		31: "AuditRecordSubModule_KNOWLEDGE_EXPERIENCE",
		40: "AuditRecordSubModule_CORPUS_MANAGEMENT",
		41: "AuditRecordSubModule_CORPUS_PROCESSING",
		42: "AuditRecordSubModule_CORPUS_LABELING",
		43: "AuditRecordSubModule_CORPUS_EVALUATING",
		50: "AuditRecordSubModule_SERVICE_DEPLOYMENT",
		51: "AuditRecordSubModule_SECURITY_CENTER",
		52: "AuditRecordSubModule_CODE_EXAMPLES",
		53: "AuditRecordSubModule_WORKFLOW_MANAGEMENT",
	}
	AuditRecordSubModule_value = map[string]int32{
		"AuditRecordSubModule_SPACE_INFO_MANAGEMENT":   0,
		"AuditRecordSubModule_SPACE_MEMBER_MANAGEMENT": 1,
		"AuditRecordSubModule_MODEL_MANAGEMENT":        10,
		"AuditRecordSubModule_MODEL_EXPERIENCE":        11,
		"AuditRecordSubModule_MODEL_TRAINING":          12,
		"AuditRecordSubModule_MODEL_EVALUATION":        13,
		"AuditRecordSubModule_PROMPT_ENGINEERING":      14,
		"AuditRecordSubModule_APP_MANAGEMENT":          20,
		"AuditRecordSubModule_APP_EXPERIENCE":          21,
		"AuditRecordSubModule_APP_PLUGIN_MANAGEMENT":   22,
		"AuditRecordSubModule_CUSTOM_OPERATOR":         23,
		"AuditRecordSubModule_APP_EVALUATION":          24,
		"AuditRecordSubModule_KNOWLEDGE_MANAGEMENT":    30,
		"AuditRecordSubModule_KNOWLEDGE_EXPERIENCE":    31,
		"AuditRecordSubModule_CORPUS_MANAGEMENT":       40,
		"AuditRecordSubModule_CORPUS_PROCESSING":       41,
		"AuditRecordSubModule_CORPUS_LABELING":         42,
		"AuditRecordSubModule_CORPUS_EVALUATING":       43,
		"AuditRecordSubModule_SERVICE_DEPLOYMENT":      50,
		"AuditRecordSubModule_SECURITY_CENTER":         51,
		"AuditRecordSubModule_CODE_EXAMPLES":           52,
		"AuditRecordSubModule_WORKFLOW_MANAGEMENT":     53,
	}
)

func (x AuditRecordSubModule) Enum() *AuditRecordSubModule {
	p := new(AuditRecordSubModule)
	*p = x
	return p
}

func (x AuditRecordSubModule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditRecordSubModule) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_audit_record_proto_enumTypes[2].Descriptor()
}

func (AuditRecordSubModule) Type() protoreflect.EnumType {
	return &file_proto_audit_record_proto_enumTypes[2]
}

func (x AuditRecordSubModule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditRecordSubModule.Descriptor instead.
func (AuditRecordSubModule) EnumDescriptor() ([]byte, []int) {
	return file_proto_audit_record_proto_rawDescGZIP(), []int{2}
}

type AuditRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"事件id"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" description:"事件id"`
	// @gotags: description:"模块"
	Module AuditRecordModule `protobuf:"varint,2,opt,name=module,proto3,enum=proto.AuditRecordModule" json:"module,omitempty" description:"模块"`
	// @gotags: description:"功能菜单"
	SubModule AuditRecordSubModule `protobuf:"varint,3,opt,name=sub_module,json=subModule,proto3,enum=proto.AuditRecordSubModule" json:"sub_module,omitempty" description:"功能菜单"`
	// @gotags: description:"操作类型"
	OpType AuditRecordOperateType `protobuf:"varint,4,opt,name=op_type,json=opType,proto3,enum=proto.AuditRecordOperateType" json:"op_type,omitempty" description:"操作类型"`
	// @gotags: description:"事件描述"
	Event string `protobuf:"bytes,5,opt,name=event,proto3" json:"event,omitempty" description:"事件描述"`
	// @gotags: description:"操作人"
	User string `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty" description:"操作人"`
	// @gotags: description:"操作时间"
	TimeMills int64 `protobuf:"varint,7,opt,name=time_mills,json=timeMills,proto3" json:"time_mills,omitempty" description:"操作时间"`
	// @gotags: description:"事件所匹配的API"
	MatchedApi *AuditRecordAPIConfig `protobuf:"bytes,8,opt,name=matched_api,json=matchedApi,proto3" json:"matched_api,omitempty" description:"事件所匹配的API"`
	// @gotags: description:"实际的请求路径"
	ReqPath string `protobuf:"bytes,9,opt,name=req_path,json=reqPath,proto3" json:"req_path,omitempty" description:"实际的请求路径"`
	// @gotags: description:"详细信息，包含请求参数、响应等"
	Details map[string]string `protobuf:"bytes,10,rep,name=details,proto3" json:"details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"详细信息，包含请求参数、响应等"`
	// @gotags: description:"项目id"
	ProjectId string `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty" description:"项目id"`
}

func (x *AuditRecord) Reset() {
	*x = AuditRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_audit_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditRecord) ProtoMessage() {}

func (x *AuditRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_audit_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditRecord.ProtoReflect.Descriptor instead.
func (*AuditRecord) Descriptor() ([]byte, []int) {
	return file_proto_audit_record_proto_rawDescGZIP(), []int{0}
}

func (x *AuditRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AuditRecord) GetModule() AuditRecordModule {
	if x != nil {
		return x.Module
	}
	return AuditRecordModule_AuditRecordModule_SPACE
}

func (x *AuditRecord) GetSubModule() AuditRecordSubModule {
	if x != nil {
		return x.SubModule
	}
	return AuditRecordSubModule_AuditRecordSubModule_SPACE_INFO_MANAGEMENT
}

func (x *AuditRecord) GetOpType() AuditRecordOperateType {
	if x != nil {
		return x.OpType
	}
	return AuditRecordOperateType_CREATE
}

func (x *AuditRecord) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *AuditRecord) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *AuditRecord) GetTimeMills() int64 {
	if x != nil {
		return x.TimeMills
	}
	return 0
}

func (x *AuditRecord) GetMatchedApi() *AuditRecordAPIConfig {
	if x != nil {
		return x.MatchedApi
	}
	return nil
}

func (x *AuditRecord) GetReqPath() string {
	if x != nil {
		return x.ReqPath
	}
	return ""
}

func (x *AuditRecord) GetDetails() map[string]string {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *AuditRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

// 事件对应的API配置
type AuditRecordAPIConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"模块"
	Module AuditRecordModule `protobuf:"varint,1,opt,name=module,proto3,enum=proto.AuditRecordModule" json:"module,omitempty" description:"模块"`
	// @gotags: description:"功能菜单"
	SubModule AuditRecordSubModule `protobuf:"varint,2,opt,name=sub_module,json=subModule,proto3,enum=proto.AuditRecordSubModule" json:"sub_module,omitempty" description:"功能菜单"`
	// @gotags: description:"操作类型"
	OpType AuditRecordOperateType `protobuf:"varint,3,opt,name=op_type,json=opType,proto3,enum=proto.AuditRecordOperateType" json:"op_type,omitempty" description:"操作类型"`
	// @gotags: description:"请求方法"
	ApiMethod string `protobuf:"bytes,4,opt,name=api_method,json=apiMethod,proto3" json:"api_method,omitempty" description:"请求方法"`
	// @gotags: description:"请求路径的匹配表达式"
	ApiPath string `protobuf:"bytes,5,opt,name=api_path,json=apiPath,proto3" json:"api_path,omitempty" description:"请求路径的匹配表达式"`
	// @gotags: description:"接口说明"
	ApiDesc string `protobuf:"bytes,6,opt,name=api_desc,json=apiDesc,proto3" json:"api_desc,omitempty" description:"接口说明"`
	// @gotags: description:"事件描述的Go Template"
	GoTemplate string `protobuf:"bytes,7,opt,name=go_template,json=goTemplate,proto3" json:"go_template,omitempty" description:"事件描述的Go Template"`
	// @gotags: description:"接口的gateway模块"
	ApiModule string `protobuf:"bytes,8,opt,name=api_module,json=apiModule,proto3" json:"api_module,omitempty" description:"接口的gateway模块"`
	// @gotags: description:"Go Template语法的判断条件，用于筛选符合事件描述的接口请求；需要渲染true或false；置空不筛选"
	Condition string `protobuf:"bytes,9,opt,name=condition,proto3" json:"condition,omitempty" description:"Go Template语法的判断条件，用于筛选符合事件描述的接口请求；需要渲染true或false；置空不筛选"`
}

func (x *AuditRecordAPIConfig) Reset() {
	*x = AuditRecordAPIConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_audit_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditRecordAPIConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditRecordAPIConfig) ProtoMessage() {}

func (x *AuditRecordAPIConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_audit_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditRecordAPIConfig.ProtoReflect.Descriptor instead.
func (*AuditRecordAPIConfig) Descriptor() ([]byte, []int) {
	return file_proto_audit_record_proto_rawDescGZIP(), []int{1}
}

func (x *AuditRecordAPIConfig) GetModule() AuditRecordModule {
	if x != nil {
		return x.Module
	}
	return AuditRecordModule_AuditRecordModule_SPACE
}

func (x *AuditRecordAPIConfig) GetSubModule() AuditRecordSubModule {
	if x != nil {
		return x.SubModule
	}
	return AuditRecordSubModule_AuditRecordSubModule_SPACE_INFO_MANAGEMENT
}

func (x *AuditRecordAPIConfig) GetOpType() AuditRecordOperateType {
	if x != nil {
		return x.OpType
	}
	return AuditRecordOperateType_CREATE
}

func (x *AuditRecordAPIConfig) GetApiMethod() string {
	if x != nil {
		return x.ApiMethod
	}
	return ""
}

func (x *AuditRecordAPIConfig) GetApiPath() string {
	if x != nil {
		return x.ApiPath
	}
	return ""
}

func (x *AuditRecordAPIConfig) GetApiDesc() string {
	if x != nil {
		return x.ApiDesc
	}
	return ""
}

func (x *AuditRecordAPIConfig) GetGoTemplate() string {
	if x != nil {
		return x.GoTemplate
	}
	return ""
}

func (x *AuditRecordAPIConfig) GetApiModule() string {
	if x != nil {
		return x.ApiModule
	}
	return ""
}

func (x *AuditRecordAPIConfig) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

var File_proto_audit_record_proto protoreflect.FileDescriptor

var file_proto_audit_record_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xfb, 0x03, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x30, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x73, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x36, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x73,
	0x12, 0x3c, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41, 0x50, 0x49, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x41, 0x70, 0x69, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x71, 0x50, 0x61, 0x74, 0x68, 0x12, 0x39, 0x0a, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xef, 0x02, 0x0a, 0x14, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41,
	0x50, 0x49, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x75,
	0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x36, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x70, 0x69, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x69, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x69, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x69, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x6f, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x6f, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x69, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2a, 0x3c, 0x0a, 0x16, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x2a,
	0xc3, 0x01, 0x0a, 0x11, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x53, 0x50, 0x41, 0x43, 0x45,
	0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x43, 0x4f, 0x52, 0x50, 0x55, 0x53, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x54,
	0x4f, 0x4f, 0x4c, 0x10, 0x05, 0x2a, 0xdd, 0x07, 0x0a, 0x14, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x2e,
	0x0a, 0x2a, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x30,
	0x0a, 0x2c, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01,
	0x12, 0x29, 0x0a, 0x25, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4d,
	0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x29, 0x0a, 0x25, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49,
	0x45, 0x4e, 0x43, 0x45, 0x10, 0x0b, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x0c, 0x12,
	0x29, 0x0a, 0x25, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75,
	0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x45, 0x56,
	0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x50, 0x54, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45,
	0x45, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x0e, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x41, 0x50, 0x50, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x14,
	0x12, 0x27, 0x0a, 0x23, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x45, 0x58, 0x50,
	0x45, 0x52, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x15, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x50, 0x4c, 0x55, 0x47, 0x49, 0x4e, 0x5f, 0x4d, 0x41, 0x4e,
	0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x16, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f,
	0x52, 0x10, 0x17, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x41, 0x50, 0x50, 0x5f,
	0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x18, 0x12, 0x2d, 0x0a, 0x29,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x4d,
	0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1e, 0x12, 0x2d, 0x0a, 0x29, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x45, 0x58,
	0x50, 0x45, 0x52, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x1f, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x43, 0x4f, 0x52, 0x50, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x28, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x43,
	0x4f, 0x52, 0x50, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47,
	0x10, 0x29, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x43, 0x4f, 0x52, 0x50, 0x55,
	0x53, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x2a, 0x12, 0x2a, 0x0a, 0x26,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x43, 0x4f, 0x52, 0x50, 0x55, 0x53, 0x5f, 0x45, 0x56, 0x41, 0x4c,
	0x55, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x2b, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x32, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x10, 0x33, 0x12,
	0x26, 0x0a, 0x22, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75,
	0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x58, 0x41,
	0x4d, 0x50, 0x4c, 0x45, 0x53, 0x10, 0x34, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x35, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61,
	0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73,
	0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_audit_record_proto_rawDescOnce sync.Once
	file_proto_audit_record_proto_rawDescData = file_proto_audit_record_proto_rawDesc
)

func file_proto_audit_record_proto_rawDescGZIP() []byte {
	file_proto_audit_record_proto_rawDescOnce.Do(func() {
		file_proto_audit_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_audit_record_proto_rawDescData)
	})
	return file_proto_audit_record_proto_rawDescData
}

var file_proto_audit_record_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_audit_record_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_audit_record_proto_goTypes = []interface{}{
	(AuditRecordOperateType)(0),  // 0: proto.AuditRecordOperateType
	(AuditRecordModule)(0),       // 1: proto.AuditRecordModule
	(AuditRecordSubModule)(0),    // 2: proto.AuditRecordSubModule
	(*AuditRecord)(nil),          // 3: proto.AuditRecord
	(*AuditRecordAPIConfig)(nil), // 4: proto.AuditRecordAPIConfig
	nil,                          // 5: proto.AuditRecord.DetailsEntry
}
var file_proto_audit_record_proto_depIdxs = []int32{
	1, // 0: proto.AuditRecord.module:type_name -> proto.AuditRecordModule
	2, // 1: proto.AuditRecord.sub_module:type_name -> proto.AuditRecordSubModule
	0, // 2: proto.AuditRecord.op_type:type_name -> proto.AuditRecordOperateType
	4, // 3: proto.AuditRecord.matched_api:type_name -> proto.AuditRecordAPIConfig
	5, // 4: proto.AuditRecord.details:type_name -> proto.AuditRecord.DetailsEntry
	1, // 5: proto.AuditRecordAPIConfig.module:type_name -> proto.AuditRecordModule
	2, // 6: proto.AuditRecordAPIConfig.sub_module:type_name -> proto.AuditRecordSubModule
	0, // 7: proto.AuditRecordAPIConfig.op_type:type_name -> proto.AuditRecordOperateType
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_proto_audit_record_proto_init() }
func file_proto_audit_record_proto_init() {
	if File_proto_audit_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_audit_record_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_audit_record_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditRecordAPIConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_audit_record_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_audit_record_proto_goTypes,
		DependencyIndexes: file_proto_audit_record_proto_depIdxs,
		EnumInfos:         file_proto_audit_record_proto_enumTypes,
		MessageInfos:      file_proto_audit_record_proto_msgTypes,
	}.Build()
	File_proto_audit_record_proto = out.File
	file_proto_audit_record_proto_rawDesc = nil
	file_proto_audit_record_proto_goTypes = nil
	file_proto_audit_record_proto_depIdxs = nil
}
