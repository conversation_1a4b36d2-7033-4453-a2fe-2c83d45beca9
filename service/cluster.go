package service

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	listv1 "k8s.io/client-go/listers/core/v1"

	knm_v1 "transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"
	"transwarp.io/mlops/expense/client"
	"transwarp.io/mlops/expense/conf"
	"transwarp.io/mlops/expense/k8s"
	"transwarp.io/mlops/expense/models"
	"transwarp.io/mlops/expense/utils"
	"transwarp.io/mlops/mlops-std/stdlog"
)

var defaultClusters = []models.ClusterInfo{{
	Id:        "default",
	Name:      "normal",
	Namespace: "default",
}}

type ClusterService struct {
	prometheusClient *client.PrometheusClient
	nodeLister       listv1.NodeLister
	podLister        listv1.PodLister
}

func NewClusterService(proCli *client.PrometheusClient) (*ClusterService, error) {
	fact, err := k8s.GetInfomersFactoty()
	if err != nil {
		return nil, fmt.Errorf("get informersFactory: %w", err)
	}
	return &ClusterService{
		prometheusClient: proCli,
		nodeLister:       fact.Core().V1().Nodes().Lister(),
		podLister:        fact.Core().V1().Pods().Lister(),
	}, nil
}

func (s ClusterService) GetNodeList(filter *models.NodeFilter) ([]*v1.Node, error) {
	sl := labels.Everything()
	if conf.Conf.Expense.NodeConfig.LabelSelector != "" {
		rq, err := labels.NewRequirement(conf.Conf.Expense.NodeConfig.LabelSelector, selection.Exists, nil)
		if err != nil {
			return nil, fmt.Errorf("new requirement: %w", err)
		}
		sl = labels.NewSelector().Add(*rq)
	}
	list, err := s.nodeLister.List(sl)
	if err != nil {
		return nil, err
	}
	if filter != nil && filter.Arch != "" {
		list = lo.Filter(list, func(n *v1.Node, _ int) bool { return n.Status.NodeInfo.Architecture == filter.Arch })
	}
	return list, nil
}

func (s ClusterService) GetPodList() ([]*v1.Pod, error) {
	list, err := s.podLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s ClusterService) GetPodMaxResource() (*models.PodMaxResource, error) {
	info, err := clusterService.GetClusterResourceInfoForHami("", "", nil)
	if err != nil {
		return nil, err
	}
	maxResource := &models.PodMaxResource{}
	for _, node := range info.Nodes {
		if node.Cpu.Allocatable > maxResource.Cpu {
			maxResource.Cpu = node.Cpu.Allocatable
		}
		if node.Memory.Allocatable > maxResource.Memory {
			maxResource.Memory = node.Memory.Allocatable
		}
		if node.VGpuCore.Allocatable > maxResource.VGpuCore {
			maxResource.VGpuCore = node.VGpuCore.Allocatable
		}
		if node.VGpuMemory.Allocatable > maxResource.VGpuMemory {
			maxResource.VGpuMemory = node.VGpuMemory.Allocatable
		}
	}
	return maxResource, nil
}

func (s ClusterService) CheckTenant(ctx context.Context, tenantId string, r *models.CheckResourceReq) (*models.CheckUsedRes, error) {
	summary, err := reportService.QuerySummary(ctx, tenantId)
	if err != nil {
		return nil, err
	}
	if r.Replicas <= 0 {
		r.Replicas = 1
	}
	if s.GetResourceByExpenseRuleId(r) != nil {
		return nil, err
	}
	return calculateTenant(r, summary)
}

func (s ClusterService) CheckNode(ctx context.Context, r *models.CheckResourceReq) (*models.CheckNodeUsedRes, error) {
	clusterInfo, err := s.GetClusterResourceInfoForHami("", "", nil)
	if err != nil {
		return nil, err
	}
	if r.Replicas <= 0 {
		r.Replicas = 1
	}
	if s.GetResourceByExpenseRuleId(r) != nil {
		return nil, err
	}
	// 如果指定了状态, 则过滤一下 node
	if len(r.NodeStatus) > 0 {
		statusNodes := lo.Filter(clusterInfo.Nodes, func(item models.NodeResourceInfo, _ int) bool {
			return len(r.NodeStatus) == len(item.Status) && lo.Every(r.NodeStatus, item.Status) // node 状态完全符合所有 NodeStatus
		})
		if len(r.Nodes) == 0 {
			clusterInfo.Nodes = statusNodes
		}
	}
	// 如果指定了需要全部满足的 node, 则过滤一下
	if len(r.SpecialNodes) > 0 {
		// 确保每个 SpecialNodes 都在 Nodes 中
		if !lo.Every(
			lo.Map(clusterInfo.Nodes, func(item models.NodeResourceInfo, _ int) string { return item.Name }),
			r.SpecialNodes,
		) {
			return nil, errors.New("special nodes not exist in nodes")
		}
		// 过滤 nodes
		clusterInfo.Nodes = lo.Filter(clusterInfo.Nodes, func(item models.NodeResourceInfo, _ int) bool {
			return lo.Contains(r.SpecialNodes, item.Name)
		})
	}
	// 不过滤, 因为检测不通过后要提示符合要求的 node, 否则需要检测两次
	// if len(r.Nodes) > 0 {
	// 	// 确保每个 r.Nodes 都在 clusterInfo.Nodes 中
	// 	if !lo.Every(
	// 		lo.Map(clusterInfo.Nodes, func(item models.NodeResourceInfo, _ int) string { return item.Name }),
	// 		r.Nodes,
	// 	) {
	// 		return nil, errors.New("special nodes not exists")
	// 	}
	// 	// 过滤 nodes
	// 	clusterInfo.Nodes = lo.Filter(clusterInfo.Nodes, func(item models.NodeResourceInfo, _ int) bool {
	// 		return lo.Contains(r.Nodes, item.Name)
	// 	})
	// }
	return calculateNodes(r, clusterInfo)
}

func (s ClusterService) GetResourceByExpenseRuleId(req *models.CheckResourceReq) error {
	if req.ExpenseRuleId == 0 {
		return nil
	}
	rule, err := expenseRuleService.QueryOne(req.ExpenseRuleId, "")
	if err != nil {
		return err
	}
	req.CpuLimit = fmt.Sprintf("%d", rule.Source.Cpu)
	req.CpuRequest = fmt.Sprintf("%d", rule.Source.Cpu)
	req.MemoryLimit = fmt.Sprintf("%dGi", rule.Source.Memory)
	req.MemoryRequest = fmt.Sprintf("%dGi", rule.Source.Memory)
	req.GpuCore = strconv.Itoa(int(rule.Source.Gpu * 100))
	req.GpuMemory = fmt.Sprintf("%dGi", int(float64(rule.Source.GpuMemory)*rule.Source.Gpu))
	return nil
}

func calculateTenant(raw *models.CheckResourceReq, tenant *models.SourceUsedInfo) (*models.CheckUsedRes, error) {
	res := &models.CheckUsedRes{
		Satisfied: true,
	}

	if raw == nil {
		res.Detail = "raw resource is empty"
		res.Satisfied = false
		return res, nil
	}

	if tenant == nil {
		res.Detail = "tenant id resource is empty"
		res.Satisfied = false
		return res, nil
	}
	var (
		remainCpu = int64(tenant.Cpu.Total - tenant.Cpu.Used)
		remainMem = tenant.Memory.Total - tenant.Memory.Used
	)

	// cpu request
	if CompareCpu(raw.CpuRequest, remainCpu, raw.Replicas) < 0 {
		res.Detail = fmt.Sprintf("raw cpu request : %s is greater than tenant cpu request: %d. replicas: %d",
			raw.CpuRequest, remainCpu, raw.Replicas)
		res.Satisfied = false
	}

	// cpu limit
	// if CompareCpu(raw.CpuLimit, remainCpu, raw.Replicas) < 0 {
	// 	res.Detail = fmt.Sprintf("raw cpu limit : %s is greater than tenant cpu limit: %d. replicas: %d",
	// 		raw.CpuLimit, remainCpu, raw.Replicas)
	// 	res.Satisfied = false
	// }

	// memory request
	if len(raw.MemoryRequest) != 0 {
		if CompareMemory(raw.MemoryRequest, remainMem, raw.Replicas) < 0 {
			res.Detail = fmt.Sprintf("raw memory request: %s is greater than tenant memory request: %.2fGi. replicas: %d",
				raw.MemoryRequest, remainMem, raw.Replicas)
			res.Satisfied = false
		}
	}

	// memory limit
	// if len(raw.MemoryLimit) != 0 {
	// 	if CompareMemory(raw.MemoryLimit, remainMem, raw.Replicas) < 0 {
	// 		res.Detail = fmt.Sprintf("raw memory limit: %s is greater than tenant gpu memory limit: %.2fGi. replicas: %d",
	// 			raw.MemoryLimit, remainMem, raw.Replicas)
	// 		res.Satisfied = false
	// 	}
	// }

	// gpu 算力
	if len(raw.GpuCore) != 0 {
		gpuCore, err := strconv.ParseInt(raw.GpuCore, 10, 64)
		if err != nil {
			stdlog.Error("Error converting string to int64:", err)
			res.Satisfied = false
		}
		// fixme 租户和节点 对于gpu资源，单位不一样
		gpuCore /= 100
		if gpuCore > int64(tenant.Gpu.Total-tenant.Gpu.Used) {
			res.Detail = fmt.Sprintf("raw gpu count: %d is greater than tenant gpu count limit: %d. replicas: %d",
				gpuCore, int64(tenant.Gpu.Total-tenant.Gpu.Used), raw.Replicas)
			res.Satisfied = false
		}
	}

	// gpu memory
	if len(raw.GpuMemory) != 0 {
		if CompareMemory(raw.GpuMemory, tenant.GpuMemory.Total-tenant.GpuMemory.Used, raw.Replicas) < 0 {
			res.Detail = fmt.Sprintf("raw gpu memory: %s is greater than tenant gpu memory : %.2fGi. replicas: %d",
				raw.GpuMemory, tenant.GpuMemory.Total-tenant.GpuMemory.Used, raw.Replicas)
			res.Satisfied = false
		}
	}

	return res, nil
}

func calculateNodes(raw *models.CheckResourceReq, info *models.ClusterResourceInfo) (*models.CheckNodeUsedRes, error) {
	if raw == nil {
		return &models.CheckNodeUsedRes{
			Satisfied: false,
			Detail:    "raw resource is empty",
		}, nil
	}
	var (
		err               error
		gpuCnt, gpuCntTmp int
		detail, nodeErr   string // detail: 最近一次错误信息, nodeErr: 指定节点的错误信息

		distributed = len(raw.SpecialNodes) > 1 || raw.Replicas > 1 // 是否是分布式模式
		nodes       = make([]string, 0)
	)
	if raw.GpuCore != "" {
		gpuCnt, err = cast.ToIntE(raw.GpuCore)
		if err != nil {
			return nil, fmt.Errorf("converting string to int64: %w", err)
		}
		gpuCnt /= 100
		gpuCntTmp = gpuCnt
	}
	for _, node := range info.Nodes {
		// cpu request
		if CompareCpu(raw.CpuRequest, int64(node.Cpu.Available), raw.Replicas) < 0 {
			detail = fmt.Sprintf("raw cpu request : %s is greater than node %s cpu request: %d. replicas: %d",
				raw.CpuRequest, node.Name, int64(node.Cpu.Available), raw.Replicas)
			if lo.Contains(raw.Nodes, node.Name) {
				nodeErr = detail
			}
			continue
		}

		// cpu limit
		// if CompareCpu(raw.CpuLimit, int64(node.Cpu.Available), raw.Replicas) < 0 {
		// 	detail = fmt.Sprintf("raw cpu limit : %s is greater than node %s cpu limit: %d. replicas: %d",
		// 		raw.CpuLimit, node.Name, int64(node.Cpu.Available), raw.Replicas)
		// 	if lo.Contains(raw.Nodes, node.Name) {
		// 		nodeErr = detail
		// 	}
		// 	continue
		// }
		// memory request
		if len(raw.MemoryRequest) != 0 {
			if CompareMemory(raw.MemoryRequest, node.Memory.Available, raw.Replicas) < 0 {
				detail = fmt.Sprintf("raw memory request : %s is greater than node %s memory request: %.2fGi. replicas: %d",
					raw.MemoryRequest, node.Name, node.Memory.Available, raw.Replicas)
				if lo.Contains(raw.Nodes, node.Name) {
					nodeErr = detail
				}
				continue
			}
		}
		// memory limit
		// if len(raw.MemoryLimit) != 0 {
		// 	if CompareMemory(raw.MemoryLimit, node.Memory.Available, raw.Replicas) < 0 {
		// 		detail = fmt.Sprintf("raw memory limit : %s is greater than node %s memory limit: %.2fGi. replicas: %d",
		// 			raw.MemoryLimit, node.Name, node.Memory.Available, raw.Replicas)
		// 		continue
		// 	}
		// }
		if gpuCnt > 0 { // gpu 个数
			if distributed { // 区分分布式模式和单机模式
				// 分布式使用所有节点的总 gpu 数量
				if lo.SumBy(info.Nodes, func(n models.NodeResourceInfo) int { return int(n.GpuCard.Allocatable) }) < gpuCnt {
					detail = fmt.Sprintf("gpu count is less than %d", gpuCnt)
					if lo.Contains(raw.Nodes, node.Name) {
						nodeErr = detail
					}
					continue
				}
			} else { // 单机模式
				if int(node.GpuCard.Allocatable) < gpuCnt {
					detail = fmt.Sprintf("node: %s, gpu count is less than %d", node.Name, gpuCnt)
					if lo.Contains(raw.Nodes, node.Name) {
						nodeErr = detail
					}
					continue
				}
			}
		}
		// 是否独占
		if raw.GpuExclusive {
			if !distributed {
				// 单机模式
				nodegpuNeed := gpuCntTmp // 单机模式下, 一个节点要满足所有 gpu 数量
				for _, gpu := range node.Gpus {
					if nodegpuNeed == 0 {
						break
					}
					if gpu.AllocatedVCore > 0 {
						detail = fmt.Sprintf("node: %s, gpu is using by others, gpu core used: %d",
							node.Name, int64(node.VGpuCore.Used))
						continue
					}
					if gpu.AllocatedMemory > 0 {
						detail = fmt.Sprintf("node: %s, gpu is using by others, gpu memory used: %d",
							node.Name, int64(node.VGpuMemory.Used))
						continue
					}
					nodegpuNeed--
				}
				if nodegpuNeed > 0 {
					continue
				} else {
					gpuCnt--
				}
			} else {
				// 分布式
				for _, gpu := range node.Gpus {
					if gpuCnt == 0 {
						break
					}
					if gpu.AllocatedVCore > 0 {
						detail = fmt.Sprintf("node: %s, gpu is using by others, gpu core used: %d",
							node.Name, int64(node.VGpuCore.Used))
						continue
					}
					if gpu.AllocatedMemory > 0 {
						detail = fmt.Sprintf("node: %s, gpu is using by others, gpu memory used: %d",
							node.Name, int64(node.VGpuMemory.Used))
						continue
					}
					gpuCnt--
				}
			}
		} else {
			// 非独占
			// gpu 算力
			if len(raw.GpuCore) != 0 {
				gpuCore, err := strconv.ParseInt(raw.GpuCore, 10, 64)
				if err != nil {
					fmt.Println("Error converting string to int64:", err)
					if lo.Contains(raw.Nodes, node.Name) {
						nodeErr = detail
					}
					continue
				}
				vcore := 0
				for _, gpu := range node.Gpus {
					// npu core 转为百分形式
					if strings.EqualFold(gpu.Vendor, models.GPUVendorAscend) {
						if gpu.TotalVCore == 0 {
							continue
						}
						// eg: 100 - (10 * 100 / 20)
						vcore += 100 - ((gpu.AllocatedVCore * 100) / gpu.TotalVCore)
					} else {
						vcore += gpu.TotalVCore - gpu.AllocatedVCore
					}
				}
				if gpuCore > int64(vcore) {
					detail = fmt.Sprintf("raw gpu core : %s is greater than node %s gpu core: %d. replicas: %d",
						raw.GpuCore, node.Name, int64(node.VGpuCore.Available), raw.Replicas)
					if lo.Contains(raw.Nodes, node.Name) {
						nodeErr = detail
					}
					continue
				}
			}
			// gpu memory
			if len(raw.GpuMemory) != 0 {
				if CompareMemory(raw.GpuMemory, node.VGpuMemory.Available, raw.Replicas) < 0 {
					detail = fmt.Sprintf("raw gpu memory : %s is greater than node %s gpu memory: %.2fGi. replicas: %d",
						raw.GpuMemory, node.Name, node.VGpuMemory.Available, raw.Replicas)
					if lo.Contains(raw.Nodes, node.Name) {
						nodeErr = detail
					}
					continue
				}
			}
		}
		// 有一个节点通过
		nodes = append(nodes, node.Name)
	}
	if raw.GpuExclusive && gpuCnt > 0 {
		return &models.CheckNodeUsedRes{
			Detail:    detail,
			Nodes:     nil,
			Satisfied: false,
		}, nil
	}

	var (
		satisfied bool
		d         string
	)
	if len(raw.SpecialNodes) == 0 {
		d, satisfied = checkRemainNode(raw.Nodes, nodes)
	} else { // 语料分支
		satisfied = lo.Every(nodes, raw.SpecialNodes) // 满足所有 SpecialNodes 中的 node
		d = "some node is not available"
	}
	if satisfied {
		detail = ""
	} else {
		if len(raw.Nodes) > 0 {
			detail = fmt.Sprintf("%s: %s", d, nodeErr)
		} else {
			detail = fmt.Sprintf("%s: %s", d, detail)
		}
	}

	return &models.CheckNodeUsedRes{
		Satisfied: satisfied,
		Detail:    detail,
		Nodes:     nodes,
	}, nil
}

// checkRemainNode 任意一个节点通过，则 true
func checkRemainNode(rawNodes, realNodes []string) (string, bool) {
	if len(realNodes) == 0 {
		return "no node available", false
	}
	if len(rawNodes) == 0 {
		return "", true
	}

	for _, realNode := range realNodes {
		for _, rawNode := range rawNodes {
			if strings.EqualFold(realNode, rawNode) {
				return "", true
			}
		}
	}
	return "no node pass check", false
}

func CompareMemory(str string, remain float64, replicas int64) int {
	remainQ, err := resource.ParseQuantity(fmt.Sprintf("%.2fGi", remain))
	if err != nil {
		return 0
	}
	strQuantity, err := resource.ParseQuantity(str)
	strQuantity.Set(strQuantity.Value() * replicas)
	if err != nil {
		fmt.Println("Error parsing quantity:", err)
		return -1
	}
	return remainQ.Cmp(strQuantity)
}

// CompareCpu remain - str
//
//	@param str 带单位的 quantity
//	@param remain 单位 核
func CompareCpu(str string, remain, replicas int64) int {
	remainQ := resource.NewQuantity(remain, resource.DecimalSI)
	strQuantity, err := resource.ParseQuantity(str)
	if err != nil {
		stdlog.Error("Error parsing quantity:", err)
		return -1
	}
	// 计算 strQuantity * replicas
	strQuantity.Set(strQuantity.Value() * replicas)
	return remainQ.Cmp(strQuantity)
}

// GetClusterResourceInfoForHami
//
//	@param nodeArch 硬件架构 为空则不限制架构 amd64/arm64
//	@param gpuType 算力平台, NVIDIA/Ascend, 为空不限制
func (s ClusterService) GetClusterResourceInfoForHami(nodeArch string, gpuType models.GPUVendor, groupIds []string) (*models.ClusterResourceInfo, error) {
	nodes, err := s.GetNodeList(&models.NodeFilter{
		Arch: nodeArch,
	})
	if err != nil {
		return nil, err
	}
	hamiGPUs, err := GPUMetricsSvc.GetGPUWithAllocatedInfo()
	if err != nil {
		return nil, err
	}
	var hamiGPUGroupByNode map[string][]models.NodeGpu
	hamiGPUGroupByNode = lo.GroupBy(hamiGPUs, func(e models.NodeGpu) string { return e.Node })
	if len(groupIds) > 0 {
		rq, err := labels.NewRequirement("name", selection.In, groupIds)
		if err != nil {
			return nil, fmt.Errorf("new requirement: %w", err)
		}
		ls := labels.NewSelector().Add(*rq)
		groups, err := ResourceGroupService.groupLister.List(ls)
		if err != nil {
			return nil, fmt.Errorf("list groups: %w", err)
		}
		groupNodes := lo.FlatMap(groups, func(e *knm_v1.NodeXpuGroup, _ int) []string {
			return lo.Map(e.Spec.Nodes, func(e knm_v1.NodeXpuItem, _ int) string {
				return e.NodeName
			})
		})
		groupGpus := lo.FlatMap(groups, func(e *knm_v1.NodeXpuGroup, _ int) []string {
			return lo.FlatMap(e.Spec.Nodes, func(e knm_v1.NodeXpuItem, _ int) []string {
				return e.XpuIds
			})
		})
		nodes = lo.Filter(nodes, func(e *v1.Node, _ int) bool {
			return slices.Contains(groupNodes, e.Name)
		})
		// gpu 的 uuid 与 nvidia-smi -L 查出来的设备 id 保持一致，例如 GPU-72dab346-42bb-b1a3-2c72-08b2f3734632
		// npu 的 id 格式为 <型号>-<card ID> cardID 对应 npu-smi 查出来的卡 id，例如：Ascend910B-2
		for k := range hamiGPUGroupByNode {
			hamiGPUGroupByNode[k] = lo.Filter(hamiGPUGroupByNode[k], func(e models.NodeGpu, _ int) bool {
				return slices.Contains(groupGpus, e.GPUUUID)
			})
		}
	}
	pods, err := s.GetPodList()
	if err != nil {
		return nil, err
	}
	// 按 node 分组
	nodePodsMap := lo.GroupBy(pods, func(p *v1.Pod) string {
		return p.Spec.NodeName
	})

	cpuUsedData, err := s.prometheusClient.Query("sum by (node) ((kube_pod_container_resource_requests_cpu_cores{job=\"kube-state-metrics\"} * on (pod, namespace) group_left () kube_pod_info{created_by_kind!~\"Job\",job=\"kube-state-metrics\"}) * on (pod, namespace) group_left () (kube_pod_status_phase{job=\"kube-state-metrics\",phase=\"Running\"} != 0))")
	if err != nil {
		return nil, err
	}

	memoryUsedData, err := s.prometheusClient.Query("sum((kube_pod_container_resource_requests_memory_bytes{job=\"kube-state-metrics\"} * on (pod, namespace)group_left()kube_pod_info{created_by_kind!~\"Job\",job=\"kube-state-metrics\"})* on (pod, namespace)group_left()(kube_pod_status_phase{phase=\"Running\",job=\"kube-state-metrics\"}!=0))by(node) /1024/1024/1024")
	if err != nil {
		return nil, err
	}

	nodeInfos := make([]models.NodeResourceInfo, 0, len(nodes))

	for _, node := range nodes {

		cpuUsed := float64(0)
		for _, item := range cpuUsedData {
			if item.Metric.Node == node.Name {
				used, _ := utils.ParseStr2Float(item.Value[1].(string), 2)
				cpuUsed = used
			}
		}
		memoryUsed := float64(0)
		for _, item := range memoryUsedData {
			if item.Metric.Node == node.Name {
				used, _ := utils.ParseStr2Float(item.Value[1].(string), 2)
				memoryUsed = used
			}
		}
		allocatable := node.Status.Allocatable
		allocatableCpu := allocatable[k8s.Cpu]
		allocatableMemory := allocatable[k8s.Memory]

		var (
			totalCore, totalAllocatedCore, totalAllocatableCard int64
			totalGPUMemory, totalAllocatedMemory                float64
			isVendor                                            bool // 是否满足 gpuType 的筛选条件
		)
		gpus, ok := hamiGPUGroupByNode[node.Name]
		if !ok {
			stdlog.Errorf("node :%v not hami model", node.Name)
		} else {
			totalAllocatableCard = int64(len(gpus))
			for _, gpu := range gpus {
				totalCore += int64(gpu.TotalVCore)
				totalGPUMemory += gpu.TotalMemoryGIB
				totalAllocatedCore += int64(gpu.AllocatedVCore)
				totalAllocatedMemory += gpu.AllocatedMemoryGIB
				isVendor = isVendor || gpu.Vendor == gpuType // 有一个GPU满足即可
			}
			if gpuType != "" && !isVendor {
				stdlog.WithFields("node", node.Name).WithField("gpus", gpus).Info("不满足算力平台筛选条件")
				continue
			}
		}
		info := models.NodeResourceInfo{
			Name: node.Name,
			Cpu: models.ResourceInfo{
				Allocatable: utils.RoundTo(float64(allocatableCpu.Value()), 3),
				Used:        cpuUsed,
				Unit:        "核",
			},
			Memory: models.ResourceInfo{
				Allocatable: utils.RoundTo(float64(allocatableMemory.Value())/GiB, 2),
				Used:        memoryUsed,
				Unit:        "GiB",
			},
			GpuCard: models.ResourceInfo{
				Allocatable: float64(totalAllocatableCard),
			},
			VGpuCore: models.ResourceInfo{
				Allocatable: float64(totalCore),
				Available:   float64(totalCore - totalAllocatedCore),
				Used:        float64(totalAllocatedCore),
			},
			VGpuMemory: models.ResourceInfo{
				Allocatable: totalGPUMemory,
				Available:   utils.RoundTo(totalGPUMemory-totalAllocatedMemory, 2),
				Used:        utils.RoundTo(totalAllocatedMemory, 2),
			},
			Gpus:           gpus,
			Labels:         node.Labels,
			CreateTimeUnix: node.CreationTimestamp.Unix(),
			Pod: models.ResourceInfo{
				Allocatable: utils.RoundTo(float64(node.Status.Allocatable.Pods().Value()), 2),
				Used:        float64(len(nodePodsMap[node.Name])),
				Unit:        "个",
			},
		}
		info.NodeSystemInfo.Fromk8s(node.Status.NodeInfo)
		info.Pod.Available = utils.RoundTo(info.Pod.Allocatable-info.Pod.Used, 2)
		info.Cpu.Available = utils.RoundTo(info.Cpu.Allocatable-info.Cpu.Used, 2)
		info.Memory.Available = utils.RoundTo(info.Memory.Allocatable-info.Memory.Used, 2)

		for _, c := range node.Status.Conditions {
			if c.Type == v1.NodeReady {
				switch c.Status {
				case v1.ConditionTrue:
					info.Status = append(info.Status, models.Ready)
				case v1.ConditionFalse:
					info.Status = append(info.Status, models.NotReady)
				case v1.ConditionUnknown:
					info.Status = append(info.Status, models.Unknown)
				default:
					stdlog.Warnf("未知的 node 状态: %s: %s", node.Name, c.Status)
					info.Status = append(info.Status, models.Unknown)
				}
				break
			}
		}
		if node.Spec.Unschedulable {
			info.Status = append(info.Status, models.SchedulingDisabled)
		}
		info.Arch = node.Status.NodeInfo.Architecture

		nodeInfos = append(nodeInfos, info)
	}
	return &models.ClusterResourceInfo{
		Nodes:    nodeInfos,
		Clusters: defaultClusters,
	}, nil
}
