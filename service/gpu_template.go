package service

import (
	"transwarp.io/aip/llmops-common/pkg/expense"
	"transwarp.io/mlops/expense/models"
)

type AscendNPU struct {
}

// GPUTemplate 获取集群中存在的ascend模板
func (a AscendNPU) GPUTemplate() (*expense.AscendConfig, error) {
	nodes, err := clusterService.GetNodeList(&models.NodeFilter{})
	if err != nil {
		return nil, err
	}
	nodeInfos, err := models.BatchK8sNodeToNodeInfo(nodes)
	if err != nil {
		return nil, err
	}
	ascends := make(map[string]struct{})
	for _, n := range nodeInfos {
		ts := n.GetAscendTypes()
		for _, t := range ts {
			ascends[t] = struct{}{}
		}
	}
	npuTemplates := models.AscendNPUCfg.VNPUs
	existTemplates := make([]*expense.VNPUConfig, 0)
	for _, n := range npuTemplates {
		if _, ok := ascends[n.<PERSON>ord]; ok {
			existTemplates = append(existTemplates, n)
		}
	}
	return &expense.AscendConfig{VNPUs: existTemplates}, nil
}
