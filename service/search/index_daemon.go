package search

import (
	"context"
	"fmt"
	"os"

	"github.com/robfig/cron/v3"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/utils/asset/client"
)

type IndexDaemon struct {
	cron *cron.Cron
}

func NewIndexDaemon() *IndexDaemon {
	secondParser := cron.NewParser(cron.Second | cron.Minute |
		cron.Hour | cron.Dom | cron.Month | cron.DowOptional | cron.Descriptor)
	c := cron.New(cron.WithParser(secondParser), cron.WithChain())

	return &IndexDaemon{
		cron: c,
	}
}

func (s *IndexDaemon) Start() {
	s.cron.Start()
}

func (s *IndexDaemon) Stop() context.Context {
	return s.cron.Stop()
}

func (s *IndexDaemon) AddSyncTask(spec string, cmd func()) error {
	entryId, err := s.cron.AddFunc(spec, cmd)
	stdlog.Infof("sync task entry_id: %d", entryId)
	if err != nil {
		return err
	}
	return nil
}

func AppletDataPollerFunc(index Index) func() {
	return func() {
		fmt.Println("update applet index")
		url := getUrl(conf.C.Client.MlopsSuffix)
		err := fetchDataAndUpdateIndex(url, index)
		if err != nil {
			stdlog.Errorf("upate applet index failed: %+v", err)
		}
	}
}

func ModelDataPollerFunc(index Index) func() {
	return func() {
		fmt.Println("update model index")
		url := getUrl(conf.C.Client.MwhSuffix)
		err := fetchDataAndUpdateIndex(url, index)
		if err != nil {
			stdlog.Errorf("upate model index failed: %+v", err)
		}
	}
}
func CorupsDataPollerFunc(index Index) func() {
	return func() {
		fmt.Println("update corups index")
		url := getUrl(conf.C.Client.SampleSuffix)
		err := fetchDataAndUpdateIndex(url, index)
		if err != nil {
			stdlog.Errorf("upate corups index failed: %+v", err)
		}
	}
}
func KnowlegeBaseDataPollerFunc(index Index) func() {
	return func() {
		fmt.Println("update knowledge base index")
		url := getUrl(conf.C.Client.KnowledgeBaseSuffix)
		err := fetchDataAndUpdateIndex(url, index)
		if err != nil {
			stdlog.Errorf("upate knowledge base index failed: %+v", err)
		}
	}
}

func fetchDataAndUpdateIndex(url string, index Index) error {
	resp, err := client.GetModuleData(url, &pb.AssetReq{
		ProjectId: "",
	}, nil)
	if err != nil {
		stdlog.Errorf("fetch %s data failed: %+v", url, err)
		return err
	}

	for _, item := range resp.Items {
		err := index.AddOrUpdateDocument(item)
		if err != nil {
			return err
		}
	}

	return nil
}

func getUrl(suffix string) string {
	return conf.C.Client.Prefix + string(os.PathSeparator) + suffix
}
