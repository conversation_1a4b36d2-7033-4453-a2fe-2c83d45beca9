package sample

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/cvat-backend/core/sample"
	"transwarp.io/applied-ai/aiot/cvat-backend/core/samplemgr"
	"transwarp.io/applied-ai/aiot/cvat-backend/dao/cache"
)

func NewAPI(root string) *restful.WebService {
	return (&Resource{
		iom: sample.GetImageObjManager(),
		ism: sample.GetImageSetManager(),
		lm:  sample.GetLocationManager(),
		ic:  cache.GetImageCache(),
		im:  samplemgr.GetImageManager(),
	}).WebService(root)
}

type Resource struct {
	iom sample.ImageObjManager
	ism sample.ImageSetManager
	lm  sample.LocationManager
	ic  cache.ImageCache
	im  samplemgr.ImageManager
}

func (r *Resource) WebService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	tags := []string{"sample"}
	// API route definations

	ws.Route(ws.GET("/location").To(r.GetFileList).
		Doc("获取所有的目录列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("lang", "语言")))

	ws.Route(ws.PUT("/location/{id}/upload").To(r.Upload).
		Doc("将上传的图像集zip导入").Metadata(restfulspec.KeyOpenAPITags, tags).
		AllowedMethodsWithoutContentType([]string{"PUT"}).
		Param(ws.PathParameter("id", "")).
		Param(ws.QueryParameter("file", "")))

	ws.Route(ws.DELETE("/location/{id}/set").To(r.DeleteImageSet).
		Doc("删除图片集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("ids", "图片集id，逗号分隔")))

	ws.Route(ws.GET("/location/{id}/set").To(r.GetImageSetList).
		Doc("获取指定空间下所有的图片集列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("id", "目录id，最高级为0")).
		Param(ws.QueryParameter("from", "起始位置")).
		Param(ws.QueryParameter("size", "每页大小")).
		Param(ws.QueryParameter("orderBy", "排序字段")).
		Param(ws.QueryParameter("asc", "是否正序")).
		Param(ws.QueryParameter("name", "数据集名称，支持模糊查询")))

	ws.Route(ws.GET("/location/{id}/set/{setId}/obj").To(r.GetImageObjList).
		Doc("获取指定图片集下的图片列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("id", "目录id，最高级为0")).
		Param(ws.PathParameter("setId", "图片集id")).
		Param(ws.QueryParameter("from", "起始位置")).
		Param(ws.QueryParameter("size", "每页大小")).
		Param(ws.QueryParameter("orderBy", "排序字段")).
		Param(ws.QueryParameter("asc", "是否正序")).
		Param(ws.QueryParameter("name", "数据集名称，支持模糊查询")))

	ws.Route(ws.GET("/location/{id}/set/check").To(r.CheckSetName).
		Doc("检查特定的图片集是否存在").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("id", "目录id，最高级为0")).
		Param(ws.QueryParameter("name", "文件名，去掉文件类型后缀")))

	ws.Route(ws.POST("/location/{id}/set/{setId}").To(r.UpdateImageSet).
		Doc("更新图片集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("id", "目录id，最高级为0")).
		Param(ws.PathParameter("setId", "图片集id")))

	ws.Route(ws.PUT("/location/{id}/set/{setId}").To(r.AddPictures).
		Doc("向指定图片集上传新图片").Metadata(restfulspec.KeyOpenAPITags, tags).
		AllowedMethodsWithoutContentType([]string{"PUT"}).
		Param(ws.PathParameter("id", "目录id，最高级为0")).
		Param(ws.PathParameter("setId", "图片集id")).
		Param(ws.QueryParameter("files", "上传文件的路径，逗号分隔")))

	ws.Route(ws.GET("/location/{id}/set/{setId}/annotation").To(r.SearchRelateAnnotationSet).
		Doc("获取图片集相关的标注集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("setId", "图片集id")))

	ws.Route(ws.GET("/location/{id}/set/{setId}/ids").To(r.ListImageIds).
		Doc("图片集里的全部图片id").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("setId", "图片集id")).
		Param(ws.QueryParameter("orderBy", "排序字段")).
		Param(ws.QueryParameter("asc", "是否正序")))

	ws.Route(ws.DELETE("/location/{id}/set/{setId}/obj").To(r.DeleteImageObj).
		Doc("删除图片").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.QueryParameter("ids", "图片id，逗号分隔")).
		Param(ws.PathParameter("setId", "图片集id")))

	ws.Route(ws.POST("/location/{id}/set/{setId}/obj/{objId}").To(r.UpdateImageObj).
		Doc("更新图片信息").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("setId", "图片集id")).
		Param(ws.PathParameter("objId", "图片id")).
		Param(ws.BodyParameter("json", "json")))

	ws.Route(ws.GET("/location/{id}/set/{setId}/obj/{objId}/original").To(r.GetImageObjOriginal).
		Doc("获取图片的原始数据（即图片数据, 经过base64编码）和名称").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("objId", "图片id")).
		Param(ws.PathParameter("setId", "图片集id")))

	ws.Route(ws.GET("/location/{id}/set/{setId}/zip").To(r.ExportImageSet).
		Doc("导出图片集为zip").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(ws.PathParameter("setId", "图片集id")).
		Param(ws.QueryParameter("name", "保存名称")))

	return ws
}
