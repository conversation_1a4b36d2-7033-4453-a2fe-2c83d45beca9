package serving

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	llmcommon "transwarp.io/aip/llmops-common/pb/common"
	llmpb "transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/mlops/serving/model"
)

func NewAuditV2Api(root string) *restful.WebService {
	return (&AuditV2Resource{}).WebService(root, "audit")
}

type AuditV2Resource struct {
}

func (s *AuditV2Resource) WebService(root string, apiTag string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	apiTags := []string{apiTag}

	ws.Route(ws.GET("/dashboard/overview").
		To(s.GetMLOpsSvcCntOverviewCurrent).
		Doc("[获取仪表盘-服务数量明细-当前上下线的服务数量，新上下线的服务数量](dashboard overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Returns(200, "OK", llmpb.DashboardOverview{}))

	ws.Route(ws.GET("/dashboard/curve").
		To(s.GetMLOpsSvcCntCurve).
		Doc("[获取服务数量趋势，包括服务总量、上线、下线数量，按时间过滤](dashboard curve)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step").DataType("integer")).
		Returns(200, "OK", llmpb.DashboardCurveChart{}))

	ws.Route(ws.GET("/dashboard/{type}/usage").
		To(s.GetResourceUsage).
		Doc("[获取审计信息的资源使用情况(cpu使用率，内存使用率)](dashboard resource usage curve)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("type", "resource type").Description("[支持cpu使用率，内存使用率]support type: cpu、memory")).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step").DataType("integer")).
		Param(ws.QueryParameter("serviceId", "[服务id]service id").Required(false)).
		Param(ws.QueryParameter("serviceVersion", "[服务版本号]service version").Required(false)).
		Returns(200, "OK", llmpb.DashboardResourceUsageCurveChart{}))

	ws.Route(ws.GET("/gpu/{type}/usage").
		To(s.GetGPUResourceUsage).
		Doc("[查找资源的使用情况，支持按照 内存和利用率查找 ](dashboard resource usage curve)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("type", "[]resource type").Description("[支持按照 内存和利用率查找]support type: power、memory")).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step").DataType("integer")).
		Param(ws.QueryParameter("serviceId", "[服务id]service id").Required(false)).
		Param(ws.QueryParameter("serviceVersion", "[服务版本]service version").Required(false)).
		Param(ws.QueryParameter("clusterId", "[集群id]cluster Id").Required(false)).
		Param(ws.QueryParameter("gpuId", "[gpu id名]gpu Id").Required(false)).
		Param(ws.QueryParameter("node", "[集群的节点名]node name").Required(false)).
		Returns(200, "OK", llmpb.GpuCurveChart{}))

	ws.Route(ws.GET("/dashboard/request/overview").
		To(s.GetMLOpsSvcReqOverviewCurrent).
		Doc("[获取仪表盘的请求信息包含今日访问量，今日平均响应时间等(Pv,NewPV,Rtt等)](dashboard request overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("serviceId", "[服务id]service id").Required(false)).
		Param(ws.QueryParameter("serviceVersion", "[服务版本号]service version").Required(false)).
		Returns(200, "OK", llmpb.DashboardRequestOverview{}))

	ws.Route(ws.GET("/dashboard/request/daily").
		To(s.GetMLOpsSvcReqCntDaily).
		Doc("[统计使用度，按当天过滤](dashboard request daily overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step").DataType("integer")).
		Param(ws.QueryParameter("serviceId", "[服务id]service id").Required(false)).
		Param(ws.QueryParameter("serviceVersion", "[服务版本号]service version").Required(false)).
		Returns(200, "OK", llmpb.DashboardVisitHotGraph{}))

	ws.Route(ws.GET("/dashboard/request/bar").
		To(s.GetMLOpsSvcReqBar).
		Doc("[获取仪表盘的访问量&响应时间趋势图，按时间过滤，条形图方式展示](dashboard request bar overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step").DataType("integer")).
		Param(ws.QueryParameter("serviceId", "[服务id]service id").Required(false)).
		Param(ws.QueryParameter("serviceVersion", "[服务版本号]service version").Required(false)).
		Returns(200, "OK", llmpb.DashboardRequestBarChart{}))

	ws.Route(ws.GET("/dashboard/first_token_time/bar").
		To(s.GetFirstTokenTimeChart).
		Doc("[获取首字时延指标监控指标趋势图](dashboard request bar overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("last", "查看最近一段时间的监控数据，单位支持小时(h)和天(d),例1d、12h").DataType("integer")).
		Param(ws.QueryParameter("start", "[开始时间]startTime，用于自定义时间区间戳，单位是秒").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime，用于自定义时间区间戳，单位是秒").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step，用于自定义时间区间").DataType("integer")).
		Param(ws.QueryParameter("service_id", "[服务id]service id").Required(false)).
		Returns(200, "OK", llmcommon.DashboardChart{}))

	ws.Route(ws.GET("/dashboard/request/rank").
		To(s.GetMLOpsSvcReqRank).
		Doc("[获取仪表盘的访问量排名](dashboard request rank overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Returns(200, "OK", llmpb.DashboardVisitRank{}))

	ws.Route(ws.GET("/dashboard/request/records").
		To(s.GetMLOpsSvcReqRecord).
		Doc("[获取仪表盘的调用记录](dashboard request records detail)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("page", "[第几页]page").DataType("integer")).
		Param(ws.QueryParameter("pageSize", "[页大小]page size").DataType("integer")).
		Param(ws.QueryParameter("isDesc", "[是否为desc 描述信息]is desc").DataType("boolean")).
		Param(ws.QueryParameter("orderBy", "[排序方式]order by")).
		Param(ws.QueryParameter("serviceName", "[服务名字]service name").Required(false)).
		Param(ws.QueryParameter("start", "[开始时间]startTime").Required(false).DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").Required(false).DataType("integer")).
		Param(ws.QueryParameter("ip", "ip").Required(false)).
		Param(ws.QueryParameter("status", "[状态码(成功-200，失败-500)]status").Required(false).DataType("integer")).
		Param(ws.QueryParameter("serviceId", "[服务id]service id").Required(false)).
		Param(ws.QueryParameter("serviceVersion", "[服务版本]service version").Required(false)).
		Returns(200, "OK", llmpb.ServiceVisitRecordPage{}))

	ws.Route(ws.GET("/dashboard/request/record/detail/{id}").
		To(s.GetMLOpsSvcReqRecordDetail).
		Doc("[获取仪表盘的某条调用记录详情](dashboard request records detail)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("x_request_id", "对应记录的id")).
		Returns(200, "OK", llmpb.Detail{}))

	ws.Route(ws.GET("/dashboard/request/records/stats").
		To(s.StatsMLOpsSvcReqRecord).
		Doc("[统计最近一段时间内各服务的访问情况](dashboard request records stats)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter(QueryParamDays, "[最近多少天]day").Required(true).DataType("integer")).
		Returns(200, "OK", llmpb.StatsVisitRecordRsp{}))

	ws.Route(ws.GET("/dashboard/request/count").
		To(s.GetMLOpsSvcReqRecordCount).
		Doc("[获取仪表盘的调用记录总量等](dashboard request records overview)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Returns(200, "OK", llmpb.ServiceRecordCount{}))

	ws.Route(ws.POST("/dashboard/request/records/download").
		To(s.ExportMLOpsSvcReqRecord).
		Doc("[下载仪表盘的访问记录信息-导出记录](download service visit record)").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads(llmpb.DownloadServiceVisitRecordReq{}).
		Returns(200, "OK", llmpb.DownloadServiceVisitRecordRes{}))

	ws.Route(ws.GET("/dashboard/token/bar").
		To(s.GetMLOpsTokenBar).
		Doc("获取模型服务TOKEN趋势图，按时间过滤，条形图方式展示").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("step", "[梯级，假设 start = 0, end = 10, step = 2, 则一共分成 (10 - 0) / 2 = 5 份]step").DataType("integer")).
		Param(ws.QueryParameter("serviceId", "[服务id]service id")).
		Param(ws.QueryParameter("type", "统计token类型 可选值 0:所有 1.输入 2.输出")).
		Returns(200, "OK", llmpb.DashboardTokenBarChart{}))

	ws.Route(ws.GET("/count/token").
		To(s.CountMLOpsToken).
		Doc("统计模型服务TOKEN").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("service_id", "[服务id]service id").Required(false)).
		Returns(200, "OK", llmpb.CountTokenRsp{}))

	ws.Route(ws.POST("/evaluate/answer").
		To(s.EvaluateAnswer).
		Doc("点赞or点踩").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads(llmpb.EvaluateAnswerReq{}).
		Returns(200, "OK", llmpb.EvaluateAnswerReq{}))
	ws.Route(ws.GET("/applet-chains/daily-user").
		To(s.GetChainDailyUser).
		Doc("按天聚合用户统计数据").
		Param(ws.QueryParameter("start", "[开始时间]startTime").DataType("integer")).
		Param(ws.QueryParameter("end", "[结束时间]endTime").DataType("integer")).
		Param(ws.QueryParameter("chain-id", "[应用链id]chain id")).
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Returns(200, "OK", model.ChainUserAudit{}))
	return ws
}
