package serving

import (
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"github.com/jinzhu/copier"
	"strconv"
	"strings"
	"transwarp.io/mlops/serving/conf"

	llmcommon "transwarp.io/aip/llmops-common/pb/common"

	"github.com/emicklei/go-restful/v3"
	llmpb "transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/serving/core"
	servingmodel "transwarp.io/mlops/serving/model"
	"transwarp.io/mlops/serving/rpc/api"
	servingutil "transwarp.io/mlops/serving/util"
)

func (s *ServiceV2Resource) Create(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	ctx = servingutil.CtxWithFromHttpHeader(ctx)
	param := new(llmpb.MLOpsServiceBaseInfo)
	if err := request.ReadEntity(param); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	res, err := api.MLOpsServiceRPCSvc.CreateService(ctx, param)
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) CreateRemote(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	param := new(llmpb.MLOpsRemoteServiceInfoReq)
	if err := request.ReadEntity(param); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	res, err := api.MLOpsServiceRPCSvc.CreateRemote(ctx, param)
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) CreateServiceVersion(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no serviceID"))
		return
	}
	svcVersionDTO := new(llmpb.MLOpsServiceVersionInfo)
	if err := request.ReadEntity(svcVersionDTO); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	res, err := api.MLOpsServiceRPCSvc.CreateServiceVersion(ctx, &llmpb.ServiceIDVersionInfo{
		ServiceId:          svcID,
		ServiceVersionInfo: svcVersionDTO,
	})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) UpdateServiceBaseInfo(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no serviceID"))
		return
	}
	svc := new(llmpb.MLOpsServiceBaseInfo)
	if err := request.ReadEntity(svc); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	svc.Id = svcID
	res, err := api.MLOpsServiceRPCSvc.UpdateService(ctx, svc)
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) UpdateServiceVersionInfo(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no serviceID"))
		return
	}
	svcVersionID := request.PathParameter("serviceVersionID")
	if svcVersionID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service version id"))
		return
	}
	svcVersion := new(llmpb.MLOpsServiceVersionInfo)
	if err := request.ReadEntity(svcVersion); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	svcVersion.Id = svcVersionID
	res, err := api.MLOpsServiceRPCSvc.UpdateServiceVersion(ctx, &llmpb.ServiceIDVersionInfo{ServiceId: svcID, ServiceVersionInfo: svcVersion})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) GetSvcYaml(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no serviceID"))
		return
	}
	yamInfo, err := api.MLOpsServiceRPCSvc.GetSvcYaml(ctx, &llmpb.ServiceID{Id: svcID})
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	postResponse(request, response, yamInfo, err)
}

func (s *ServiceV2Resource) UpsertSvcYaml(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no serviceID"))
		return
	}
	yamlInfo := new(llmpb.YamlConfig)
	if err := request.ReadEntity(yamlInfo); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	yamlInfo.ServiceId = svcID
	_, err := api.MLOpsServiceRPCSvc.UpsertSvcYaml(ctx, yamlInfo)
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	postResponse(request, response, struct {
		SvcID string `json:"service_id"`
	}{SvcID: svcID}, err)
}

func (s *ServiceV2Resource) GetMLOpsSvc(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	serviceID := request.PathParameter("serviceID")
	if serviceID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no serviceID"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.QueryByID(ctx, &llmpb.ServiceID{Id: serviceID, OrderBy: request.QueryParameter("order_by")})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) CheckNameUnique(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)

	req := &llmcommon.NameUniqueReq{Name: request.QueryParameter("name")}
	if req.Name == "" {
		postResponse(request, response, nil, stderr.ParamMiss.Error("name"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.CheckNameUnique(ctx, req)
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) GpuTypes(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)

	res, err := api.MLOpsServiceRPCSvc.GpuTypes(ctx)
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) CheckApiUnique(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	apiStr := request.QueryParameter("api")
	if apiStr == "" {
		postResponse(request, response, nil, stderr.ParamMiss.Error("api"))
		return
	}
	req := &llmpb.ApiUniqueReq{Id: svcID, Api: apiStr}
	res, err := api.MLOpsServiceRPCSvc.CheckApiUnique(ctx, req)

	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) ListMLOpsSvcs(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	param := &llmpb.ListServiceReq{
		SourceTypes: make([]llmpb.SourceType, 0),
	}
	name := request.QueryParameter("name")
	if name != "" {
		param.Name = &name
	}
	sourceType := request.QueryParameter("source_type")
	if sourceType != "" {
		for _, st := range strings.Split(sourceType, ",") {
			if _, ok := llmpb.SourceType_value[st]; !ok {
				postResponse(request, response, nil, stderr.Internal.Errorf("source type :%v invalid", st))
				return
			}
			param.SourceTypes = append(param.SourceTypes, llmpb.SourceType(llmpb.SourceType_value[st]))
		}
	}
	nodes := request.QueryParameter("nodes")
	if nodes != "" {
		for _, node := range strings.Split(nodes, ",") {
			param.Nodes = append(param.Nodes, node)
		}
	}
	sourceMetaExtra := request.QueryParameter("source_meta_extra")
	if sourceMetaExtra != "" {
		param.SourceMetaExtra = map[string]string{}
		util.FromJson(sourceMetaExtra, &param.SourceMetaExtra)
	}
	pageSize, _ := strconv.ParseInt(request.QueryParameter("page_size"), 10, 64)
	page, _ := strconv.ParseInt(servingutil.QueryOr(request, "page", "1"), 10, 64)
	param.PageSize = int32(pageSize)
	param.Page = int32(page)
	param.OrderBy = request.QueryParameter("order_by")
	param.Desc, _ = strconv.ParseBool(servingutil.QueryOr(request, "desc", "false"))
	sts := request.QueryParameter("state")
	if sts != "" {
		param.States = strings.Split(sts, ",")
	}
	tenantId := request.QueryParameter("tenantId")
	if tenantId == "" {
		tenantId = request.QueryParameter("tenant_id")
	}
	if tenantId != "" {
		param.Cluster = &tenantId
	}

	res, err := api.MLOpsServiceRPCSvc.List(ctx, param)
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) ListSvcStateStream(request *restful.Request, response *restful.Response) {
	//ctx := util.GenContextFromRequest(request)
	//response.Header().Set("Access-Control-Allow-Origin", "*")
	//response.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	//response.Header().Set("Content-Type", "text/event-stream")
	//response.Header().Set("Cache-Control", "no-cache")
	//response.Header().Set("Connection", "keep-alive")
	//response.Flush()
	//projectID := util.GetProjectID(ctx)
	//if projectID == "" {
	//	SSEError("no project id", response)
	//	return
	//}
	//// 先发送一次全量数据
	//states, err := core.MLOpsSvcStateCacheManager.ListStatusByProjectID(ctx)
	//if err != nil {
	//	SSEError(err.Error(), response)
	//	return
	//}
	//res := core.BuildSvcStateListResp(states)
	//SSEEvent("FullState", res.Marshal(), response)
	//
	//consumer, err := core.NewMLopsSvcConsumer(ctx)
	//if err != nil {
	//	SSEError("init consumer err", response)
	//	return
	//}
	//consumerID, err := core.MLOpsSvcStateCacheManager.RegisterConsumer(ctx, consumer)
	//if err != nil {
	//	SSEError("register consumer err", response)
	//	return
	//}
	//defer core.MLOpsSvcStateCacheManager.UnRegisterConsumer(consumerID)
	//for {
	//	select {
	//	case <-ctx.Done():
	//		stdlog.Infof("close consumer :%v", consumerID)
	//		return
	//	case state := <-consumer.GetStateChan():
	//		msg := core.BuildSvcStateResp(state)
	//		SSEEvent("StateUpdate", msg.Marshal(), response)
	//	}
	//}
	//return
}

func (s *ServiceV2Resource) DelMLOpsSvcVersion(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	svcVersionID := request.PathParameter("serviceVersionID")
	if svcVersionID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service version id"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.DeleteServiceVersion(ctx, &llmpb.ServiceAndVersionID{
		ServiceId:        svcID,
		ServiceVersionId: svcVersionID,
	})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) DelMLOpsSvc(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.DeleteService(ctx, &llmpb.ServiceID{Id: svcID})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) Deploy(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.Deploy(ctx, &llmpb.ServiceID{Id: svcID})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) Offline(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.Offline(ctx, &llmpb.ServiceID{Id: svcID})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) GetMLOpsSvcRuntimeInfo(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.GetRuntimeInfo(ctx, &llmpb.ServiceID{Id: svcID})
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) GetMLOpsContainerLog(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	podName := request.PathParameter("podName")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no pod name"))
		return
	}
	containerID := request.PathParameter("containerID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no container id"))
		return
	}
	logs, err := core.MLOpsSvcManager.GetContainerLog(ctx, svcID, podName, containerID)
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	bytes := make([]byte, 0)
	if logs != nil {
		bytes = logs.Bytes()
	}
	postResponseWriter(request, response, bytes, svcID+"_"+podName+"_"+containerID+".log", err)
}

func (s *ServiceV2Resource) GetMLOpsPodEvent(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	podName := request.PathParameter("podName")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no pod name"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.GetEvents(ctx, &llmpb.GetMLOpsPodEventsReq{
		ServiceId: svcID,
		PodName:   podName,
	})
	postResponseWriter(request, response, res.File, svcID+"_"+podName+"_"+".json", err)
}
func (s *ServiceV2Resource) GetMLOpsEvent(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}

	res, err := api.MLOpsServiceRPCSvc.GetEvents(ctx, &llmpb.GetMLOpsPodEventsReq{
		ServiceId: svcID,
	})
	if res != nil {
		postResponse(request, response, res.Event, err)
		return
	}
	postResponse(request, response, nil, stderr.NotFound.Error("res is nil"))
}

func (s *ServiceV2Resource) GetMLOpsPodEventV2(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	podName := request.PathParameter("podName")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no pod name"))
		return
	}
	res, err := api.MLOpsServiceRPCSvc.GetEvents(ctx, &llmpb.GetMLOpsPodEventsReq{
		ServiceId: svcID,
		PodName:   podName,
	})
	postResponse(request, response, res.Event, err)
}

func (s *ServiceV2Resource) IsStreamAPI(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	req := new(llmpb.CallAPIReq)
	if err := request.ReadEntity(req); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	req.ServiceId = svcID
	httpRspInfo, err := core.MLOpsSvcManager.TestAPIType(ctx, req.ServiceId, req)
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	postResponse(request, response, servingmodel.IsStreamAPI{
		IsStream:       httpRspInfo.IsStream,
		HttpStatusCode: httpRspInfo.HttpStatusCode,
		NonStreamResp:  httpRspInfo.Resp,
	}, err)
}

func (s *ServiceV2Resource) CallAPI(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	req := new(llmpb.CallAPIReq)
	if err := request.ReadEntity(req); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	req.ServiceId = svcID
	res, err := api.MLOpsServiceRPCSvc.CallAPI(ctx, req)
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) UpdateSvcApprovalState(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		postResponse(request, response, nil, stderr.ParamParseFailure.Error("no service id"))
		return
	}
	req := new(servingmodel.UpdateSvcApprovalState)
	if err := request.ReadEntity(req); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	res, err := api.MLOpsServiceRPCSvc.UpdateApprovalState(ctx, &llmpb.UpdateApprovalStateReq{
		ServiceId:     svcID,
		ApprovalState: servingmodel.ApprovalStateDO2DTO(servingmodel.MLopsSvcState(req.State)),
	})
	if err != nil {
		postResponse(request, response, nil, err)
	}
	postResponse(request, response, llmpb.ServiceID{Id: res.ServiceId}, nil)
}

func (s *ServiceV2Resource) CallStreamAPI(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	if svcID == "" {
		SSEError("no service id", response)
		return
	}
	req := new(llmpb.CallAPIReq)
	if err := request.ReadEntity(req); err != nil {
		SSEError(err.Error(), response)
		return
	}
	req.ServiceId = svcID
	response.Header().Set("Content-Type", "text/event-stream")
	response.Header().Set("Access-Control-Allow-Origin", "*")
	response.Header().Set("Connection", "keep-alive")
	response.Flush()
	if err := core.MLOpsSvcManager.CallAPIStream(ctx, svcID, req, response); err != nil {
		SSEError(err.Error(), response)
	}
}

func (s *ServiceV2Resource) GetCodeTemplates(request *restful.Request, response *restful.Response) {
	var err error
	defer func() {
		if err != nil {
			postResponse(request, response, nil, err)
		}
	}()
	svcID := request.PathParameter("id")
	if svcID == "" {
		SSEError("no service id", response)
		return
	}
	ctx := util.GenContextFromRequest(request)
	ctx = servingutil.CtxWithFromHttpHeader(ctx)
	req := new(llmpb.GetServiceInvokingTemplateReq)
	err = request.ReadEntity(req)
	if err != nil {
		err = stderr.Wrap(err, "failed to read GetServiceInvokingTemplateReq")
		return
	}
	platformHost := request.HeaderParameter(stdsrv.TranswarpCustomHeaderReferer)
	stdlog.Infof("platform host e.g. T-Origin-Referer: %s", platformHost)
	if req.PlatformHost == "" {
		req.PlatformHost = platformHost
	}
	if req.ServiceId == "" {
		req.ServiceId = svcID
	}
	rsp, err := api.MLOpsServiceRPCSvc.GetCodeTemplates(ctx, req)
	if err != nil {
		err = stderr.Wrap(err, "failed to get code templates")
		return
	}
	postResponse(request, response, rsp, nil)
}
func (s *ServiceV2Resource) GetHpaMetricOptions(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)

	res, err := core.MLOpsSvcSeldonManager.ListHpaMetricOptions(ctx)
	if err != nil {
		postResponse(request, response, nil, err)
		return
	}
	stdsrv.SuccessResponseMixWithProto(response, res)
}

func (s *ServiceV2Resource) Evaluation(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")
	platformHost := request.HeaderParameter(stdsrv.TranswarpCustomHeaderReferer)
	if platformHost == "" {
		postResponse(request, response, nil, stderr.Internal.Errorf("no host"))
	}
	host, err := api.MLOpsServiceRPCSvc.GetInvokingHost(ctx, &llmpb.GetServiceInvokingTemplateReq{
		InvokingMethod: llmpb.InvokingMethod_INVOKING_METHOD_EXTERNAL,
		PlatformHost:   platformHost,
	})
	if err != nil {
		postResponse(request, response, nil, err)
	}
	res, err := core.MLOpsSvcManager.ServiceEvaluation(ctx, host, svcID)
	postResponse(request, response, res, err)
}

func (s *ServiceV2Resource) EvaluationMock(request *restful.Request, response *restful.Response) {
	req := new(struct {
		ModelName string `json:"model_name"`
		APIUrl    string `json:"api_url"`
		Remark    string `json:"remark"`
	})
	if err := request.ReadEntity(req); err != nil {
		postResponse(request, response, nil, err)
		return
	}
	res := &servingmodel.ServiceEvaluation{
		Code:    0,
		Message: fmt.Sprintf("body :%v", spew.Sdump(req)),
	}
	postResponse(request, response, res, nil)
}

func (s *ServiceV2Resource) ServiceCfg(request *restful.Request, response *restful.Response) {

	postResponse(request, response, &servingmodel.ServingCfgResp{EnableEvaluation: conf.Conf.ServiceEvaluationConfig.Enable}, nil)
}

func (s *ServiceV2Resource) EvaluationHistory(request *restful.Request, response *restful.Response) {
	ctx := util.GenContextFromRequest(request)
	svcID := request.PathParameter("serviceID")

	res, err := core.MLOpsSvcManager.ServiceEvaluationHistory(ctx, svcID)
	if err != nil {
		postResponse(request, response, nil, err)
	}
	resItems := make([]servingmodel.Item, len(res.Metadata.Items))
	if err := copier.Copy(&resItems, &res.Metadata.Items); err != nil {
		postResponse(request, response, nil, err)
	}
	postResponse(request, response, &servingmodel.ServiceEvaluationHistoryResp{Items: resItems}, err)
}

func (s *ServiceV2Resource) EvaluationHistoryMock(request *restful.Request, response *restful.Response) {

	modelName := request.QueryParameter("model_name")
	res := &servingmodel.ServiceEvaluationHistory{
		Code:    0,
		Message: "",
		Metadata: servingmodel.Metadata{
			Items: []servingmodel.ZZItem{
				{
					ModelName:   modelName,
					EvalDataset: "111",
					CreateAt:    "2025-01-01",
					Remark:      "mock data",
				},
				{
					ModelName:   modelName,
					EvalDataset: "222",
					CreateAt:    "2025-01-02",
					Remark:      "mock data",
				},
			},
		},
	}

	postResponse(request, response, res, nil)
}
