package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
	client "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
)

var HippoTdcInitHandlerName = "hippo_tdc_init_handler"

type HippoTdcInitHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := HippoTdcInitHandler{
		name: HippoTdcInitHandlerName,
	}
	RegisterHandler(HippoTdcInitHandlerName, &h)
}

func NewHippoTdcInitHandler(next IHandler) IHandler {
	h := HippoTdcInitHandler{
		name: HippoTdcInitHandlerName,
		next: next,
	}
	return &h
}

func (h *HippoTdcInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, HippoTdcInitHandlerName)

	if conf.C.Tenant.Hippo.Enabled {
		stdlog.Infof("Check whether hippo is installed in namespace %s.", namespace)
		installed, err := client.IsTdcHippoInstalled(namespace)
		// hippo instance already exists, return
		if err != nil {
			stdlog.Errorf("Try get hippo before install hippo failed: %+v", err)
		} else if installed {
			stdlog.Infof("Hippo has installed in namespace %s.", namespace)
		} else {
			stdlog.Infof("Installing hippo in namespace %s", namespace)
			hippoServiceId, err := client.GetTdcHippoServiceId()
			if err != nil {
				stdlog.Errorf("Get hippo service id from broker failed: %+v", err)
				return err
			}
			playload := models.TdcCreateInstanceReq{
				GuardianToken:       client.GetGuardianAccessToken(),
				TenantID:            namespace,
				InstanceName:        conf.C.Tenant.TDC.BrokerService.HippoInstanceName,
				ServiceID:           hippoServiceId,
				SecurityOn:          true,
				NodeSelectorConfigs: map[string]interface{}{},
				CustomConfigs: []models.InstaceCustomConfig{
					{
						Type:         "hippo",
						TemplateName: conf.C.Tenant.TDC.BrokerService.HippoTemplateName,
						CustomConfig: map[string]interface{}{},
					},
				},
			}

			baseUrl, err := client.GetTdcBrokerServiceBaseUrl()
			if err != nil {
				stdlog.Errorf("Get tdc broker service base url failed: %+v", err)
				return err
			}
			c := client.NewTdcHTTPClient(baseUrl)
			_, err = c.DoRequest("PUT", "/v2/service-instances/create-instance", playload, nil)
			if err != nil {
				stdlog.Errorf("Installing hippo failed: %+v", err)
			} else {
				stdlog.Infof("Install hippo in namespace %s completed.", namespace)
			}
		}
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *HippoTdcInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
