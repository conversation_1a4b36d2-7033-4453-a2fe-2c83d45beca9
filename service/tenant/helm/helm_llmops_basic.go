package tenant

import "transwarp.io/applied-ai/central-auth-service/models"

var LlmopsBasicReleaseName = "llmops-basic"
var LlmopsBasicChartPath = "/opt/charts/llmops-basic-0.0.0-dev.tgz"
var LlmopsBasicValues1File = "./etc/llmops-basic/values1.yaml"
var LlmopsBasicValues2File = "./etc/llmops-basic/values2.yaml"

func InstallLlmopsBasic(ns string, setParams []string) error {
	return HelmInstall(ns, LlmopsBasicReleaseName, LlmopsBasicChartPath, setParams, LlmopsBasicValues1File, LlmopsBasicValues2File)
}

func UninstallLlmopsBasic(ns string) error {
	return HelmUninstall(ns, LlmopsBasicReleaseName)
}

func GetLlmopsBasicRelease(ns string) (*models.Instance, error) {
	release, err := GetRelease(ns, LlmopsBasicReleaseName)
	if err != nil {
		return nil, err
	}
	return helmRelease2Instance(release), nil
}

func LlmopsBasicExists(ns string) (bool, error) {
	return HelmReleaseExists(ns, LlmopsBasicReleaseName)
}
