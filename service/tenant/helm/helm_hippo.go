package tenant

import (
	"helm.sh/helm/v3/pkg/release"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
)

var HippoReleaseName = "llmops-hippo"
var HippoChartPath = "/opt/charts/hippo-0.0.0-dev.tgz"
var HippoValuesFile = "./etc/hippo/values.yaml"

var statusMap = map[string]string{
	"deployed":         "RUNNING",
	"unknown":          "FAILED",
	"uninstalled":      "FAILED",
	"superseded":       "FAILED",
	"failed":           "FAILED",
	"uninstalling":     "FAILED",
	"pending-install":  "FAILED",
	"pending-upgrade":  "FAILED",
	"pending-rollback": "FAILED",
}

// curl -XGET -u shiva:shiva autocv-hippo-service:7789/api/shiva/overview
// check hippo status

func helmRelease2Instance(r *release.Release) *models.Instance {
	instance := &models.Instance{
		Id:     r.Name,
		Name:   r.Name,
		Status: statusMap[string(r.Info.Status)],
	}
	return instance
}

func InstallHippo(ns string) error {
	stdlog.Infoln("Installing hippo...")
	releaseName := HippoReleaseName
	err := HelmInstall(ns, releaseName, HippoChartPath, []string{}, HippoValuesFile)
	if err != nil {
		stdlog.Errorf("Helm install %s failed: %v, in ns %s k8s strategy.", HippoReleaseName, err, ns)
		return err
	}
	return nil
}

func GetHippoRelease(ns string) (*models.Instance, error) {
	release, err := GetRelease(ns, HippoReleaseName)
	if err != nil {
		return nil, err
	}
	instance := helmRelease2Instance(release)
	running := httpclient.IsHippoRunning(ns)
	if running {
		instance.Status = "RUNNING"
	} else {
		instance.Status = "UNHEALTHY"
	}
	return instance, nil
}

func HippoExists(ns string) (bool, error) {
	return HelmReleaseExists(ns, HippoReleaseName)
}

func UninstallHippo(ns string) error {
	stdlog.Infoln("Uninstalling hippo...")
	releaseName := HippoReleaseName
	err := HelmUninstall(ns, releaseName)
	if err != nil {
		stdlog.Errorf("Helm uninstall %s failed: %v, in ns %s k8s strategy.", HippoReleaseName, err, ns)
		return err
	}
	return nil
}
