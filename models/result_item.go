/*
 * AUTOCV REST API
 *
 * REST API for AUTOCV
 *
 * API version: v22.08
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package models

type ResultItem struct {
	Objects []ResultItemObject `json:"objects,omitempty"`

	Attributes []ResultItemObjectAttr `json:"attributes,omitempty"`

	Id string `json:"id,omitempty"`
}

type InsResult struct {
	Filename string             `json:"filename,omitempty"`
	Objects  []ResultItemObject `json:"objects,omitempty"`
}

type KeyPointsTrainingResult struct {
	Images      []ImageRes      `json:"images,omitempty"`
	Annotations []AnnotationRes `json:"annotations,omitempty"`
	Categories  []CategoryRes   `json:"categories,omitempty"`
}

type ImageRes struct {
	Id       string `json:"id,omitempty"`
	FileName string `json:"file_name,omitempty"`
}

type AnnotationRes struct {
	Id           string    `json:"id,omitempty"`
	ImageId      string    `json:"image_id,omitempty"`
	CategoryId   string    `json:"category_id,omitempty"`
	Bbox         []float64 `json:"bbox,omitempty"`
	Keypoints    []float64 `json:"keypoints,omitempty"`
	NumKeypoints int32     `json:"num_keypoints,omitempty"`
}

type CategoryRes struct {
	Supercategory string   `json:"supercategory,omitempty"`
	Id            string   `json:"id,omitempty"`
	Name          string   `json:"name,omitempty"`
	Keypoint      []string `json:"keypoint,omitempty"`
}
