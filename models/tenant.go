package models

import "transwarp.io/aip/llmops-common/pkg/expense"

const (
	TenantUidPrefix = "llmops-"
)

type Tenant struct {
	TenantName           string                        `json:"tenant_name" gorm:"column:name;type:varchar(500);"`
	TenantUid            string                        `json:"tenant_uid" gorm:"primary_key;column:uid;type:varchar(64);"`
	TenantDescription    string                        `json:"tenant_description" gorm:"column:description;type:text;"`
	TenantLogo           string                        `json:"tenant_logo" gorm:"column:logo;type:varchar(1000);"`
	TenantQuotas         TenantResourceQuota           `json:"tenant_quotas" gorm:"-"`
	TenantStatus         string                        `json:"status" gorm:"-"`
	Creator              string                        `json:"creator" gorm:"column:creator;type:varchar(500);"`
	CreateTime           uint64                        `json:"createTime" gorm:"column:create_time"`
	TenantLabels         map[string]string             `json:"-" gorm:"-"`
	TenantAnnotations    map[string]string             `json:"-" gorm:"-"`
	TccUrl               string                        `json:"tcc_url" gorm:"-"`
	ClusterNamespace     string                        `json:"cluster_namespace" gorm:"-"` // TDC5 的 一个租户对应的cluster namespace不一定和 tenant uid 一致
	HippoServiceName     string                        `json:"hippo_service_name"`
	ResourceGroupDetails []expense.ResourceGroupTenant `json:"resource_group_details" gorm:"-"`
	ResourceGroupIDs     []string                      `json:"resource_group_ids" gorm:"-"`
	ProjectIDInfos       []ProjectIDInfo               `json:"project_infos" gorm:"-"`
	CanDelete            bool                          `json:"can_delete" gorm:"-"`
}

func (t *Tenant) DeleteSafe(tdcModel bool) bool {
	if tdcModel {
		return false
	}
	for _, rg := range t.ResourceGroupDetails {
		if !rg.TenantStatus.Idle {
			return false
		}
	}
	return true
}

type ProjectIDInfo struct {
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`
	Creator     string `json:"creator"`
}

type ResourceQuotaSpec struct {
	LimitsCpu       string `json:"limits_cpu"`
	LimitsMemory    string `json:"limits_memory"`
	RequestsCpu     string `json:"requests_cpu"`
	RequestsMemory  string `json:"requests_memory"`
	RequestsStorage string `json:"requests_storage"`
	Pods            string `json:"pods"`

	Bandwidth        string `json:"bandwidth"`
	EgressBandwidth  string `json:"egress_bandwidth"`
	IngressBandwidth string `json:"ingress_bandwidth"`

	Gpu       string `json:"gpu"`
	GpuMemory string `json:"gpu_memory"`

	Knowl                string `json:"knowl"`
	KnowledgeBaseStorage string `json:"knowledge_base_storage"`
	FileStorage          string `json:"file_storage"`
}

type Attribute struct {
	Name           string `json:"name"`
	AllowExpansion bool   `json:"allow_expansion"`
	Limit          string `json:"limit"`
}

type TenantResourceQuota struct {
	NameSpace string            `json:"namespace"`
	QuotaName string            `json:"quota_name"`
	Hard      ResourceQuotaSpec `json:"hard"`
	Used      ResourceQuotaSpec `json:"used"`

	QuotaItemAttributes []Attribute `json:"quota_item_attributes"`
}

type UnifyUnitTenantQuota struct {
	QuotaName string            `json:"quotaName"`
	Hard      ResourceQuotaSpec `json:"hard"`
	Used      ResourceQuotaSpec `json:"used"`
}

type Instance struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"`
}

// for tdc
type TdcTenantList struct {
	Data []TdcTenant `json:"data"`
}

type TdcTenant struct {
	TenantUid         string `json:"tenantUid"`
	TenantName        string `json:"tenantName"`
	TenantStatus      string `json:"tenantStatus"`
	TenantDescription string `json:"tenantDescription"`
	Creator           string `json:"creator"`
	Password          string `json:"password"`
	Company           string `json:"company"`
	Department        string `json:"department"`
	UserFullName      string `json:"userFullName"`
	UserEmail         string `json:"userEmail"`
	UserPhone         string `json:"userPhone"`
	Quota             string `json:"quota"`
	TccUrl            string `json:"tccUrl"`
	CreateTime        uint64 `json:"createTime"`
}

type InstaceCustomConfig struct {
	Type         string                 `json:"type"`
	TemplateName string                 `json:"templateName"`
	CustomConfig map[string]interface{} `json:"customConfig"`
}

type TdcCreateInstanceReq struct {
	GuardianToken       string                 `json:"guardianToken"`
	TenantID            string                 `json:"tenantId"`
	InstanceName        string                 `json:"instanceName"`
	ServiceID           string                 `json:"serviceID"`
	SecurityOn          bool                   `json:"SecurityOn"`
	NodeSelectorConfigs map[string]interface{} `json:"nodeSelectorConfigs"`
	CustomConfigs       []InstaceCustomConfig  `json:"customConfigs"`
}

type TdcSearchInstanceReq struct {
	TenantId      string
	GuardianToken string
}

type TdcInstance struct {
	CategoryID           int    `json:"categoryId"`
	ExternalInstanceCode string `json:"externalInstanceCode"`
	ExternalOrderID      string `json:"externalOrderId"`
	ExternalProjectCode  string `json:"externalProjectCode"`
	ID                   int    `json:"id"`
	Name                 string `json:"name"`
	UUID                 string `json:"uuid"`
	Nodes                int    `json:"nodes"`
	ProductUUID          string `json:"productUuid"`
	Status               string `json:"status"`
	Visibility           string `json:"visibility"`
}

type HippoNode struct {
	Host                   string `json:"host"`
	Port                   int    `json:"port"`
	Shards                 int    `json:"shards"`
	BlockCacheUsed         int    `json:"block_cache_used"`
	BlockCacheCapacity     int    `json:"block_cache_capacity"`
	SST                    int    `json:"sst"`
	MemReader              int    `json:"mem.reader"`
	MemMemtable            int    `json:"mem.memtable"`
	EmbeddingSegments      int    `json:"embedding.segments"`
	EmbeddingInMemory      int    `json:"embedding.in_memory"`
	UsedStoreCapacityUnits int    `json:"used_store_capacity_units"`
}

type HippoNodeResponse struct {
	Nodes []HippoNode `json:"nodes"`
}

type TenantEnv struct {
	GuardianAccessToken string `json:"guardian_access_token"`
	Strategy            string `json:"strategy"`
	TDC5Address         string `json:"tdc5_address"`
}

type TenantStatusLocationUrl struct {
	Location string `json:"location"`
}

type HamiGpu struct {
	DeviceUuid   string `json:"deviceuuid"`
	Power        string `json:"power"`
	Memory       string `json:"memory"`
	Utilization  string `json:"utilization"`
	Model        string `json:"model"`
	Index        string `json:"index"`
	Availability bool   `json:"availability"`
}

// for tdc5
type Tdc5TenantList struct {
	Data []Tdc5Tenant `json:"data"`
}

type Tdc5Tenant struct {
	TenantUid    string        `json:"tenantUid"`
	TenantName   string        `json:"tenantName"`
	TenantStatus string        `json:"tenantStatus"`
	Creator      string        `json:"creator"`
	Clusters     []Tdc5Cluster `json:"clusters"`
}

type Tdc5Cluster struct {
	ClusterId        string `json:"clusterId"`
	ClusterNamespace string `json:"clusterNamespace"`
}

type TenantIDs struct {
	IDs []string `json:"ids"`
}
