package models

import (
	"fmt"
	"strings"
)

const (
	GPURulePrefix        = "GPU资源组合"
	GPURuleMini          = "迷你版"
	GPURuleBase          = "基础版"
	GPURuleUpgraded      = "升级版"
	GPURuleAdvanced      = "高级版"
	GPURuleBaseGPUCnt    = 1
	GPURuleBaseCPUCnt    = 8
	GPURuleBaseMemoryGiB = 32
	GPURuleSplit         = " - "
)

type GPURuleLevel int

const (
	GPURuleLevel1 GPURuleLevel = 1
	GPURuleLevel2 GPURuleLevel = 2
	GPURuleLevel3 GPURuleLevel = 3
	GPURuleLevel0 GPURuleLevel = 0
)

var GPURuleMap = map[GPURuleLevel]string{
	GPURuleLevel1: GPURuleBase,
	GPURuleLevel2: GPURuleUpgraded,
	GPURuleLevel3: GPURuleAdvanced,
	GPURuleLevel0: GPURuleMini,
}

var ResourceCntMap = map[GPURuleLevel]float64{
	GPURuleLevel1: 1,
	GPURuleLevel2: 2,
	GPURuleLevel3: 4,
	GPURuleLevel0: 0.5,
}

func GenGPURuleName(level GPURuleLevel) string {
	return fmt.Sprintf("[%s-%s] {gpu} × %.1f%s%.1f 核CPU - %.1fGiB内存", GPURulePrefix,
		GPURuleMap[level],
		GPURuleBaseGPUCnt*ResourceCntMap[level],
		GPURuleSplit,
		GPURuleBaseCPUCnt*ResourceCntMap[level],
		GPURuleBaseMemoryGiB*ResourceCntMap[level])
}

func GetGPURuleFromRule(rule string) string {
	return strings.Split(rule, GPURuleSplit)[0]
}
