package models

import (
	"strconv"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

type ExpirationTimeSelect string

const (
	Custom  ExpirationTimeSelect = "custom"  // 自定义, 此时 ExpirationTime 必填
	Nolimit ExpirationTimeSelect = "nolimit" // 无限制
	Day1    ExpirationTimeSelect = "1"       // 24 小时
	Day7    ExpirationTimeSelect = "7"       // 7 天
	Day15   ExpirationTimeSelect = "15"      // 15 天
	Day30   ExpirationTimeSelect = "30"      // 30 天
)

type UserReq struct {
	Uid                  uint64               `json:"uid"`
	UserName             string               `json:"user_name"`
	FullName             string               `json:"full_name"`
	Email                string               `json:"email"`
	Password             string               `json:"password"`
	UserGroupNames       []string             `json:"user_group_names"`
	PlatformRoleId       uint64               `json:"platform_role_id"`
	DefaultProject       string               `json:"default_project"`
	Status               UserStatus           `json:"status"`
	ExpirationTimeSelect ExpirationTimeSelect `json:"expiration_time_select"` // 选择的有效期: 1(24小时),7,15,30,nolimit(无限制),自定义(custom)
	ExpirationTime       int64                `json:"expiration_time"`        // 秒时间戳 过期时间: 仅选择 custom 时
	WhiteIps             []string             `json:"white_ips"`
	PhoneNumber          string               `json:"phone_number" description:"手机号: 选填,数字,+,-"`
}

// SetExpriationTime 通过 ExpirationTimeSelect 设置 ExpirationTime, 校验数据正确性
func (u *UserReq) SetExpriationTime() error {
	switch u.ExpirationTimeSelect {
	case Day1, Day7, Day15, Day30:
		day, _ := strconv.Atoi(string(u.ExpirationTimeSelect))
		u.ExpirationTime = time.Now().AddDate(0, 0, day).Unix()
	case Nolimit:
		u.ExpirationTime = 0
	case Custom:
		if u.ExpirationTime == 0 {
			return stderr.Errorf("自定义时间不能为空")
		}
		if time.Unix(u.ExpirationTime, 0).Before(time.Now()) {
			return stderr.Errorf("时间范围错误")
		}
	default:
		return stderr.Errorf("时间范围选择错误")
	}
	return nil
}

type UserListReq struct {
	UsernameLike, FullName string
	PhoneNumber, ProjectId string
}

type UserResp struct {
	Uid                  uint64               `json:"uid"`
	UserName             string               `json:"user_name"`
	FullName             string               `json:"full_name"`
	Email                string               `json:"email"`
	PlatformRoleId       uint64               `json:"platform_role_id"`
	PlatformRoleName     string               `json:"platform_role_name"`
	ProjectRoleIds       []uint64             `json:"project_role_ids"`
	UserGroupNames       []string             `json:"user_group_names"`
	CreateUser           string               `json:"create_user"`
	CreateTime           time.Time            `json:"create_time"`
	DefaultProject       string               `json:"default_project"`
	Status               UserStatus           `json:"status"`
	ExpirationTimeSelect ExpirationTimeSelect `json:"expiration_time_select"` // 选择的有效期: 1(24小时),7,15,30,nolimit(无限制),自定义(custom)
	ExpirationTime       int64                `json:"expiration_time"`        // 秒时间戳 过期时间
	WhiteIps             []string             `json:"white_ips"`
	PhoneNumber          string               `json:"phone_number" description:"手机号"`
}

type UserCountResp struct {
	Count int `json:"count"`
}

type UserProfileResp struct {
	Uid              int64             `json:"uid"`
	UserName         string            `json:"user_name"`
	FullName         string            `json:"full_name"`
	Email            string            `json:"email"`
	UserGroupNames   []string          `json:"user_group_names"`
	CreateUser       string            `json:"create_user"`
	CreateTime       time.Time         `json:"create_time"`
	PlatformRoleName string            `json:"platform_role_name"`
	PlatformRoleId   uint64            `json:"platform_role_id"`
	ProjectRoleIds   []uint64          `json:"project_role_ids"`
	ProjectRoleNames []string          `json:"project_role_names"`
	Permissions      []*PermissionResp `json:"permissions"`
	DefaultProject   string            `json:"default_project"`
	ProjectLocation  string            `json:"project_location"`
}

func (Group) TableName() string {
	return "groups"
}

func (UserGroup) TableName() string {
	return "user_group"
}

type Group struct {
	Id          uint64 `json:"id" gorm:"primary_key"`
	Name        string `json:"name" gorm:"column:name;type:varchar(500)"`
	Description string `json:"description" gorm:"column:description;type:text"`
	// Users       []*dao.User `json:"users" gorm:"many2many:user_group;"`
	CreateUser string    `json:"create_user" gorm:"column:create_user;type:varchar(500)"`
	CreateTime time.Time `json:"create_time" gorm:"column:create_time"`
}

type GroupReq struct {
	Gid            uint64   `json:"gid"`
	Name           string   `json:"name"`
	UserNames      []string `json:"user_names"`
	PlatformRoleId uint64   `json:"platform_role_id"`
	Description    string   `json:"description"`
}

type GroupResp struct {
	Gid              uint64    `json:"gid"`
	Name             string    `json:"name"`
	PlatformRoleName string    `json:"platform_role_name"`
	PlatformRoleId   uint64    `json:"platform_role_id"`
	ProjectRoleIds   []uint64  `json:"project_role_ids"`
	Description      string    `json:"description"`
	UserNames        []string  `json:"user_names"`
	CreateUser       string    `json:"create_user"`
	CreateTime       time.Time `json:"create_time"`
}

type UserGroup struct {
	Id         uint64    `json:"id" gorm:"primary_key"`
	Username   string    `json:"username" gorm:"column:username;type:varchar(500)"`
	GroupName  string    `json:"group_name" gorm:"column:group_name;type:varchar(500)"`
	CreateTime time.Time `json:"create_time"`
}

type RoleReq struct {
	Name        string       `json:"name"`
	Description string       `json:"description"`
	Creator     string       `json:"creator"`
	Permissions []Permission `json:"permissions"`
}

type PasswordReq struct {
	OldPassword string `json:"old_password"`
	Password    string `json:"password"`
}

type RoleType string

const (
	PlatformRoleType RoleType = "platform"
	ProjectRoleType  RoleType = "project"
)

func (roleType RoleType) String() string {
	switch roleType {
	case PlatformRoleType:
		return "platform"
	case ProjectRoleType:
		return "project"
	}
	return ""
}

type BindType string

const (
	UserType      BindType = "user"
	UserGroupType BindType = "user_group"
)

type PlatformRoleName string

const (
	GeneralUser        PlatformRoleName = "普通用户"
	Administrator      PlatformRoleName = "管理员"
	SuperAdministrator PlatformRoleName = "超级管理员"
)

func (platformRoleName PlatformRoleName) String() string {
	switch platformRoleName {
	case GeneralUser:
		return "普通用户"
	case Administrator:
		return "管理员"
	case SuperAdministrator:
		return "超级管理员"
	}
	return ""
}

type ProjectRoleName string

const (
	ProjectManager                   ProjectRoleName = "空间负责人"
	DataEngineer                     ProjectRoleName = "数据工程师"
	MachineLearningEngineer          ProjectRoleName = "机器学习工程师"
	ModelOperationManagementEngineer ProjectRoleName = "模型运营管理工程师"
	BuiltInDataShare                 ProjectRoleName = "内置数据共享"
)

func (projectRoleName ProjectRoleName) String() string {
	switch projectRoleName {
	case ProjectManager:
		return "空间负责人"
	case DataEngineer:
		return "数据工程师"
	case MachineLearningEngineer:
		return "机器学习工程师"
	case ModelOperationManagementEngineer:
		return "模型运营管理工程师"
	case BuiltInDataShare:
		return "内置数据共享"
	}
	return ""
}

func (bindType BindType) String() string {
	switch bindType {
	case UserType:
		return "user"
	case UserGroupType:
		return "user_group"
	}
	return ""
}

type UserStoreType string

const (
	Local    UserStoreType = "local"
	Guardian UserStoreType = "guardian"
)

func (userStoreType UserStoreType) String() string {
	switch userStoreType {
	case Local:
		return "local"
	case Guardian:
		return "guardian"
	}
	return ""
}

type GroupName string

const (
	AllUsers GroupName = "all_users"
)

func (groupName GroupName) String() string {
	switch groupName {
	case AllUsers:
		return "all_users"
	}
	return ""
}

type PermissionAction string

const (
	Access PermissionAction = "access"
	Read   PermissionAction = "read"
	All    PermissionAction = "*"
)

func (permissionAction PermissionAction) String() string {
	switch permissionAction {
	case Access:
		return "access"
	case Read:
		return "read"
	case All:
		return "*"
	}
	return ""
}

func (Role) TableName() string {
	return "role"
}

func (UserRole) TableName() string {
	return "user_role"
}

func (Permission) TableName() string {
	return "permission"
}

func (RolePermission) TableName() string {
	return "role_permission"
}

type Role struct {
	Id          uint64            `json:"id" gorm:"primary_key"`
	Name        string            `json:"name" gorm:"column:name;type:varchar(500)"`
	NameLocals  map[string]string `json:"-" gorm:"type:json;serializer:json"` // 国际化翻译 json: {en:"..."}
	Type        RoleType          `json:"type" gorm:"column:type;type:varchar(20)"`
	Description string            `json:"description" gorm:"column:description;type:text"`
	DescLocals  map[string]string `json:"-" gorm:"type:json;serializer:json"` // json: {en:"..."}
	CreateUser  string            `json:"create_user" gorm:"column:create_user;type:varchar(500)"`
	CreatedAt   time.Time         `json:"created_at" gorm:"column:created_at"`
	// 仅用作读配置，不需要保存
	Permissions []string `json:"permissions" gorm:"-"`
}

func (r *Role) GetNameWithLocal(lc stdsrv.Language) string {
	if v, ok := r.NameLocals[lc.String()]; ok && v != "" {
		return v
	}
	return r.Name
}

type UserRoleReq struct {
	Name      string   `json:"name" gorm:"column:name;type:varchar(500)"`
	RoleId    uint64   `json:"role_id" gorm:"column:role_id;type:int"`
	BindType  BindType `json:"bind_type" gorm:"column:bind_type;type:varchar(20)"`
	ProjectId string   `json:"project_id" gorm:"column:project_id;type:varchar(500)"`
}

type UserRole struct {
	UserRoleReq
	Id         uint64    `json:"id" gorm:"primary_key"`
	CreateTime time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime time.Time `json:"update_time" gorm:"column:update_time"`
}

type RoleResp struct {
	Role
	Permissions []*Permission `json:"permissions"`
}

type Permission struct {
	ID          uint64            `json:"id" gorm:"primary_key"`
	Code        string            `json:"code" gorm:"column:code;type:varchar(500)"`
	Name        string            `json:"name" gorm:"column:name;type:varchar(500)"`
	NameLocals  map[string]string `json:"-" gorm:"type:json;serializer:json"` // 国际化翻译 json: {en:"..."}
	Action      string            `json:"action" gorm:"column:action;type:varchar(500)"`
	Type        string            `json:"type" gorm:"column:type;type:varchar(20)"`
	Description string            `json:"description" gorm:"column:description;type:text"`
	DescLocals  map[string]string `json:"-" gorm:"type:json;serializer:json"` // json: {en:"..."}
	Parent      string            `json:"parent" gorm:"column:parent;type:varchar(500)"`
	CreatedAt   time.Time         `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time         `json:"updated_at" gorm:"column:updated_at"`
}

func (p *Permission) GetNameWithLocal(lc stdsrv.Language) string {
	if v, ok := p.NameLocals[lc.String()]; ok && v != "" {
		return v
	}
	return p.Name
}

type PermissionResp struct {
	Id     uint64   `json:"id"`
	Code   string   `json:"code"`
	Name   string   `json:"name"`
	Action []string `json:"action"`
}

type PermissionTreeNode struct {
	ID       uint64                `json:"id"`
	Code     string                `json:"code"`
	Name     string                `json:"name"`
	Action   string                `json:"action"`
	Children []*PermissionTreeNode `json:"children"`
}

type RolePermission struct {
	Id           uint64    `json:"id" gorm:"primary_key"`
	RoleId       uint64    `json:"role_id" gorm:"column:role_id;type:int"`
	PermissionId uint64    `json:"permission_id" gorm:"column:permission_id;type:int"`
	CreateTime   time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime   time.Time `json:"update_time" gorm:"column:update_time"`
}

type RolePermissionReq struct {
	RoleId       uint64 `json:"role_id"`
	PermissionId uint64 `json:"permission_id"`
}

type GuardianAccessToken struct {
	Name    string `json:"name"`
	Content string `json:"content"`
}

type UserImportReq struct {
	Path string `json:"path"`
}

type ValidateUserImportResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type UserStatusReq struct {
	Id     uint64     `json:"id"`
	Status UserStatus `json:"status"`
}

type UserStatus = int // 用户启用状态

// ?? 改为与前端一致, 经验证库中使用的枚举与前端相同, 与之前定义的不同
const (
	Enable  UserStatus = 1
	Disable UserStatus = 2
)
