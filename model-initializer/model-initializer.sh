#!/bin/bash

# mount nfs dir
LOCAL_MOUNT_PATH=/tmp/model
mkdir -p ${LOCAL_MOUNT_PATH}

#
#if [ ${NFS_TYPE} ] && [[ ${NFS_TYPE} == "juicefs" ]]; then
#  echo "juicefs mounted"
#  SOPHON_USER_TOKEN=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw
#  curl -H "Authorization: Bearer ${SOPHON_USER_TOKEN}" -O ${SOPHON_GATEWAY_URL}/resource/api/files/nfs/config/juicefs.tar.gz
#  tar -xvf juicefs.tar.gz
#  sh .juicefs_config/jfs-mount.sh ${LOCAL_MOUNT_PATH}
#else
#  echo "nfs mounted"
#  mount -t nfs -o soft,retrans=1,timeo=14,fg,nolock,vers=3 ${nfs_host}:${nfs_path} ${LOCAL_MOUNT_PATH}
#fi

python downloadModel.py $*