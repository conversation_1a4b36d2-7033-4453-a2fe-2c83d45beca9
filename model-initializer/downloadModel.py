import os
import sys
import shutil
import logging
from minio import Minio
from minio.error import S3Error

model_path = '/app/model'
tmp_mount_path = '/tmp/model'
if __name__ == '__main__':
    args = sys.argv
    if not os.path.exists("/app/model/xai"):
        os.mkdir("/app/model/xai")
# nfs
nfs_host = os.environ.get("nfs_host")
nfs_path = os.environ.get("nfs_path")
if nfs_host and nfs_path:
    if not os.path.exists(tmp_mount_path):
        os.mkdir(tmp_mount_path)
    mount_res = os.system(
        "mount -t nfs -o soft,retrans=1,timeo=14,fg,nolock,vers=3 " + nfs_host + ":" + nfs_path + " " + tmp_mount_path)
    if mount_res != 0:
        raise Exception("mount failure, return code" + str(mount_res))
    # s3client
    s3_end_point = os.environ.get("s3_end_point")
    s3_access_key = os.environ.get("s3_access_key")
    s3_secret_key = os.environ.get("s3_secret_key")
    s3_default_bucket = os.environ.get("s3_default_bucket")
    s3_default_path = os.environ.get("s3_default_path")
    s3_secure = os.environ.get("s3_secure")
    response = None
    secure = False
    if "true" == s3_secure:
        secure = True
    client = Minio(
        s3_end_point,
        access_key=s3_access_key,
        secret_key=s3_secret_key,
        secure=secure
    )
    found = client.bucket_exists(s3_default_bucket)
    if not found:
        raise Exception("mount failure, s3 bucket not found")
    # download
    for i in range(int(len(args) / 2)):
        try:
            src = args[i * 2 + 1]
            dest = args[i * 2 + 2]
            if src.startswith("nfs:"):
                srcfile = src[4:]
                srcPath = os.path.join(tmp_mount_path, srcfile)
                destPath = os.path.join(model_path, dest)
                if os.path.isdir(srcPath):
                    if not os.path.exists(destPath):
                        shutil.copytree(srcPath, destPath)
                else:
                    shutil.copy(srcPath, destPath)
            if src.startswith("s3:"):
                destPath = os.path.join(model_path, dest)
                if not os.path.exists(destPath):
                    os.mkdir(destPath)
                srcFile = src[3:]
                objects = client.list_objects(s3_default_bucket, prefix=srcFile, recursive=True)
                for modelFile in objects:
                    try:
                        if not modelFile.is_dir:
                            response = client.get_object(s3_default_bucket, modelFile.object_name)

                            if "" == s3_default_path:
                                pathList = str.split(modelFile.object_name, "/")
                            else:
                                pathList = str.split(modelFile.object_name.replace(s3_default_path + "/", ""), "/")
                            destDir = destPath + "/" + "/".join(pathList[2:len(pathList) - 1])
                            destFile = destPath + "/" + "/".join(pathList[2:])
                            if not os.path.exists(destDir):
                                os.makedirs(destDir)
                            if not os.path.exists(destFile):
                                os.mknod(destFile)
                            with open(destFile, "ab") as tmp_file:
                                for data in response.stream(amt=1024 * 1024):
                                    tmp_file.write(data)
                    finally:
                        if response:
                            response.close()
        except shutil.Error as e:
            for src, dst, msg in e.args[0]:
                print(dst, src, msg)
        except S3Error as s3e:
            print("s3 error", s3e)
