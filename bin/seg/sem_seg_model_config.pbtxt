name: "sem_seg_model"
platform: "onnxruntime_onnx"

input [
  {
    name: "input"
    data_type: TYPE_FP32
    dims: [ 1, 3, -1, -1 ]

  }
]
output [
  {
    name: "output"
    data_type: TYPE_INT64
    dims: [ 1, 1, -1, -1]
  }
]



model_warmup [
  {
    name: "warmup"
    inputs {
      key: "input"
      value: {                                                                      
            data_type: TYPE_FP32                                                      
            dims: [1, 3, 416, 416]                                                  
            zero_data: true                                                           
      }
    }
  }
]

