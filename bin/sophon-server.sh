#!/bin/bash
set -x
set -e
# Runs sophon server.

export SOPHON_HOME=$(cd $(dirname $0)/.. && pwd)
# if SOPHON_SERVICE_ID is set, it is in a container launched by Manager

export SOPHON_CONF_DIR=/etc/sophon/conf

export BASE_CONF_DIR=${SOPHON_CONF_DIR}/base
export SOPHON_DRIVER_DIR=${SOPHON_HOME}/drivers

# this path is for user uploaded jars
export USER_DRIVER_DIR=/home/<USER>/sophon/driver
if [ ! -d "$USER_DRIVER_DIR" ]; then
    mkdir -p ${USER_DRIVER_DIR}
    cp -r ${SOPHON_DRIVER_DIR}/* ${USER_DRIVER_DIR}
fi

if [ -f "${SOPHON_CONF_DIR}/common-env.sh" ]; then
  # Promote all variable declarations to environment (exported) variables
  set -a
  . "${SOPHON_CONF_DIR}/common-env.sh"
  set +a
fi

if [ -f "${BASE_CONF_DIR}/env.sh" ]; then
  # Promote all variable declarations to environment (exported) variables
  set -a
  . "${BASE_CONF_DIR}/env.sh"
  set +a
fi

# mount nfs dir
if [ -f "${SOPHON_CONF_DIR}/nfs-env.sh" ]; then
  # Promote all variable declarations to environment (exported) variables
  set -a
  mkdir -p /sophon/project
  . "${SOPHON_CONF_DIR}/nfs-env.sh"
  set +a
  mount -t nfs -o soft,retrans=1,timeo=14,fg,nolock ${SOPHON_NFS_IP}:${SHARE_DIR} /sophon/project
fi

export NFS_DRIVER_DIR=/sophon/project/driver
if [ ! -d "$NFS_DRIVER_DIR" ]; then
    mkdir -p ${NFS_DRIVER_DIR}
    cp -r ${SOPHON_DRIVER_DIR}/* ${NFS_DRIVER_DIR}
fi


# Find the java binary
if [ -n "${JAVA_HOME}" ]; then
  RUNNER="${JAVA_HOME}/bin/java"
elif [ `command -v java` ]; then
  RUNNER="java"
else
  echo "JAVA_HOME is not set" >&2
  exit 1
fi

change_permission() {
  chown -R sophon:sophon /home/<USER>/sophon
  chown -R sophon:sophon /usr/lib/sophon
  chown -R sophon:sophon ${NFS_DRIVER_DIR}
  mkdir -p /var/log/${SOPHON_SERVICE_ID}
  chmod -R 777 /var/log/${SOPHON_SERVICE_ID}
  set +x
  set +e
  chmod o+x /srv/kubernetes
  chmod -R o+r /srv/kubernetes
  set -x
  set -e
}

start_sophon_server() {
  LIBDIR1="$SOPHON_HOME/server-jars"
  if [ ! -d "$LIBDIR1" ]; then
    LIBDIR1="$SOPHON_HOME/server/target/server-jars"
  fi
  if [ ! -d "$LIBDIR1" ]; then
    echo "Could not find server jars directory." 1>&2
    exit 1
  fi

  LIBDIR2="$SOPHON_HOME/driver-jars"
  if [ ! -d "$LIBDIR2" ]; then
    LIBDIR2="$SOPHON_HOME/engine/target/driver-jars"
  fi
  if [ ! -d "$LIBDIR2" ]; then
    echo "Could not find driver jars directory." 1>&2
    exit 1
  fi

  SOPHON_CLASSPATH="$LIBDIR1/*:$LIBDIR2/*"


  if [ -n "$SPARK_HOME" ]; then
    SOPHON_CLASSPATH="$SPARK_HOME/jars/*:$SOPHON_CLASSPATH"
  fi
  if [ -n "$SPARK_CONF_DIR" ]; then
    SOPHON_CLASSPATH="$SPARK_CONF_DIR:$SOPHON_CLASSPATH"
  fi
  if [ -n "$HADOOP_CONF_DIR" ]; then
    SOPHON_CLASSPATH="$HADOOP_CONF_DIR:$SOPHON_CLASSPATH"
  fi
  if [ -n "$YARN_CONF_DIR" ]; then
    SOPHON_CLASSPATH="$YARN_CONF_DIR:$SOPHON_CLASSPATH"
  fi
  if [ -n "$HYPERBASE_CONF_DIR" ]; then
    SOPHON_CLASSPATH="$HYPERBASE_CONF_DIR:$SOPHON_CLASSPATH"
    if [ -n "$HADOOP_CONF_DIR" ]; then
        rm -f $HADOOP_CONF_DIR/hbase-site.xml
        cp ${HYPERBASE_CONF_DIR}/hbase-site.xml $HADOOP_CONF_DIR
    fi
  fi

  SOPHON_CLASSPATH="$BASE_CONF_DIR:$SOPHON_CONF_DIR:$SOPHON_CLASSPATH:$NFS_DRIVER_DIR/jars/*"
  JAVA_OPTS="$JAVA_OPTS -Djava.security.auth.login.config=${BASE_CONF_DIR}/sophon_driver_jaas.conf"
  command="$RUNNER $JAVA_OPTS -cp $SOPHON_CLASSPATH:$CLASSPATH io.transwarp.sophon.server.SophonSpringServer"
  change_permission
  export HOME=/home/<USER>
  su sophon -p -c "exec $command"
}

start_sophon_server
