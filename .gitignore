# IDE
.idea
.vscode

# Mac
.DS_Store
**/.DS_Store

# Certs for local test
kubernetes
admin-key.pem
admin.pem
ca.pem
kubeconfig
certs
*.pem
*.key
*.csr
*.crt

# Local device-config
device-config.yaml
!hack/device-config.yaml

# Local build binary
bin

# Test binary, build with `go test -c` or `ginkgo build`
*.test
# Coverage test report
coverage.txt

# Output
_output/
*.out
dist
conf

vendor
# Kubernetes Generated files - skip generated files, except for vendored files
!vendor/**/zz_generated.*

# Backup files
*.bak

