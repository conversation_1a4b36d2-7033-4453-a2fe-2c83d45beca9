adduser/now 3.118ubuntu5 all [installed,local]
apt-utils/now 2.4.11 amd64 [installed,local]
apt/now 2.4.11 amd64 [installed,local]
autoconf/now 2.71-2 all [installed,local]
automake/now 1:1.16.5-1.3 all [installed,local]
autotools-dev/now 20220109.1 all [installed,local]
base-files/now 12ubuntu4.6 amd64 [installed,local]
base-passwd/now 3.5.52build1 amd64 [installed,local]
bash/now 5.1-6ubuntu1 amd64 [installed,local]
bind9-dnsutils/now 1:9.18.30-0ubuntu0.22.04.1 amd64 [installed,local]
bind9-host/now 1:9.18.30-0ubuntu0.22.04.1 amd64 [installed,local]
bind9-libs/now 1:9.18.30-0ubuntu0.22.04.1 amd64 [installed,local]
binutils-common/now 2.38-4ubuntu2.6 amd64 [installed,local]
binutils-x86-64-linux-gnu/now 2.38-4ubuntu2.6 amd64 [installed,local]
binutils/now 2.38-4ubuntu2.6 amd64 [installed,local]
bsdutils/now 1:2.37.2-4ubuntu3 amd64 [installed,local]
build-essential/now 12.9ubuntu3 amd64 [installed,local]
bzip2/now 1.0.8-5build1 amd64 [installed,local]
ca-certificates/now 20240203~22.04.1 all [installed,local]
ccache/now 4.5.1-1 amd64 [installed,local]
code-server/now 4.9.1 amd64 [installed,local]
coreutils/now 8.32-4.1ubuntu1.1 amd64 [installed,local]
cpp-11/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
cpp/now 4:11.2.0-1ubuntu1 amd64 [installed,local]
cuda-cccl-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-compat-12-4/now 550.54.15-1 amd64 [installed,local]
cuda-crt-12-4/now 12.4.131-1 amd64 [installed,local]
cuda-cudart-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-cudart-dev-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-cuobjdump-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-cupti-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-cupti-dev-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-driver-dev-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-gdb-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvcc-12-4/now 12.4.131-1 amd64 [installed,local]
cuda-nvdisasm-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvml-dev-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvprof-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvprune-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvrtc-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvrtc-dev-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvtx-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-nvvm-12-4/now 12.4.131-1 amd64 [installed,local]
cuda-profiler-api-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-sanitizer-12-4/now 12.4.127-1 amd64 [installed,local]
cuda-toolkit-12-4-config-common/now 12.4.127-1 all [installed,local]
cuda-toolkit-12-config-common/now 12.4.127-1 all [installed,local]
cuda-toolkit-config-common/now 12.4.127-1 all [installed,local]
curl/now 7.81.0-1ubuntu1.16 amd64 [installed,local]
dash/now 0.5.11+git20210903+057cd650a4ed-3build1 amd64 [installed,local]
debconf/now 1.5.79ubuntu1 all [installed,local]
debianutils/now 5.5-1ubuntu2 amd64 [installed,local]
diffutils/now 1:3.8-0ubuntu2 amd64 [installed,local]
dirmngr/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
dnsutils/now 1:9.18.30-0ubuntu0.22.04.1 all [installed,local]
dpkg-dev/now 1.21.1ubuntu2.3 all [installed,local]
dpkg/now 1.21.1ubuntu2.2 amd64 [installed,local]
e2fsprogs/now 1.46.5-2ubuntu1.1 amd64 [installed,local]
file/now 1:5.41-3ubuntu0.1 amd64 [installed,local]
findutils/now 4.8.0-1ubuntu3 amd64 [installed,local]
fonts-liberation/now 1:1.07.4-11 all [installed,local]
g++-11/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
g++/now 4:11.2.0-1ubuntu1 amd64 [installed,local]
gcc-11-base/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
gcc-11/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
gcc-12-base/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
gcc/now 4:11.2.0-1ubuntu1 amd64 [installed,local]
gdb/now 12.1-0ubuntu1~22.04.2 amd64 [installed,local]
gfortran-11/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
gfortran/now 4:11.2.0-1ubuntu1 amd64 [installed,local]
git-lfs/now 3.0.2-1ubuntu0.2 amd64 [installed,local]
git-man/now 1:2.34.1-1ubuntu1.10 all [installed,local]
git/now 1:2.34.1-1ubuntu1.10 amd64 [installed,local]
gnupg-l10n/now 2.2.27-3ubuntu2.1 all [installed,local]
gnupg-utils/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gnupg/now 2.2.27-3ubuntu2.1 all [installed,local]
gpg-agent/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gpg-wks-client/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gpg-wks-server/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gpg/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gpgconf/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gpgsm/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
gpgv/now 2.2.27-3ubuntu2.1 amd64 [installed,local]
grep/now 3.7-1build1 amd64 [installed,local]
gzip/now 1.10-4ubuntu4.1 amd64 [installed,local]
hdf5-helpers/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
hostname/now 3.23ubuntu2 amd64 [installed,local]
ibverbs-providers/now 39.0-1 amd64 [installed,local]
ibverbs-utils/now 39.0-1 amd64 [installed,local]
init-system-helpers/now 1.62 all [installed,local]
iputils-ping/now 3:20211215-1 amd64 [installed,local]
jq/now 1.6-2.1ubuntu3 amd64 [installed,local]
less/now 590-1ubuntu0.22.04.2 amd64 [installed,local]
libacl1/now 2.3.1-1 amd64 [installed,local]
libaec-dev/now 1.0.6-1 amd64 [installed,local]
libaec0/now 1.0.6-1 amd64 [installed,local]
libapt-pkg6.0/now 2.4.11 amd64 [installed,local]
libasan6/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
libasound2-data/now *******-1ubuntu1 all [installed,local]
libasound2/now *******-1ubuntu1 amd64 [installed,local]
libassuan0/now 2.5.5-1build1 amd64 [installed,local]
libatlas-base-dev/now 3.10.3-12ubuntu1 amd64 [installed,local]
libatlas3-base/now 3.10.3-12ubuntu1 amd64 [installed,local]
libatomic1/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libattr1/now 1:2.5.1-1build1 amd64 [installed,local]
libaudit-common/now 1:3.0.7-1build1 all [installed,local]
libaudit1/now 1:3.0.7-1build1 amd64 [installed,local]
libbabeltrace1/now 1.5.8-2build1 amd64 [installed,local]
libbinutils/now 2.38-4ubuntu2.6 amd64 [installed,local]
libblkid1/now 2.37.2-4ubuntu3 amd64 [installed,local]
libboost-regex1.74.0/now 1.74.0-14ubuntu3 amd64 [installed,local]
libbrotli1/now 1.0.9-2build6 amd64 [installed,local]
libbsd-dev/now 0.11.5-1 amd64 [installed,local]
libbsd0/now 0.11.5-1 amd64 [installed,local]
libbz2-1.0/now 1.0.8-5build1 amd64 [installed,local]
libbz2-dev/now 1.0.8-5build1 amd64 [installed,local]
libc-bin/now 2.35-0ubuntu3.6 amd64 [installed,local]
libc-dev-bin/now 2.35-0ubuntu3.6 amd64 [installed,local]
libc6-dev/now 2.35-0ubuntu3.6 amd64 [installed,local]
libc6/now 2.35-0ubuntu3.6 amd64 [installed,local]
libcap-ng0/now 0.7.9-2.2build3 amd64 [installed,local]
libcap2-bin/now 1:2.44-1ubuntu0.22.04.1 amd64 [installed,local]
libcap2/now 1:2.44-1ubuntu0.22.04.1 amd64 [installed,local]
libcbor0.8/now 0.8.0-2ubuntu1 amd64 [installed,local]
libcc1-0/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libcmark-gfm-extensions0.29.0.gfm.3/now 0.29.0.gfm.3-3 amd64 [installed,local]
libcmark-gfm0.29.0.gfm.3/now 0.29.0.gfm.3-3 amd64 [installed,local]
libcom-err2/now 1.46.5-2ubuntu1.1 amd64 [installed,local]
libcrypt-dev/now 1:4.4.27-1 amd64 [installed,local]
libcrypt1/now 1:4.4.27-1 amd64 [installed,local]
libctf-nobfd0/now 2.38-4ubuntu2.6 amd64 [installed,local]
libctf0/now 2.38-4ubuntu2.6 amd64 [installed,local]
libcublas-12-4/now 12.4.5.8-1 amd64 [installed,local]
libcublas-dev-12-4/now 12.4.5.8-1 amd64 [installed,local]
libcudnn9-cuda-12/now 9.1.0.70-1 amd64 [installed,local]
libcudnn9-dev-cuda-12/now 9.1.0.70-1 amd64 [installed,local]
libcufft-12-4/now 11.2.1.3-1 amd64 [installed,local]
libcufft-dev-12-4/now 11.2.1.3-1 amd64 [installed,local]
libcufile-12-4/now 1.9.1.3-1 amd64 [installed,local]
libcufile-dev-12-4/now 1.9.1.3-1 amd64 [installed,local]
libcurand-12-4/now 10.3.5.147-1 amd64 [installed,local]
libcurand-dev-12-4/now 10.3.5.147-1 amd64 [installed,local]
libcurl3-gnutls/now 7.81.0-1ubuntu1.16 amd64 [installed,local]
libcurl4-openssl-dev/now 7.81.0-1ubuntu1.16 amd64 [installed,local]
libcurl4/now 7.81.0-1ubuntu1.16 amd64 [installed,local]
libcusolver-12-4/now 11.6.1.9-1 amd64 [installed,local]
libcusolver-dev-12-4/now 11.6.1.9-1 amd64 [installed,local]
libcusparse-12-4/now 12.3.1.170-1 amd64 [installed,local]
libcusparse-dev-12-4/now 12.3.1.170-1 amd64 [installed,local]
libcutensor-dev/now 2.0.1.2-1 amd64 [installed,local]
libcutensor2/now 2.0.1.2-1 amd64 [installed,local]
libdb5.3/now 5.3.28+dfsg1-0.8ubuntu3 amd64 [installed,local]
libdebconfclient0/now 0.261ubuntu1 amd64 [installed,local]
libdebuginfod-common/now 0.186-1build1 all [installed,local]
libdebuginfod1/now 0.186-1build1 amd64 [installed,local]
libdpkg-perl/now 1.21.1ubuntu2.3 all [installed,local]
libdrm-amdgpu1/now 2.4.113-2~ubuntu0.22.04.1 amd64 [installed,local]
libdrm-common/now 2.4.113-2~ubuntu0.22.04.1 all [installed,local]
libdrm-intel1/now 2.4.113-2~ubuntu0.22.04.1 amd64 [installed,local]
libdrm-nouveau2/now 2.4.113-2~ubuntu0.22.04.1 amd64 [installed,local]
libdrm-radeon1/now 2.4.113-2~ubuntu0.22.04.1 amd64 [installed,local]
libdrm2/now 2.4.113-2~ubuntu0.22.04.1 amd64 [installed,local]
libdw1/now 0.186-1build1 amd64 [installed,local]
libedit-dev/now 3.1-20210910-1build1 amd64 [installed,local]
libedit2/now 3.1-20210910-1build1 amd64 [installed,local]
libelf1/now 0.186-1build1 amd64 [installed,local]
liberror-perl/now 0.17029-1 all [installed,local]
libexpat1-dev/now 2.4.7-1ubuntu0.3 amd64 [installed,local]
libexpat1/now 2.4.7-1ubuntu0.3 amd64 [installed,local]
libext2fs2/now 1.46.5-2ubuntu1.1 amd64 [installed,local]
libffi-dev/now 3.4.2-4 amd64 [installed,local]
libffi8/now 3.4.2-4 amd64 [installed,local]
libfido2-1/now 1.10.0-1 amd64 [installed,local]
libflac8/now 1.3.3-2ubuntu0.2 amd64 [installed,local]
libgcc-11-dev/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
libgcc-s1/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libgcrypt20/now 1.9.4-3ubuntu3 amd64 [installed,local]
libgdbm-compat4/now 1.23-1 amd64 [installed,local]
libgdbm-dev/now 1.23-1 amd64 [installed,local]
libgdbm6/now 1.23-1 amd64 [installed,local]
libgdrapi/now 2.3.1 amd64 [installed,local]
libgflags-dev/now 2.2.2-2 amd64 [installed,local]
libgflags2.2/now 2.2.2-2 amd64 [installed,local]
libgfortran-11-dev/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
libgfortran5/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libgl1-mesa-dri/now 23.2.1-1ubuntu3.1~22.04.2 amd64 [installed,local]
libgl1-mesa-glx/now 23.0.4-0ubuntu1~22.04.1 amd64 [installed,local]
libgl1/now 1.4.0-1 amd64 [installed,local]
libglapi-mesa/now 23.2.1-1ubuntu3.1~22.04.2 amd64 [installed,local]
libglib2.0-0/now 2.72.4-0ubuntu2.2 amd64 [installed,local]
libglvnd0/now 1.4.0-1 amd64 [installed,local]
libglx-mesa0/now 23.2.1-1ubuntu3.1~22.04.2 amd64 [installed,local]
libglx0/now 1.4.0-1 amd64 [installed,local]
libgmp10/now 2:6.2.1+dfsg-3ubuntu1 amd64 [installed,local]
libgnutls30/now 3.7.3-4ubuntu1.4 amd64 [installed,local]
libgomp1/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libgoogle-glog-dev/now 0.5.0+really0.4.0-2 amd64 [installed,local]
libgoogle-glog0v5/now 0.5.0+really0.4.0-2 amd64 [installed,local]
libgpg-error0/now 1.43-3 amd64 [installed,local]
libgpm2/now 1.20.7-10build1 amd64 [installed,local]
libgsm1/now 1.0.19-1 amd64 [installed,local]
libgssapi-krb5-2/now 1.19.2-2ubuntu0.3 amd64 [installed,local]
libhdf5-103-1/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-103/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-cpp-103-1/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-dev/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-fortran-102/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-hl-100/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-hl-cpp-100/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhdf5-hl-fortran-100/now 1.10.7+repack-4ubuntu2 amd64 [installed,local]
libhiredis0.14/now 0.14.1-2 amd64 [installed,local]
libhogweed6/now 3.7.3-1build2 amd64 [installed,local]
libibumad-dev/now 39.0-1 amd64 [installed,local]
libibumad3/now 39.0-1 amd64 [installed,local]
libibverbs-dev/now 39.0-1 amd64 [installed,local]
libibverbs1/now 39.0-1 amd64 [installed,local]
libice6/now 2:1.0.10-1build2 amd64 [installed,local]
libicu70/now 70.1-2 amd64 [installed,local]
libidn2-0/now 2.3.2-2build1 amd64 [installed,local]
libipt2/now 2.0.5-1 amd64 [installed,local]
libisl23/now 0.24-2build1 amd64 [installed,local]
libitm1/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libjpeg-dev/now 8c-2ubuntu10 amd64 [installed,local]
libjpeg-turbo8-dev/now 2.1.2-0ubuntu1 amd64 [installed,local]
libjpeg-turbo8/now 2.1.2-0ubuntu1 amd64 [installed,local]
libjpeg8-dev/now 8c-2ubuntu10 amd64 [installed,local]
libjpeg8/now 8c-2ubuntu10 amd64 [installed,local]
libjq1/now 1.6-2.1ubuntu3 amd64 [installed,local]
libjs-jquery/now 3.6.0+dfsg+~3.5.13-1 all [installed,local]
libjs-sphinxdoc/now 4.3.2-1 all [installed,local]
libjs-underscore/now 1.13.2~dfsg-2 all [installed,local]
libjson-c5/now 0.15-3~ubuntu********* amd64 [installed,local]
libk5crypto3/now 1.19.2-2ubuntu0.3 amd64 [installed,local]
libkeyutils1/now 1.6.1-2ubuntu3 amd64 [installed,local]
libkrb5-3/now 1.19.2-2ubuntu0.3 amd64 [installed,local]
libkrb5support0/now 1.19.2-2ubuntu0.3 amd64 [installed,local]
libksba8/now 1.6.0-2ubuntu0.2 amd64 [installed,local]
libldap-2.5-0/now 2.5.17+dfsg-0ubuntu0.22.04.1 amd64 [installed,local]
libleveldb-dev/now 1.23-3build1 amd64 [installed,local]
libleveldb1d/now 1.23-3build1 amd64 [installed,local]
libllvm15/now 1:15.0.7-0ubuntu0.22.04.3 amd64 [installed,local]
liblmdb-dev/now 0.9.24-1build2 amd64 [installed,local]
liblmdb0/now 0.9.24-1build2 amd64 [installed,local]
liblsan0/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libltdl7/now 2.4.6-15build2 amd64 [installed,local]
liblz4-1/now 1.9.3-2build2 amd64 [installed,local]
liblzma-dev/now 5.2.5-2ubuntu1 amd64 [installed,local]
liblzma5/now 5.2.5-2ubuntu1 amd64 [installed,local]
libmagic-mgc/now 1:5.41-3ubuntu0.1 amd64 [installed,local]
libmagic1/now 1:5.41-3ubuntu0.1 amd64 [installed,local]
libmaxminddb0/now 1.5.2-1build2 amd64 [installed,local]
libmd-dev/now 1.0.4-1build1 amd64 [installed,local]
libmd0/now 1.0.4-1build1 amd64 [installed,local]
libmount1/now 2.37.2-4ubuntu3 amd64 [installed,local]
libmpc3/now 1.2.1-2build1 amd64 [installed,local]
libmpdec3/now 2.5.1-2build2 amd64 [installed,local]
libmpfr6/now 4.1.0-3build3 amd64 [installed,local]
libnccl-dev/now 2.21.5-1+cuda12.4 amd64 [installed,local]
libnccl2/now 2.21.5-1+cuda12.4 amd64 [installed,local]
libncurses-dev/now 6.3-2ubuntu0.1 amd64 [installed,local]
libncurses5-dev/now 6.3-2ubuntu0.1 amd64 [installed,local]
libncurses5/now 6.3-2ubuntu0.1 amd64 [installed,local]
libncurses6/now 6.3-2ubuntu0.1 amd64 [installed,local]
libncursesw5/now 6.3-2ubuntu0.1 amd64 [installed,local]
libncursesw6/now 6.3-2ubuntu0.1 amd64 [installed,local]
libnettle8/now 3.7.3-1build2 amd64 [installed,local]
libnghttp2-14/now 1.43.0-1ubuntu0.1 amd64 [installed,local]
libnl-3-200/now 3.5.0-0.1 amd64 [installed,local]
libnl-3-dev/now 3.5.0-0.1 amd64 [installed,local]
libnl-route-3-200/now 3.5.0-0.1 amd64 [installed,local]
libnl-route-3-dev/now 3.5.0-0.1 amd64 [installed,local]
libnpp-12-4/now 12.2.5.30-1 amd64 [installed,local]
libnpp-dev-12-4/now 12.2.5.30-1 amd64 [installed,local]
libnpth0/now 1.6-3build2 amd64 [installed,local]
libnsl-dev/now 1.3.0-2build2 amd64 [installed,local]
libnsl2/now 1.3.0-2build2 amd64 [installed,local]
libnspr4-dev/now 2:4.35-0ubuntu0.22.04.1 amd64 [installed,local]
libnspr4/now 2:4.35-0ubuntu0.22.04.1 amd64 [installed,local]
libnss3-dev/now 2:3.98-0ubuntu0.22.04.2 amd64 [installed,local]
libnss3/now 2:3.98-0ubuntu0.22.04.2 amd64 [installed,local]
libnuma-dev/now 2.0.14-3ubuntu2 amd64 [installed,local]
libnuma1/now 2.0.14-3ubuntu2 amd64 [installed,local]
libnvjitlink-12-4/now 12.4.127-1 amd64 [installed,local]
libnvjitlink-dev-12-4/now 12.4.127-1 amd64 [installed,local]
libnvjpeg-12-4/now 12.3.1.117-1 amd64 [installed,local]
libnvjpeg-dev-12-4/now 12.3.1.117-1 amd64 [installed,local]
libogg0/now 1.3.5-0ubuntu3 amd64 [installed,local]
libonig5/now 6.9.7.1-2build1 amd64 [installed,local]
libopencore-amrnb0/now 0.1.5-1 amd64 [installed,local]
libopencore-amrwb0/now 0.1.5-1 amd64 [installed,local]
libopus0/now 1.3.1-0.1build2 amd64 [installed,local]
libp11-kit0/now 0.24.0-6build1 amd64 [installed,local]
libpam-modules-bin/now 1.4.0-11ubuntu2.4 amd64 [installed,local]
libpam-modules/now 1.4.0-11ubuntu2.4 amd64 [installed,local]
libpam-runtime/now 1.4.0-11ubuntu2.4 all [installed,local]
libpam0g/now 1.4.0-11ubuntu2.4 amd64 [installed,local]
libpciaccess0/now 0.16-3 amd64 [installed,local]
libpcre2-8-0/now 10.39-3ubuntu0.1 amd64 [installed,local]
libpcre3/now 2:8.39-13ubuntu0.22.04.1 amd64 [installed,local]
libperl5.34/now 5.34.0-3ubuntu1.3 amd64 [installed,local]
libpmi2-0-dev/now 21.08.5-2ubuntu1 amd64 [installed,local]
libpmi2-0/now 21.08.5-2ubuntu1 amd64 [installed,local]
libpng-dev/now 1.6.37-3build5 amd64 [installed,local]
libpng16-16/now 1.6.37-3build5 amd64 [installed,local]
libpopt0/now 1.18-3build1 amd64 [installed,local]
libprocps8/now 2:3.3.17-6ubuntu2.1 amd64 [installed,local]
libprotobuf-dev/now 3.12.4-1ubuntu7.22.04.1 amd64 [installed,local]
libprotobuf-lite23/now 3.12.4-1ubuntu7.22.04.1 amd64 [installed,local]
libprotobuf23/now 3.12.4-1ubuntu7.22.04.1 amd64 [installed,local]
libprotoc23/now 3.12.4-1ubuntu7.22.04.1 amd64 [installed,local]
libpsl5/now 0.21.0-1.2build2 amd64 [installed,local]
libpython3-dev/now 3.10.6-1~22.04 amd64 [installed,local]
libpython3-stdlib/now 3.10.6-1~22.04 amd64 [installed,local]
libpython3.10-dev/now 3.10.12-1~22.04.3 amd64 [installed,local]
libpython3.10-minimal/now 3.10.12-1~22.04.3 amd64 [installed,local]
libpython3.10-stdlib/now 3.10.12-1~22.04.3 amd64 [installed,local]
libpython3.10/now 3.10.12-1~22.04.3 amd64 [installed,local]
libquadmath0/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
librdmacm-dev/now 39.0-1 amd64 [installed,local]
librdmacm1/now 39.0-1 amd64 [installed,local]
libreadline-dev/now 8.1.2-1 amd64 [installed,local]
libreadline8/now 8.1.2-1 amd64 [installed,local]
librtmp1/now 2.4+20151223.gitfa8646d.1-2build4 amd64 [installed,local]
libsasl2-2/now 2.1.27+dfsg2-3ubuntu1.2 amd64 [installed,local]
libsasl2-modules-db/now 2.1.27+dfsg2-3ubuntu1.2 amd64 [installed,local]
libseccomp2/now 2.5.3-2ubuntu2 amd64 [installed,local]
libselinux1/now 3.3-1build2 amd64 [installed,local]
libsemanage-common/now 3.3-1build2 all [installed,local]
libsemanage2/now 3.3-1build2 amd64 [installed,local]
libsensors-config/now 1:3.6.0-7ubuntu1 all [installed,local]
libsensors5/now 1:3.6.0-7ubuntu1 amd64 [installed,local]
libsepol2/now 3.3-1build1 amd64 [installed,local]
libsigsegv2/now 2.13-1ubuntu3 amd64 [installed,local]
libslurm37/now 21.08.5-2ubuntu1 amd64 [installed,local]
libsm6/now 2:1.2.3-1build2 amd64 [installed,local]
libsmartcols1/now 2.37.2-4ubuntu3 amd64 [installed,local]
libsnappy-dev/now 1.1.8-1build3 amd64 [installed,local]
libsnappy1v5/now 1.1.8-1build3 amd64 [installed,local]
libsndfile1/now 1.0.31-2ubuntu0.1 amd64 [installed,local]
libsodium23/now 1.0.18-1build2 amd64 [installed,local]
libsource-highlight-common/now 3.1.9-4.1build2 all [installed,local]
libsource-highlight4v5/now 3.1.9-4.1build2 amd64 [installed,local]
libsox-fmt-alsa/now 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 amd64 [installed,local]
libsox-fmt-base/now 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 amd64 [installed,local]
libsox3/now 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 amd64 [installed,local]
libsqlite3-0/now 3.37.2-2ubuntu0.3 amd64 [installed,local]
libsqlite3-dev/now 3.37.2-2ubuntu0.3 amd64 [installed,local]
libss2/now 1.46.5-2ubuntu1.1 amd64 [installed,local]
libssh-4/now 0.9.6-2ubuntu0.22.04.3 amd64 [installed,local]
libssl-dev/now 3.0.2-0ubuntu1.15 amd64 [installed,local]
libssl3/now 3.0.2-0ubuntu1.15 amd64 [installed,local]
libstdc++-11-dev/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
libstdc++6/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libsystemd0/now 249.11-0ubuntu3.12 amd64 [installed,local]
libsz2/now 1.0.6-1 amd64 [installed,local]
libtasn1-6/now 4.18.0-4build1 amd64 [installed,local]
libtcmalloc-minimal4/now 2.9.1-0ubuntu3 amd64 [installed,local]
libtinfo5/now 6.3-2ubuntu0.1 amd64 [installed,local]
libtinfo6/now 6.3-2ubuntu0.1 amd64 [installed,local]
libtirpc-common/now 1.3.2-2ubuntu0.1 all [installed,local]
libtirpc-dev/now 1.3.2-2ubuntu0.1 amd64 [installed,local]
libtirpc3/now 1.3.2-2ubuntu0.1 amd64 [installed,local]
libtool/now 2.4.6-15build2 all [installed,local]
libtsan0/now 11.4.0-1ubuntu1~22.04 amd64 [installed,local]
libubsan1/now 12.3.0-1ubuntu1~22.04 amd64 [installed,local]
libudev1/now 249.11-0ubuntu3.12 amd64 [installed,local]
libunistring2/now 1.0-1 amd64 [installed,local]
libunwind-dev/now 1.3.2-2build2.1 amd64 [installed,local]
libunwind8/now 1.3.2-2build2.1 amd64 [installed,local]
libuuid1/now 2.37.2-4ubuntu3 amd64 [installed,local]
libuv1/now 1.43.0-1ubuntu0.1 amd64 [installed,local]
libvorbis0a/now 1.3.7-1build2 amd64 [installed,local]
libvorbisenc2/now 1.3.7-1build2 amd64 [installed,local]
libvorbisfile3/now 1.3.7-1build2 amd64 [installed,local]
libwavpack1/now 5.4.0-1build2 amd64 [installed,local]
libx11-6/now 2:1.7.5-1ubuntu0.3 amd64 [installed,local]
libx11-data/now 2:1.7.5-1ubuntu0.3 all [installed,local]
libx11-xcb1/now 2:1.7.5-1ubuntu0.3 amd64 [installed,local]
libxau6/now 1:1.0.9-1build5 amd64 [installed,local]
libxcb-dri2-0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-dri3-0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-glx0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-present0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-randr0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-shm0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-sync1/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb-xfixes0/now 1.14-3ubuntu3 amd64 [installed,local]
libxcb1/now 1.14-3ubuntu3 amd64 [installed,local]
libxdmcp6/now 1:1.1.3-0ubuntu5 amd64 [installed,local]
libxext6/now 2:1.3.4-1build1 amd64 [installed,local]
libxfixes3/now 1:6.0.0-1 amd64 [installed,local]
libxml2/now 2.9.13+dfsg-1ubuntu0.4 amd64 [installed,local]
libxrender1/now 1:0.9.10-1build4 amd64 [installed,local]
libxshmfence1/now 1.3-1build4 amd64 [installed,local]
libxxf86vm1/now 1:1.1.4-1build3 amd64 [installed,local]
libxxhash0/now 0.8.1-1 amd64 [installed,local]
libzstd1/now 1.4.8+dfsg-3build1 amd64 [installed,local]
linux-libc-dev/now 5.15.0-100.110 amd64 [installed,local]
locales/now 2.35-0ubuntu3.8 all [installed,local]
login/now 1:4.8.1-2ubuntu2.2 amd64 [installed,local]
logsave/now 1.46.5-2ubuntu1.1 amd64 [installed,local]
lsb-base/now 11.1.0ubuntu4 all [installed,local]
lto-disabled-list/now 24 all [installed,local]
m4/now 1.4.18-5ubuntu2 amd64 [installed,local]
make/now 4.3-4.1build1 amd64 [installed,local]
mawk/now 1.3.4.20200120-3 amd64 [installed,local]
media-types/now 7.0.0 all [installed,local]
mount/now 2.37.2-4ubuntu3 amd64 [installed,local]
nano/now 6.2-1 amd64 [installed,local]
nasm/now 2.15.05-1 amd64 [installed,local]
ncurses-base/now 6.3-2ubuntu0.1 all [installed,local]
ncurses-bin/now 6.3-2ubuntu0.1 amd64 [installed,local]
ninja-build/now 1.10.1-1 amd64 [installed,local]
nsight-compute-2024.1.1/now 2024.1.1.4-1 amd64 [installed,local]
nsight-systems-cli-2024.2.1/now 2024.2.1.106-242134037904v0 amd64 [installed,local]
numactl/now 2.0.14-3ubuntu2 amd64 [installed,local]
openssh-client/now 1:8.9p1-3ubuntu0.6 amd64 [installed,local]
openssl/now 3.0.2-0ubuntu1.15 amd64 [installed,local]
pandoc-data/now 2.9.2.1-3ubuntu2 all [installed,local]
pandoc/now 2.9.2.1-3ubuntu2 amd64 [installed,local]
passwd/now 1:4.8.1-2ubuntu2.2 amd64 [installed,local]
patch/now 2.7.6-7build2 amd64 [installed,local]
patchelf/now 0.14.3-1 amd64 [installed,local]
perl-base/now 5.34.0-3ubuntu1.3 amd64 [installed,local]
perl-modules-5.34/now 5.34.0-3ubuntu1.3 all [installed,local]
perl/now 5.34.0-3ubuntu1.3 amd64 [installed,local]
pigz/now 2.6-1 amd64 [installed,local]
pinentry-curses/now 1.1.1-1build2 amd64 [installed,local]
pkg-config/now 0.29.2-1ubuntu3 amd64 [installed,local]
procps/now 2:3.3.17-6ubuntu2.1 amd64 [installed,local]
protobuf-compiler/now 3.12.4-1ubuntu7.22.04.1 amd64 [installed,local]
python-is-python3/now 3.9.2-2 all [installed,local]
python3-dev/now 3.10.6-1~22.04 amd64 [installed,local]
python3-distutils/now 3.10.8-1~22.04 all [installed,local]
python3-lib2to3/now 3.10.8-1~22.04 all [installed,local]
python3-minimal/now 3.10.6-1~22.04 amd64 [installed,local]
python3-pip/now 22.0.2+dfsg-1ubuntu0.4 all [installed,local]
python3-pkg-resources/now 59.6.0-1.2ubuntu0.22.04.1 all [installed,local]
python3-setuptools/now 59.6.0-1.2ubuntu0.22.04.1 all [installed,local]
python3-wheel/now 0.37.1-2ubuntu0.22.04.1 all [installed,local]
python3.10-dev/now 3.10.12-1~22.04.3 amd64 [installed,local]
python3.10-minimal/now 3.10.12-1~22.04.3 amd64 [installed,local]
python3.10/now 3.10.12-1~22.04.3 amd64 [installed,local]
python3/now 3.10.6-1~22.04 amd64 [installed,local]
rapidjson-dev/now 1.1.0+dfsg2-7 all [installed,local]
readline-common/now 8.1.2-1 all [installed,local]
rpcsvc-proto/now 1.4.2-0ubuntu6 amd64 [installed,local]
rsync/now 3.2.7-0ubuntu0.22.04.2 amd64 [installed,local]
sed/now 4.8-1ubuntu2 amd64 [installed,local]
sensible-utils/now 0.0.17 all [installed,local]
sox/now 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 amd64 [installed,local]
sudo/now 1.9.9-1ubuntu2.4 amd64 [installed,local]
sysvinit-utils/now 3.01-1ubuntu1 amd64 [installed,local]
tar/now 1.34+dfsg-1ubuntu0.********* amd64 [installed,local]
ubuntu-keyring/now 2021.03.26 all [installed,local]
ucf/now 3.0043 all [installed,local]
unzip/now 6.0-26ubuntu3.2 amd64 [installed,local]
usrmerge/now 25ubuntu2 all [installed,local]
util-linux/now 2.37.2-4ubuntu3 amd64 [installed,local]
vim-common/now 2:8.2.3995-1ubuntu2.16 all [installed,local]
vim-runtime/now 2:8.2.3995-1ubuntu2.16 all [installed,local]
vim/now 2:8.2.3995-1ubuntu2.16 amd64 [installed,local]
wget/now 1.21.2-2ubuntu1.1 amd64 [installed,local]
x11-common/now 1:7.7+23ubuntu2 all [installed,local]
xxd/now 2:8.2.3995-1ubuntu2.16 amd64 [installed,local]
xz-utils/now 5.2.5-2ubuntu1 amd64 [installed,local]
zlib1g-dev/now 1:1.2.11.dfsg-2ubuntu9.2 amd64 [installed,local]
zlib1g/now 1:1.2.11.dfsg-2ubuntu9.2 amd64 [installed,local]