# 基础配置
edge_id: "csm-backend"
edge_host: 127.0.0.1
disable_auth: false
database: mysql
token: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
namespace: dev
storage_root: "/tmp/nfs"
#registry: ***********
registry: localhost:31500
registry_port: 31500
registry_api_addr: *************:31500
repo_prefix: "aip"
registry_tokens: ""
registry_timeout: "10s"
registry_catalog_limit: 20000
service_account: llmops-admin
build_path: /data/nfs/csm/build
http_timeout: 5s
use_other_registry: false
scheduler_name: default-scheduler
data_juicer_image: ***********/aip/data-juicer:dev
registry_path: /registry

server:
  addr: ":30080"

proxy:
  addr: ":30081"
  jupyter_prefix: "/jupyter"

# 公共组件配置, 包含以下组件
# - Mysql     仅云端
# - License
license:
  verifier_path: "/usr/local/bin/verifier"
  licensor_addr: "http://autocv-licensor-service:80"
  check_interval: "5s"

thinger_log:
  # 日志所在默认目录
  path: "/opt/vision/node/.data/store/logs"
  # 后端服务的日志配置
  logger:
    # 日志输出级别
    level: debug
    # 日志是否输出到标准输出
    console: true

mysql:
  username: root
  password: Warp!CV@2022#
  host: **************
  port: 31907
  db_name: csm_metastore

redis:
  database: 0
  # 如果有多个addr，逗号分割
  addrs: **************:30171
  password: Warp!CV@2022#
  timeout: 5000

influxdb:
  #  url: "http://127.0.0.1:30886"
  url: "http://**************:32660"
  database: "thinger"
  precision: "ns"
  retention_policy: ""
  write_consistency: ""
  timeout: "30s"
  batch_size: 1000

builder:
  endpoint: autocv-csm-builder-service

resource_config:
  endpoint: resource-service:8718

cas_config:
  endpoint: autocv-cas-service:80

engine:
  in_cluster: false
  kubeconfig_path: etc/dev-207.yaml

pvc:
  name: autocv-sfs-pvc

grpc_config:
  max_message_mb: 1024 # grpc服务能够接收发送的message大小 单位MB
  server_host: "localhost"
  server_port: ":31080"
