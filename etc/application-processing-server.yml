processing:
  image:
    cpu: ***********/aip/data-juicer:dev
    gpu: ***********/aip/data-juicer:dev
  process:
    start: sleep 1000000000
    global:
      res_config:
        cpu_config:
          cpu_limit: 4
          cpu_request: 1
        gpu_enable: false
        memory_config:
          memory_limit: 8
          memory_request: 4
          unit: GiB
        resource_config_mode: Single
        schedule_mode: Random
        distributed_mode_config:
          node_num: 3
    data_cleaning:
      res_config:
        cpu_config:
          cpu_limit: 8
          cpu_request: 4
        gpu_enable: false
        memory_config:
          memory_limit: 16
          memory_request: 8
          unit: GiB
        resource_config_mode: Single
        schedule_mode: Random
        distributed_mode_config:
          node_num: 3
    doc_parsing:
      res_config:
        cpu_config:
          cpu_limit: 1
          cpu_request: 1
        gpu_enable: false
        memory_config:
          memory_limit: 1
          memory_request: 1
          unit: GiB
        resource_config_mode: Single
        schedule_mode: Random
        distributed_mode_config:
          node_num: 3
  debug:
    memory_limit: 0
    memory_request: 0
    memory_unit: GiB
    cpu_limit: 0
    cpu_request: 0
  debug_selector: