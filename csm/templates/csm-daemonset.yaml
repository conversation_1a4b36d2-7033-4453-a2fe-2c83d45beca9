{{- if .Values.global.csm.enable -}}
# 用于将算法基础镜像提前下发到各个节点，避免镜像不存在的情况，节省首次调度计算任务的准备时间
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    {{- include "charts.labels" . | nindent 4 }}
    csm.builder.component: {{ include "charts.servername" . }}-builder
    io.transwarp.aip.service: {{ include "charts.servername" . }}-builder
  name: {{ include "charts.servername" . }}-builder-ds
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      io.transwarp.aip.service: {{ include "charts.servername" . }}-builder
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        io.transwarp.aip.service: {{ include "charts.servername" . }}-builder
    spec:
      terminationGracePeriodSeconds: 3
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        # builder镜像
        - name: builder
          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.csmIB }}
          imagePullPolicy: {{ .Values.global.images.pullPolicy }}
          command:
            - /bin/boot.sh
          env:
            - name: SOPHON_SERVICE_ID
              value: {{ include "charts.fullname" . }}
            - name: IP_ADDR
              value: {{ include "charts.servername" . }}-service
            {{ if .Values.global.auth.disable }}
            - name: CSM_DISABLE_AUTH
              value: "true"
            {{ end }}
            - name: IB_EDGE_ID
              value: csm-image-builder
            - name: IB_DATABASE
              value: {{ .Values.csm.database }}
            # 挂载oss的根目录
            - name: IB_STORAGE_ROOT
              value: /sfs
            # csm数据的bucket
            - name: IB_BASE_LOCATION
              value: {{ .Values.csm.baseLocation }}
            - name: IB_SERVER.ADDR
              value: ":80"
            - name: IB_BUILD_PATH
              value: /sfs/csm/build
            - name: IB_REGISTRY
              value: "{{ .Values.csm.registry }}"
            - name: IB_REGISTRY_TOKEN
              value: ""
            - name: IB_CLEAN_ENABLED
              value: "{{ .Values.csm.cleanEnabled }}"
            - name: IB_LICENSE.VERIFIER_PATH
              value: /share/verifier
            - name: IB_LICENSE.LICENSOR_ADDR
              value: http://autocv-licensor-service
            - name: IB_LICENSE.CHECK_INTERVAL
              value: 300s
            - name: IB_ENGINE.DOCKER_API_VERSION
              value: "1.32"
          ports:
            - name: csm-builder
              containerPort: 80
          resources:
          {{- toYaml .Values.resources | nindent 12 }}
          securityContext:
            privileged: true
          volumeMounts:
            - name: sfs-volume
              mountPath: /opt/vision/node/.data/store/logs
              subPath: csm/log
            - name: sfs-volume
              mountPath: /share
              subPath: licensor/share
            - name: sfs-volume
              mountPath: /sfs
            # 用于进行部分Docker命令的执行，和Docker Server进行通信
            # 通过API方式需要Docker启动时指定远程访问地址,因此暂时通过默认的 unix socket进行通信
            - mountPath: /var/run/docker.sock
              name: docker-sock
            - mountPath: /certs
              name: docker-cert
            # 挂载registry的目录，获取ip配置
            - name: sfs-volume
              mountPath: /registry
              subPath: registry
{{/*        # 默认微调镜像*/}}
{{/*        - name: finetune*/}}
{{/*          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.csmFinetune }}*/}}
{{/*          imagePullPolicy: {{ .Values.global.images.pullPolicy }}*/}}
{{/*          args:*/}}
{{/*            - sleep*/}}
{{/*            -  infinity*/}}
{{/*        # 默认python镜像*/}}
{{/*        - name: python*/}}
{{/*          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.csmPython }}*/}}
{{/*          imagePullPolicy: {{ .Values.global.images.pullPolicy }}*/}}
{{/*          args:*/}}
{{/*            - sleep*/}}
{{/*            -  infinity*/}}
{{/*        # data juicer*/}}
{{/*        - name: data-juicer*/}}
{{/*          image: {{ include "image-tpl" . | replace "@" .Values.global.images.repo.csmDataJuicer }}*/}}
{{/*          imagePullPolicy: {{ .Values.global.images.pullPolicy }}*/}}
{{/*          args:*/}}
{{/*            - sleep*/}}
{{/*            -  infinity*/}}
      restartPolicy: Always
      volumes:
        - name: sfs-volume
          persistentVolumeClaim:
            claimName: {{ .Values.global.storage.sfs.pvcName }}
        - name: docker-sock
          hostPath:
            path: /var/run/docker.sock
        - name: docker-cert
          hostPath:
            path: /etc/docker/certs.d
{{- end -}}