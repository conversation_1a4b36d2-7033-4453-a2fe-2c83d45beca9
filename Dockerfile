FROM harbor.transwarp.io/aip/base/golang-builder:1.23-ubuntu24.04 AS gobuilder

ARG TARGETARCH

ARG PACKAGE=transwarp.io/mlops/fluent-bit-clickhouse
ARG BUILD_VERSION=0.0.0
ARG BUILD_TIME=99999999
ARG CI_COMMIT_SHA=00000000
ARG ARCH=${TARGETARCH}

WORKDIR /build

COPY log-collection  log-collection
COPY mlops-std mlops-std
COPY log-collection/${ARCH}-confd /usr/bin/confd

RUN export PATH=$PATH && cd log-collection &&\
    go env -w GOPROXY=http://*************:1111,https://goproxy.io/,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,direct && go mod tidy &&\
        GO111MODULE=on \
        GOOS=linux \
        CGO_ENABLED=1 \
        GOARCH=${ARCH} \
        go build -buildmode=c-shared \
        -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
        -o dist/mlops.so ./main.go
FROM  ***********/aip/sophon-fluent-bit-clickhouse:merge

COPY --from=gobuilder /build/log-collection/dist/mlops.so /fluent-bit/
COPY --from=gobuilder /build/log-collection/debug/bin/ /build/log-collection/debug/busybox /bin/
COPY --from=gobuilder /usr/bin/confd /usr/bin/confd
CMD ["/fluent-bit/bin/fluent-bit", "-c", "/fluent-bit/etc/fluent-bit.conf", "-e", "/fluent-bit/mlops.so"]