from seldon_core.proto import prediction_pb2, prediction_pb2_grpc
from seldon_core.utils import seldon_message_to_json
import grpc
import logging
from google.protobuf import json_format

logger = logging.getLogger(__name__)
logger.level = logging.DEBUG
grpc_max_send_message_length: int = 4 * 1024 * 1024
grpc_max_receive_message_length: int = 4 * 1024 * 1024


def _grpc(predictor_host: str, data: dict, namespace="", deployment_name=""):
    import time
    import os
    start_time = time.time()
    batch_size = int(os.environ.get("BATCH_SIZE", "1000"))
    print(batch_size)
    options = [
        ("grpc.max_send_message_length", grpc_max_send_message_length),
        ("grpc.max_receive_message_length", grpc_max_receive_message_length),
    ]
    channel = grpc.insecure_channel(predictor_host, options)
    stub = prediction_pb2_grpc.SeldonStub(channel)
    b = prediction_pb2.ModelPredictMessage()
    json_format.ParseDict(data, b)
    try:
        if deployment_name != "":
            metadata = [("seldon", deployment_name), ("namespace", namespace)]
            response = stub.PredictV2(request=b, metadata=metadata)
        else:
            response = stub.PredictV2(request=b)
        response = seldon_message_to_json(response)
    except Exception as e:
        logger.error(e)
        raise e
    end_time = time.time()
    logger.info("grpc end, spend time:" + str(end_time - start_time))
    channel.close()
    return response


a = {
    "inputs": [{
        "name": "data",
        "data": [[
            1,2,3,4
        ]],
        "datatype": "float",
        "shape": [28, 28],
        "parameters": {
            "feature_names": "null"
        }
    }]
}

response = _grpc(predictor_host="localhost:5000",
                 data=a, namespace="", deployment_name="")
print(response)
