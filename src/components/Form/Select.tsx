import React from 'react';
import AntdSelect from 'antd/es/select';
import 'antd/es/select/style';
import Icon from 'antd/es/icon';
import 'antd/es/icon/style';
import { SelectProps as AntdSelectProps } from 'antd/es/select';

const Option = AntdSelect.Option;

export interface IconConfig {
  type: string;
  title?: string;
  style?: object;
}

export interface Option {
  label: string;
  value: string;
  icon?: IconConfig;
  disabled?: boolean;
}

export interface SelectProps {
  options: Option[];
}

class Select extends React.Component<SelectProps & AntdSelectProps> {
  filterOption = (input: string, option: any) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;

  render() {
    return (
      <AntdSelect {...this.props} filterOption={this.filterOption}>
        {this.props.options.map(option => (
          <Option key={option.value} value={option.value} disabled={option.disabled} title={option.value}>
            {option.label} {option.icon ? <Icon {...option.icon} /> : ''}
          </Option>
        ))}
      </AntdSelect>
    );
  }
}

export default Select;
