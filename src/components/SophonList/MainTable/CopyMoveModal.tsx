import * as React from 'react';
import { useState, useContext, useEffect, useMemo, createRef, forwardRef, useImperativeHandle} from 'react';
import Modal from 'antd/es/modal';
import 'antd/es/modal/style';
import Tabs from 'antd/es/tabs';
import 'antd/es/tabs/style';
import Menu  from 'antd/es/menu';
import 'antd/es/menu/style';
import Button from 'antd/es/button';
import 'antd/es/button/style';
import Breadcrumb from 'antd/es/breadcrumb';
import 'antd/es/breadcrumb/style';
import { CommonModalProps } from "sophon-base/src/components/SophonList/MainTable/index";
import { genReqParams, getReqFun, SophonListConfigStore } from "sophon-base/src/components/SophonList";
import 'antd/es/form/style';
import Input from 'antd/es/input';
import 'antd/es/input/style';
import { NameValidateRules } from "sophon-utils/src/utils";
import Form, { FormComponentProps } from "antd/lib/form/Form";
import Icon from 'antd/es/icon';
import 'antd/es/icon/style';
import { useLoadingEffect } from "sophon-utils/src/components/SophonHooks/LoadingEffect";

const TabPane = Tabs.TabPane;

export enum ModalType {
  COPY = 'copy',
  MOVE = 'move'
}

interface CopyMoveModalProps<T> extends CommonModalProps<T>{
  type: ModalType;
  entity: T;
  parentEntity: T | null;
}

function CopyMoveModal<T>(props: CopyMoveModalProps<T>) {
  const { onOK, onCancel, popup,type, entity, parentEntity} = props;
  const { listName, keyFields, cruds, treeMenu, t } = useContext(SophonListConfigStore);
  const { leafName, folderId, folderName, distinguishFolder } = keyFields;
  const {rootNodes = []} = treeMenu;
  const mockRootNodes = useMemo(() => rootNodes.map((rootName: string, index: number) => ({
    [folderId]: null,
    [folderName]: rootName,
    isRoot: true,
    isDirectory: true,
    rootNodeIndex: index,
    mockId: `root_${index}`,
  } as any as T)), [rootNodes]);
  const defaultTab = mockRootNodes && mockRootNodes.length ? mockRootNodes[entity ? (entity['rootNodeIndex'] || 0) : 0] : {'mockId': 'root', [folderName]: listName};
  const [menus, setMenus] = useState<React.ReactNode[]>([]);
  const lastLevelMenus = useMemo(() => menus.slice(Math.max(menus.length - 3, 0)), [menus]);
  const [destId, setDestId] = useState<string>('');
  const [rename, setRename] = useState<string>(entity ? entity[leafName] || '' : '');
  const formRef = createRef<FormComponentProps>();
  const [crumbs, setCrumbs] = useState<T[]>([]);

  const loadData = async (level: number, node?: T) => {
    let items: React.ReactNode[] = [];
    const {method, needParams} = getReqFun(cruds, 'read', node);
    if (method && needParams) {
      const additionalParams = level === 0 ? {parentId: ''} : {};  // 首次加数据，都从根节点开始取数据，所以parentId传空串
      const reqParams = genReqParams(needParams, node as any, {...additionalParams});
      const nodes = await method(reqParams);
      const folders = nodes.filter((node: T) => node[distinguishFolder] && (parentEntity? parentEntity[folderId] !== node[folderId] : true) ); // 过滤出除父文件夹以外的文件夹
      if (folders) {
        items = folders.map((folder: T) => (
          <Menu.Item key={folder[`${folderId}`]} onClick={() => loadData(level + 1, folder)}>
            <div className='modal-menu-folder-item'>
              <div className='modal-tree'>
                <Icon type='folder-open' style={{fontSize: '16px'}}/>
                {folder[`${folderName}`]}
              </div>
            </div>
          </Menu.Item>
        ));
      }
    }

    if (items.length) {
      // 此处设置新的Menus需要用回调的方式，否则如果直接取menu 拿到的是初始值
      setMenus((currentMenus) =>
        [...currentMenus.slice(0, level),
          <Menu key={node ? node[folderId] || 'root' : 'root'}>{items}</Menu>
        ]);
    } else {
      setMenus((currentMenus) =>
        [...currentMenus.slice(0, level)]);
    }

    if (level === 0) {
      resetDest();
    } else if(node){
      setDestId(node[folderId] || '');
      setCrumbs((currentCrumbs) => [...currentCrumbs.slice(0, level), node]);
    }
  };

  const resetDest = () => {
    if (mockRootNodes.length) {
      setDestId('');
      setCrumbs([mockRootNodes[entity['rootNodeIndex'] || 0]]);
    } else if(parentEntity) {
      setDestId(parentEntity[folderId]);
      setCrumbs([parentEntity]);
    } else {
      setDestId('');
      setCrumbs([]);
    }
    setMenus((currentMenus) => [...currentMenus.slice(0, 1)]);
  };

  const [_onOK, loading]= useLoadingEffect(async () => {
    if (type === ModalType.MOVE) {
      const {method: moveFun, needParams} = getReqFun(cruds, 'move', entity);
      if (moveFun && needParams) {
        const reqParams = genReqParams(needParams, entity as any, {destId});
        await moveFun(entity, reqParams);
        if (onOK) {
          onOK();
        }
      }
    } else if (type === ModalType.COPY) {
      formRef.current!.form.validateFieldsAndScroll( async (errors: any) => {
        if (errors) {
          return;
        } else {
          const {method: copyFun, needParams} = getReqFun(cruds, 'copy', entity);
          if (copyFun && needParams) {
            const reqParams = genReqParams(needParams, entity as any, {destId, name: rename});
            await copyFun(entity, reqParams);
          }
          if (onOK) {
            onOK();
          }
        }
      });
    }
    onCancel && onCancel();
  });

  const handleRenameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRename(e.target.value);
  };

  useEffect(() => {
    if (popup) {
      setMenus([]);
      loadData(0, entity);
    }
  }, [popup]);

  return(
    <Modal
      onCancel={onCancel}
      footer={null}
      width={738}
      className='copy-move-modal'
      title={type === ModalType.COPY ? t(`App:Copy`) : t(`App:Move`)}
      visible={popup}
    >
      <div className='copy-move-tab-first-step'>
        {
          type === ModalType.COPY ? <NameForm wrappedComponentRef={formRef} initName={rename} onChange={handleRenameChange}/> : null
        }
        <Tabs onChange={()=>{}}>
          <TabPane key={defaultTab['mockId']} tab={defaultTab[folderName]}>
            <div className='common-copy-move-menu-contents' style={{marginBottom: 20}}>
              <div className='common-copy-move-menu-breadcrumbs'>
                  <span style={{ marginRight: '5px'}}>
                    {t('ProjectsViewList:Target Folder')}:{' '}
                  </span>
                  <Breadcrumb style={{fontSize: 12}}>
                    {
                      crumbs.map((entity: T, index: number) =>
                        (<Breadcrumb.Item key={entity[folderId]}><a onClick={() => loadData(index, entity)}>{entity[folderName]}</a></Breadcrumb.Item>)
                      )
                    }
                  </Breadcrumb>
              </div>
              <div className='common-copy-move-menus'>
                {lastLevelMenus}
              </div>
            </div>
          </TabPane>
        </Tabs>
        <div className='copy-move-modal-footer'>
          <div>
            <Button key='reset' type='primary' onClick={resetDest} >
              {t('App:Reset Selection')}
            </Button>
          </div>
          <div>
            <Button key='cancel' onClick={onCancel} className='cm-cancel-btn'>
              {t('App:Cancel')}
            </Button>
            &nbsp;&nbsp;
            <Button
              key='submit'
              type='primary'
              onClick={_onOK}
              loading={loading}
              className='cm-confirm-btn'
            >
              {t('App:Confirm')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}


interface NameFormProps extends FormComponentProps {
  initName: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}
const NameFormBase = forwardRef<FormComponentProps, NameFormProps>(
  ({ initName, onChange, form }: NameFormProps, ref) => {
    useImperativeHandle(ref, () => ({
      form
    }));

    return (
      <Form
        labelCol={{ span:3 }}
        wrapperCol={{ span: 21 }}
      >
        <Form.Item label="rename">
          {form.getFieldDecorator('rename', {
            initialValue: initName,
            rules: NameValidateRules,
          })(<Input onChange={onChange}/>)}
        </Form.Item>
      </Form>
    );
  }
);

const NameForm = Form.create<NameFormProps>()(NameFormBase);


export default CopyMoveModal;
