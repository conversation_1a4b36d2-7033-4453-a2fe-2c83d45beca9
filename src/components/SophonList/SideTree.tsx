import * as React from 'react';
import { useEffect, useState, useCallback, useContext } from 'react';
import Search from 'antd/es/input/Search';
import 'antd/es/input/style';
import Menu from 'antd/es/menu';
import 'antd/es/menu/style';
import { FolderIcon } from "sophon-utils/src/icons/FolderIcon";
import EllipsisText from "sophon-utils/src/components/EllipsisText";
import cs from 'classnames';
import { searchAncestors, SophonListConfigStore } from "sophon-base/src/components/SophonList/index";
import _compact from 'lodash/compact';
import _cloneDeep from 'lodash/cloneDeep';

const SubMenu = Menu.SubMenu;
// import { baseInjectHook } from "sophon-utils/src/business-related/utils";

export interface TreeMenuProps<T> {
  data?: T[];
  loadBranch: (node?: T) => void;
  refresh: (node?: T) => void;
}

function SideTree<T>(props: TreeMenuProps<T>) {
  const { data = [], loadBranch, refresh } = props;
  const { keyFields, openKeys, selectedKeys, treeMenu, dispatch } = useContext(SophonListConfigStore);
  const {rootNodes,  showLeaf = false } = treeMenu;
  const { childrenField, folderId, folderName, leafId, leafName, distinguishFolder } = keyFields;
  const [searchKey, setSearchKey] = useState<string>('');
  const [treeData, setTreeData] = useState<T[]>(data);

  useEffect(() => {
    handlePressEnter();
  }, [data]);

  const expandShrink = async (node: T) => {
    await refresh(node);
    const ancestors = searchAncestors(data, folderId!, childrenField!, node);
    dispatch({
      type: 'updateBreadcrumb',
      payload: {
        breadcrumb: ancestors,
      } as any
    });
  };

  const handleOpenChange = (newKeys: string[]) => {
    dispatch({type: 'updateOpenKeys', payload: newKeys});
  };


  const renderMenu = (treeLevel: number, nodes: Array<T>, index: number) => {
    const items = [];
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      let rootNodeIndex = 0;
      if (rootNodes && rootNodes.length) {
        rootNodeIndex = treeLevel === 0 ? i : index;
        node['rootNodeIndex'] = rootNodeIndex;
      }
      if (node[`${distinguishFolder}`] && Array.isArray(node[`${childrenField}`]) && node[`${childrenField}`].length > 0) {
        items.push(
          <SubMenu key={node[`${folderId}`] || node['mockId']} title={commonRender(node)} onTitleClick={(e) => expandShrink(node)}>
            {renderMenu(treeLevel + 1, node[`${childrenField}`], rootNodeIndex)}
          </SubMenu>
        );
      } else if (node[`${distinguishFolder}`]) {
        items.push(
          <Menu.Item key={node[`${folderId}`] || node['mockId']} onClick={(e) => loadBranch(node)}>{commonRender(node)}</Menu.Item>
        );
      } else {
        if (showLeaf) {
          items.push(
            <Menu.Item key={node[`${leafId}`]}>{commonRender(node)}</Menu.Item>
          );
        }
      }
    }
    return items;
  };

  const commonRender = (node: T): React.ReactNode => {
    const className = cs('submenu', {
      'root-node': false,
      'stack-root': false,
      'first-level': true,
      'non-first-level': false,
    });
    const FileFolderImg = node[`${distinguishFolder}`] ? <FolderIcon /> : <i/>;
    return (
      <div className={className} style={{ display: 'flex' }}>
        <span className='icon'>
          {FileFolderImg}
        </span>
        <EllipsisText text={node[`${leafName}`] || node[`${folderName}`]} mode='dimension' length={'calc(100% - 20px)'}/>
      </div>
    );
  };

  const changeHandle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKey(e.target.value);
  };

  const searchHandle = useCallback(() => {
    if (data.length) {
      const nodes = _cloneDeep(data);
      const filterNodes = nodes.map(node => clipTree(searchKey, node));
      setTreeData(_compact(filterNodes));
      setTreeData(nodes);
    }
  }, [searchKey, data]);

  const handlePressEnter = () => {
    if (searchKey === '') {
      setTreeData(data);
    } else {
      searchHandle();
    }
  };

  const clipTree = (search: string,node: T): T | undefined => {
    const keyName = leafName || folderName;
    const showName = node[keyName!];

    if (node[childrenField!] && node[childrenField!].length) {
      const filterChildren = node[childrenField!].map((leaf: T) => clipTree(search, leaf));
      const newChildren = filterChildren.filter((n: T) => n !== undefined);
      if (newChildren.length) {
        node[childrenField!] = newChildren;
      } else {
        delete node[childrenField!];
      }
    }
    return showName.indexOf(search) >= 0 || node[childrenField!] || node['isRoot'] ? node : undefined;
  };



  console.dir('side tree render');
  return (
    <div className="search-menu-wrapper">
        <Search
          className='menu-search'
          value={searchKey}
          onChange={changeHandle}
          onPressEnter={handlePressEnter}
        />
      <Menu
        mode='inline'
        openKeys={openKeys}
        selectedKeys={selectedKeys}
        onOpenChange={handleOpenChange}
      >
        {renderMenu(0, treeData, 0)}
      </Menu>
    </div>
  );
}

export default SideTree;
