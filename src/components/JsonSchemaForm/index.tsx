import { ParamDescJson, TFParamsJson } from 'sophon-base/src/models/json';
import { Operator } from 'sophon-base/src/models/Lab';
import Form from 'antd/es/form';
import 'antd/es/form/style';
import * as React from 'react';
import { toJS, observable, runInAction } from 'mobx';
import { getFormItem } from './registry';
import { baseInjectWithRouter, BaseInjectProps } from '../../utils/index';
import ImageParamsItem from './ImageParamsItem';
import { modelApi } from 'sophon-base/src/api/ModelApi';
import { editorStore } from '../../stores/Lab/FlowEditorStore';
import Tabs from 'antd/es/tabs';
interface JsonSchemaFormProps {
  operator: Operator;
}

@baseInjectWithRouter
export class JsonSchemaForm extends React.Component<JsonSchemaFormProps & BaseInjectProps<{ pid: string }>> {
  // 导入深度学习算子 图片的参数
  @observable picModelParams: TFParamsJson | null = null;
  // 导入深度学习算子 若不是最新版本 则需要传的版本id
  @observable versionId: string | undefined = undefined;
  paramVisibility = (p: ParamDescJson) => {
    let visible = true;
    const paramsKv = this.props.operator.params.kvs;
    if (!p.conditions) {
      return visible;
    }
    for (const condition of p.conditions) {
      if (condition.type) {
        const v = JSON.stringify(toJS(paramsKv.get(condition.depParam)));
        if (condition.type === 'equal') {
          if (v !== condition.depValue) {
            visible = false;
          }
        } else if (condition.type === 'exist') {
          if (!v) {
            visible = false;
          }
        }
      }
    }
    return visible;
  };

  onChange = (k: string, v: any, p: any, n?: any) => {
    if (v.nativeEvent) {
      // fix [object Object] 避免把 React.Change 对象传入
      return;
    }
    if (p.type === 'integer' || p.type === 'number') {
      // 数据类型时不允许输入超范围的数值
      if (!isNaN(v) && p.maximum) {
        if (parseFloat(v) > p.maximum) {
          return;
        }
      }
      if (!isNaN(v) && p.minimum) {
        if (parseFloat(v) < p.minimum) {
          return;
        }
      }
    }

    this.props.operator.params.setParam(k, v);
    if (p.onChange) {
      p.onChange(v);
    }
    if (p.type === 'model-version') {
      runInAction(() => {
        this.versionId = v;
        if (v) {
          this.showPicModelParams(this.props.operator);
        }
      });
    } else if (p.type === 'file' && p.extra && p.extra.onChange) {
      this.versionId = undefined;
      p.extra.onChange(v, n);
    } else {
      runInAction(() => {
        this.versionId = undefined;
        if (p.type === 'bool') {
          this.showPicModelParams(this.props.operator);
        }
      });
    }
  };

  get pid() {
    return this.props.match!.params.pid;
  }

  componentWillReceiveProps(nextProps: JsonSchemaFormProps & BaseInjectProps<{ pid: string }>) {
    this.showPicModelParams(nextProps.operator);
  }

  showPicModelParams = (operator: Operator) => {
    if (operator.desc.key === 'read_model' && this.versionId) {
      modelApi.getPicModelParams(this.versionId).then((res: TFParamsJson) => {
        runInAction(() => {
          if (res.isImage) {
            this.picModelParams = res;
          } else {
            this.picModelParams = null;
          }
        });
      });
    } else {
      if (this.picModelParams) {
        runInAction(() => (this.picModelParams = null));
      }
    }
  };

  renderItems(type?: string) {
    const datasetNames: string[] = [];
    try {
      const ops = editorStore.currentTab!.canvas.root.operators[0].ops;
      if (ops) {
        ops.forEach(op => {
          if (op.desc.key.toLowerCase() === 'dataset') {
            datasetNames.push(op.name);
          }
        });
      }
    } catch (e) {
      console.warn('Not found dataset!');
    }
    const operator = this.props.operator;
    const editable = editorStore && editorStore.currentTab ? editorStore.currentTab!.editable : true;
    const visibleParamDescs = operator.params.descs.filter(this.paramVisibility);
    const filterDescs = visibleParamDescs.filter(p => {
      if (type) {
        if (p.paramType) {
          return p.paramType === type;
        } else {
          return type === 'basic';
        }
      } else {
        return true;
      }
    });
    const items = filterDescs.map((p, i) => {
      const Item = getFormItem(p, operator.desc.key);
      return (
        // 有显示条件的参数需要有一块独立区域以区别其他参数
        <div className={(p.conditions && p.conditions.length) ? 'conditions-visible-param' : 'normal-param'} key={i}>
          <Item
            hidden={p.hidden}
            operator={operator}
            value={toJS(operator.params.kvs.get(p.key))}
            paramDesc={p}
            onChange={(v, n) => this.onChange(p.key, v, p, n)}
            editable={editable}
            datasetNames={datasetNames}
          />
        </div>
      );
    });
    return items;
  }

  renderItemTab() {
    const basicItems = this.renderItems('basic');
    const ioItems = this.renderItems('io');
    const advancedItems = this.renderItems('advanced');
    const operator = this.props.operator;
    if (operator.desc.key === 'read_model') {
      return this.renderReadModelItem();
    } else if (
      (basicItems.length && !ioItems.length && !advancedItems.length) ||
      (!basicItems.length && ioItems.length && !advancedItems.length) ||
      (!basicItems.length && !ioItems.length && advancedItems.length)
    ) {
      return this.renderItems();
    } else if (this.props.operator.params.descs.filter(this.paramVisibility).length) {
      return (
        <Tabs
          className='op-tabs'
          animated={false}
          defaultActiveKey={basicItems.length && ioItems.length && advancedItems.length ? 'basic' : undefined}
        >
          {ioItems.length ? <Tabs.TabPane key='io' tab={this.props.t!('ProjectsViewLab:IO Parameter')}>
            {ioItems}
          </Tabs.TabPane> : null}
          {basicItems.length ? <Tabs.TabPane key='basic' tab={this.props.t!('Components:Parameters')}>
            {basicItems}
          </Tabs.TabPane> : null}
          {advancedItems.length ? <Tabs.TabPane key='advanced' tab={this.props.t!('ProjectsViewLab:Advanced Parameter')}>
            {advancedItems}
          </Tabs.TabPane> : null}
        </Tabs>
      );
    } else {
      return null;
    }
  }

  renderReadModelItem() {
    return (
      <>
        <div className='param-label'>{this.props.t!('App:Name')}</div>
        <div className='param-content'>{this.props.operator.name}</div>
        <div className='param-label'>{this.props.t!('App:Description')}</div>
        <div className='param-content'>{this.props.operator.desc.desc}</div>
        {this.renderItems()}
      </>
    );
  }

  render() {
    const operator = this.props.operator;
    return (
      <Form className='param-from' key={operator.id}>
        {this.renderItemTab()}
        <ImageParamsItem params={this.picModelParams} />
      </Form>
    );
  }
}
