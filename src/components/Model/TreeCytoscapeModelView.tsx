import * as React from 'react';
import { <PERSON><PERSON>ode<PERSON>son, TreeEdgeJson } from '../../models/json';
// import { HierarchyNode } from 'd3-hierarchy';
// import * as d3 from 'd3';
import cytoscape from 'cytoscape';
import dagre from 'cytoscape-dagre';

interface TreeProps {
  nodeJson: TreeNodeJson;
  schemas: string[];
}

interface TreeStates {
  w: number | string;
  h: number | string;
  // root: HierarchyNode<TreeNodeJson> | null;
}

interface Nodes {
  data: {
    id: string;
    name: string;
  };
}

interface Edges {
  data: {
    source: string;
    target: string;
    relation: string;
  };
}

export class TreeCytoscapeModelView extends React.Component<TreeProps, TreeStates> {
  canvasRef: HTMLElement | null;
  nodes: Nodes[];
  edges: Edges[];
  nodeIndex: number;

  constructor(props: TreeProps) {
    super(props);
    this.nodeIndex = 0;
    this.nodes = [];
    this.edges = [];
    this.state = {
      w: '100%',
      h: 700,
      // root: null,
    };
  }

  parseTree(nodeJson: TreeNodeJson) {
    const curId = 'n' + this.nodeIndex;
    this.nodes.push({
      data: {
        id: curId,
        name:
          nodeJson.name === 'leaf'
            ? parseFloat(nodeJson.prediction).toFixed(1)
            : this.props.schemas[nodeJson.featureIndex],
      },
    });
    if (nodeJson.children) {
      nodeJson.children.forEach((nj: TreeEdgeJson) => {
        const ch: TreeNodeJson = nj.to;
        const targetId = 'n' + ++this.nodeIndex;
        this.edges.push({
          data: {
            source: curId,
            target: targetId,
            relation: nj.relation + parseFloat(nj.value).toFixed(1),
          },
        });
        this.parseTree(ch);
      });
    }
  }

  componentDidMount() {
    this.parseTree(this.props.nodeJson);
    cytoscape.use(dagre);
    cytoscape({
      container: this.canvasRef,
      boxSelectionEnabled: false,
      autounselectify: true,
      layout: {
        name: 'dagre',
      },
      style: [
        {
          selector: 'node',
          style: {
            'content': 'data(name)',
            'background-color': '#48a2cc',
            'color': '#363636',
            'text-opacity': 0.8,
            'text-valign': 'center',
            'text-halign': 'center',
          },
        },
        {
          selector: 'edge',
          style: {
            'width': 4,
            'target-arrow-shape': 'triangle',
            'line-color': '#9dbaea',
            'target-arrow-color': '#9dbaea',
            'curve-style': 'bezier',
            'content': 'data(relation)',
            'color': '#363636',
            'text-opacity': 0.8,
            // 'target-text-offset': '1',
          },
        },
      ],

      elements: {
        nodes: this.nodes,
        edges: this.edges,
      },
    });
  }

  render() {
    return (
      <div
        style={{
          position: 'relative',
          height: this.state.h,
          width: this.state.w,
          overflow: 'auto',
        }}
        id='cy'
        ref={r => (this.canvasRef = r)}
      />
    );
  }
}
