import { SelectProps } from 'antd/es/select';
import * as React from 'react';
import Select from 'antd/es/select';
import 'antd/es/select/style';
import _debounce from 'lodash/debounce';
import classNames from 'classnames';
import { observer } from 'mobx-react';
import { action, observable } from 'mobx';

export type ScrollPullSelectProps = {
  id: string; // 唯一标识Select，便于DOM定位
  onPull: () => Promise<any>; // 下拉到底时执行动态获取数据
} & SelectProps;

/** ScrollPullSelect 基于 antd 的 Select 组件，在其基础上添加了下拉到底执行 onPull的功能，可用于 Select 组件的懒加载 */

@observer
export default class ScrollPullSelect extends React.Component<ScrollPullSelectProps> {
  @observable loading: boolean = false;

  get scrollSelect() {
    const selects = document.querySelectorAll('.ant-select')!;
    const target = document.querySelector(`.${this.props.id}`)!;
    const index = [].indexOf.call(selects, target);
    const menu = document.querySelectorAll('.ant-select-dropdown-menu')![index];
    const wrapper = document.querySelectorAll('.ant-select-dropdown')![index];
    return {
      menu,
      wrapper,
    };
  }

  onPopupScroll = _debounce(async () => {
    const menu = this.scrollSelect.menu;
    const scrollHeight = menu.scrollHeight;
    const scrollTop = menu.scrollTop;
    const height = menu.clientHeight;
    if (scrollTop + height >= scrollHeight - 10) {
      await this.props.onPull();
      menu.scrollTo({ top: scrollTop });
    }
  }, 100);

  @action
  setLoading(loading: boolean) {
    this.loading = loading;
  }

  render() {
    return (
      <Select {...this.props} className={classNames(this.props.id)} onPopupScroll={this.onPopupScroll}>
        {this.props.children}
      </Select>
    );
  }
}
