@use '~sophon-utils/src/themes/tm';

.add-cluster-modal {
  table {
    table-layout: fixed;

    tr {
      &:hover {
        .anticon {
          @include tm.themeify {
            color: tm.themed('white-color') !important;
          }
        }
      }
    }

    td {
      word-break: break-all;
    }
  }

  .ant-alert {
    &.ant-alert-error {
      @include tm.themeify {
        background-color: rgba($color: tm.themed('error-color'), $alpha: 0.1);
        border-color: tm.themed('error-color');
      }
    }
  }
}

#sparkResource {
  .ant-table-row-expand-icon-cell {
    padding-left: 17px;
    padding-right: 16px;
  }

  .ant-table-expanded-row {
    &:hover {
      .btn-link {
        @include tm.themeify {
          color: tm.themed('active-color');
        }
      }
    }

    > td {
      overflow: inherit;
      padding: 0;
    }
  }

  .sophon-icon {
    &.cross {
      position: absolute;
      top: auto;
    }
  }
}

.hadoop-members-wrapper {
  padding: 30px;

  .hadoop-members-header {
    padding-bottom: 10px;

    .back {
      margin-right: 10px;
    }

    .cluster-name {
      margin-left: 10px;
    }
  }

  .hadoop-members-content {
    .hadoop-members-table-header {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;

      .cluster-info {
        .cluster-label {
          margin-right: 10px;
        }

        .cluster-content {
          margin-right: 30px;

          > a {
            cursor: text;
          }
        }
      }

      .search-input {
        width: 180px;
      }
    }

    .simple-table-wrapper {
      .ant-table {
        max-height: calc(100vh - 380px);
        overflow: auto;
      }
    }
  }
}

.yarn-cluster-deletion-content {
  .yarn-cluster-deletion-radio-group {
    width: 100%;

    .yarn-cluster-deletion-radio-item {
      margin: auto;
      margin-bottom: 10px;
      padding: 15px;
      width: 438px;

      @include tm.themeify {
        background: tm.themed('layout-background');
        border: 1px solid tm.themed('border-color');
      }

      .ant-radio-wrapper {
        align-items: center;
        display: flex;
        font-size: 12px;
        white-space: normal;

        @include tm.themeify {
          color: tm.themed('text-color');
        }

        > span {
          word-break: break-all;
          word-wrap: break-word;
        }

        > span:last-child {
          flex: 1;
          padding-left: 10px;
          padding-right: 0;
        }
      }
    }
  }
}
