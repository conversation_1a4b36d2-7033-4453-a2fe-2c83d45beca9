// import { JobUpdate<PERSON>son } from 'sophon-base/src/models/json';
// import { notification, DEFAULTNOTECONFIG } from '../components/Msg';
// import { getBaseName, getPublicAPIPath, history } from 'utils';
// import * as React from 'react';
// import * as io from 'socket.io-client';
// import { DefaultPoolId, SessionInfoJson } from 'sophon-utils/src/business-related/models/SessionJson';
// import { sessionStore } from 'sophon-utils/src/business-related/stores/SessionStore';
// import { bpcStoreContainer } from '../stores/Kg/BlueprintConstructionStore';
// import { uiStore } from 'sophon-base/src/stores/common/UiStore';
// import { resourceStore } from '../stores/Lab/EditorResourceStore';
// import { projectStore } from '../stores/common/ProjectStore';
// import { userStore } from '../stores/common/UserStore';
//
// export interface EventMsg {
//   data: string;
//   type: string;
// }
//
// export interface KgSchemaResultJson {
//   schemaName: string;
//   status: string;
// }
//
// export interface EventHandler {
//   connect(config: Map<string, string>): void;
//
//   disconnect(): void;
//

//   addEventListener(type: string, callback: (evt: EventMsg) => void): void;
//
//   removeEventListener(type: string, callback: (evt: EventMsg) => void): void;
//   sendMsg(type: string, msg?: string): void;
// }
//

// function sessionUpdateListener(evt: EventMsg) {
//   const s = JSON.parse(evt.data) as SessionInfoJson;
//   const t = uiStore.t;
//   const owner = JSON.parse(s.owner);
//   // Only update the correct resource pool's session
//   if (!projectStore.currentProject || (projectStore.currentProject.resourcePool || DefaultPoolId) === owner.id) {
//     sessionStore.updateSessionStatus(s);
//     if (sessionStore.isCurrentSessionReady) {
//       resourceStore.updateFunctions(sessionStore.currentSessionId, projectStore.pid);
//       notification.info(t('Store:Session'), t('Store:Session Ready'), DEFAULTNOTECONFIG);
//     }
//   }
// }
//

// function jobUpdateListener(evt: EventMsg) {
//   const e = React.createElement;
//   const pid = projectStore.pid;
//   const graphStore = bpcStoreContainer.getBpcStoreByPid(pid);
//   const s = JSON.parse(evt.data) as JobUpdateJson;
//
//   // 判断不是当前项目不提示
//   if (s.pid !== pid) {
//     return;
//   }

//   const t = uiStore.t;
//   const id = s.id;
//   const resultUrl = `/projects/${pid}/history/${id}/result`;
//   const logUrl = `/projects/${pid}/history/${id}/log`;
//   const br = e('br');
//   const link = e(
//     'a',
//     { style: { marginRight: 20 }, onClick: () => history.push(resultUrl) },
//     t('ProjectsViewLab:View Result')
//   );
//   const logLink = e('a', { onClick: () => history.push(logUrl) }, t('ProjectsViewLab:View Log'));
//   console.log(`socket get JobUpdate Message: ${s.state.state}`);
//   // kg构建蓝图的任务
//   const isBlueprintJob = s.id.indexOf('graph-job') !== -1;
//   if (s.state.state === 'job finished' || s.state.state === 'SUCCEEDED') {
//     if (isBlueprintJob) {
//       graphStore.removeTask(s.bpId!);
//       const blueprintId = s.bpId;
//
//       const kgUrl = `/projects/${s.pid}/blueprint/${blueprintId}/branches`;
//       const kgLink = e(
//         'a',
//         { style: { marginRight: 20 }, onClick: () => history.push(kgUrl) },
//         t('ProjectsViewLab:View Result')
//       );
//
//       const div = e('p', undefined, t('ProjectsViewLab:Run successful'), br, kgLink, logLink);
//       notification.info(t('Store:Run Status'), div, DEFAULTNOTECONFIG);
//     } else {
//       const div = e('p', undefined, t('ProjectsViewLab:Run successful'), br, link, logLink);
//       notification.info(t('Store:Run Status'), div, DEFAULTNOTECONFIG);
//     }
//   } else if (s.state.state === 'FAILED') {
//     const div = e('p', undefined, t('ProjectsViewLab:Run failed'), br, logLink);
//     notification.info(t('Store:Run Status'), div, DEFAULTNOTECONFIG);
//     if (isBlueprintJob) {
//       graphStore.removeTask(s.bpId!);
//     }
//   } else if (s.state.state === 'STARTED') {
//     notification.info(t('Store:Run Status'), t('ProjectsViewLab:Run started'), DEFAULTNOTECONFIG);
//     if (isBlueprintJob) {
//       graphStore.updateTask(s.bpId!, s);
//     }
//   } else if (s.state.state === 'CANCELLED') {
//     const div = e('p', undefined, t('ProjectsViewLab:Cancelled'), br, logLink);
//     notification.info(t('Store:Run Status'), div, DEFAULTNOTECONFIG);
//     if (isBlueprintJob) {
//       graphStore.removeTask(s.bpId!);
//     }
//   } else {
//     notification.info(t('Store:Run Status'), s.state.state, DEFAULTNOTECONFIG);
//   }
// }
//

// function jobStepListener(evt: EventMsg) {
//   // const json = JSON.parse(evt.data) as JobStepUpdateJson;
//   // console.dir(json);
// }
//
// function initListeners(handler: EventHandler) {
//   handler.addEventListener('session', sessionUpdateListener);
//   handler.addEventListener('job', jobUpdateListener);
//   handler.addEventListener('job-step', jobStepListener);

//   handler.addEventListener('kg-schema-construction', (ev: EventMsg) => {
//     const t = uiStore.t;
//     const kgResult = JSON.parse(ev.data) as KgSchemaResultJson;
//
//     if (kgResult.status === 'succeeded') {
//       notification.info(
//         t('ProjectsViewGraphSchema:Schema Construction'),
//         t('ProjectsViewGraphSchema:Schema was loaded into StellarDB'),
//         DEFAULTNOTECONFIG
//       );
//     } else if (kgResult.status === 'failed') {
//       notification.info(
//         t('ProjectsViewGraphSchema:Schema Construction'),
//         t('ProjectsViewGraphSchema:Failed to load Schema data into StellarDB'),
//         DEFAULTNOTECONFIG
//       );
//     }
//   });
// }
//
// export class SocketEventHandler implements EventHandler {

//   socket?: SocketIOClient.Socket = undefined;
//   _unbindEventListener: Array<[string, (evt: EventMsg) => void]> = [];
//
//   connect(c: Map<string, string>) {
//     console.log(`socket connect start`);
//     this.disconnect();
//
//     // const port = c.get('sophon.socket.port');
//     const proxyStatus = c.get('sophon.proxy.ok');
//     const basename = getBaseName();
//     let connectUrl = '';
//     if (proxyStatus === 'true') {
//       if (basename === '') {
//         this.socket = io(`${window.location.protocol}//${window.location.hostname}:${window.location.port}`, {
//           path: `${getPublicAPIPath()}/sophonWS/socket.io`,
//         });
//         connectUrl = `${window.location.protocol}//${window.location.hostname}:${
//           window.location.port
//         }${getPublicAPIPath()}/sophonWS/socket.io`;
//       } else {
//         this.socket = io(`${window.location.protocol}//${window.location.hostname}:${window.location.port}`, {
//           path: `${getPublicAPIPath()}/${basename}/sophonWS/socket.io`,
//         });
//         connectUrl = `${window.location.protocol}//${window.location.hostname}:${
//           window.location.port
//         }${getPublicAPIPath()}/${basename}/sophonWS/socket.io`;
//       }
//     } else {
//       this.socket = io(
//         `${window.location.protocol}//${window.location.hostname}:${window.location.port}${getPublicAPIPath()}`
//       );
//       connectUrl = `${window.location.protocol}//${window.location.hostname}:${
//         window.location.port
//       }${getPublicAPIPath()}`;
//     }
//     console.log(`socket connect success, baseName: ${basename}, WsUrl: ${connectUrl}`);
//     this.socket.on('connect', () => {
//       // to map username to socket session id

//       console.log(`receive socket connect event, usrName: ${userStore.userId}`);
//       this.sendMsg('update-username', userStore.userId);
//     });
//     this.socket.on('disconnect', () => {
//       console.log(`receive socket disconnect event`);
//     });
//     this.socket.on('update-username', () => {
//       // notification.info('更新用户名', '更新');
//     });
//     initListeners(this);
//     this._unbindEventListener.forEach(([type, callback]) => this.addEventListener(type, callback));
//     this._unbindEventListener.length = 0;
//   }
//
//   disconnect() {
//     if (this.socket) {
//       this.socket.close();
//       this.socket = undefined;
//       console.log(`socket disconnect`);
//     }
//   }
//

//   addEventListener(type: string, callback: (evt: EventMsg) => void): void {
//     if (this.socket) {
//       this.socket.on(type, (data: any) => {
//         callback({ type, data });
//       });
//     } else {
//       this._unbindEventListener.push([type, callback]);
//     }
//   }
//

//   removeEventListener(type: string, callback: (evt: EventMsg) => void): void {
//     if (this.socket) {
//       this.socket.off(type, callback);
//     } else {
//       this._unbindEventListener = this._unbindEventListener.filter(
//         listener => !(listener[0] === type && listener[1] === callback)
//       );
//     }
//   }
//
//   sendMsg(type: string, data?: string): void {
//     if (this.socket && data) {
//       console.log(`socket sentData: ${data}`);
//       this.socket.emit(type, data);
//     }
//   }
// }
