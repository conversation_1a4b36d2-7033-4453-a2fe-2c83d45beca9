import { CommonApi } from 'sophon-utils/src/business-related/api/CommonApi';
import { JobResult<PERSON>son, JobInfoJson } from 'sophon-base/src/models/json';

const PREFIX = '/base/api/history';

interface HistoryMap {
  [index: string]: JobInfo<PERSON>son;
}

class HistoryApi extends CommonApi {
  fetchHistoryList(pid: string): Promise<HistoryMap> {
    return this.axiosInstance.get(`${PREFIX}`, { params: { pid } }).then(r => r.data as HistoryMap);
  }

  fetchHistory(id: string): Promise<JobInfoJson> {
    return this.axiosInstance.get(`${PREFIX}/${id}`).then(r => r.data as JobInfo<PERSON>son);
  }

  getGraphHistoryList(pid: string): Promise<HistoryMap> {
    return this.axiosInstance.get(`${PREFIX}/graph`, { params: { pid } }).then(r => r.data as HistoryMap);
  }

  delete(ids: string[]): Promise<string[]> {
    return this.axiosInstance.delete(`${PREFIX}`, { params: { ids } }).then(r => r.data);
  }

  getLogs(id: string, start?: number, end?: number): Promise<string[]> {
    return this.axiosInstance
      .get(
        `${PREFIX}/${id}/logs${start !== undefined ? '?start=' + start : ''}${end !== undefined ? '&end=' + end : ''}`
      )
      .then(r => r.data as string[]);
  }

  getResult(id: string): Promise<JobResultJson> {
    return this.axiosInstance.get(`${PREFIX}/${id}/result`).then(r => r.data as JobResultJson);
  }

  async clearHistory(pid: string) {
    await this.axiosInstance.delete(`${PREFIX}/delete-all`, { params: { pid } });
  }
}

export const historyApi = new HistoryApi();
