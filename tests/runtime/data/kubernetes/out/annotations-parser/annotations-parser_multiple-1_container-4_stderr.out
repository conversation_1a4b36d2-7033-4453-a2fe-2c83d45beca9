[1554476063.58427, {"stream": "stderr", "container_4_stderr_parser": "Container 4 is logging on stderr", "kubernetes": {"pod_name": "multiple-1", "namespace_name": "annotations-parser", "annotations": {"fluentbit.io/parser-container-1": "container-1-parser", "fluentbit.io/parser_stdout-container-2": "container-2-stdout-parser", "fluentbit.io/parser_stderr-container-2": "container-2-stderr-parser", "fluentbit.io/parser_stdout-container-3": "container-3-stdout-parser", "fluentbit.io/parser_stderr-container-4": "container-4-stderr-parser", "fluentbit.io/parser": "default-parser"}, "container_name": "container-4"}}]