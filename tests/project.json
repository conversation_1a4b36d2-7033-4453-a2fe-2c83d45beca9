{"variables": [], "info": {"name": "project", "_postman_id": "51e2d6c5-447e-e5d1-da98-b156f4aafcf2", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "login", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["", "tests[\"authorization is present\"] = postman.getResponseHeader(\"authorization\"); ", "", "", "var authToken = postman.getResponseHeader(\"authorization\");", "postman.setEnvironmentVariable(\"token\", authToken);", ""]}}], "request": {"url": "{{host}}/login", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test\",\n  \"password\": \"password\"\n}"}, "description": ""}, "response": []}, {"name": "get projects (0 project)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['no projects'] = projects.length === 0;", ""]}}], "request": {"url": "{{host}}/projects", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "create project", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['create project'] = responseCode.code === 200;"]}}], "request": {"url": "{{host}}/projects", "method": "POST", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"project1\",\n  \"description\": \"this is project1\"\n}"}, "description": ""}, "response": []}, {"name": "get projects(1 project)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['1 projects'] = projects.length === 1;", "", "postman.setEnvironmentVariable(\"project1\", projects[0].id);"]}}], "request": {"url": "{{host}}/projects", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "create project copy", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['create project'] = responseCode.code === 200;"]}}], "request": {"url": "{{host}}/projects/{{project1}}", "method": "DELETE", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}, {"name": "get projects(0 project)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['0 projects'] = projects.length === 0;"]}}], "request": {"url": "{{host}}/projects", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "create project", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['create project'] = responseCode.code === 200;"]}}], "request": {"url": "{{host}}/projects", "method": "POST", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"project a\",\n  \"description\": \"this is project a\"\n}"}, "description": ""}, "response": []}, {"name": "create project", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['create project'] = responseCode.code === 200;"]}}], "request": {"url": "{{host}}/projects", "method": "POST", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"project b\",\n  \"description\": \"this is project b\"\n}"}, "description": ""}, "response": []}, {"name": "create project", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['create project'] = responseCode.code === 200;"]}}], "request": {"url": "{{host}}/projects", "method": "POST", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"project c\",\n  \"description\": \"this is project c\"\n}"}, "description": ""}, "response": []}, {"name": "get projects(page1)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['0 projects'] = projects.length === 2;"]}}], "request": {"url": {"raw": "{{host}}/projects?page=1&pageSize=2", "host": ["{{host}}"], "path": ["projects"], "query": [{"key": "page", "value": "1", "equals": true, "description": ""}, {"key": "pageSize", "value": "2", "equals": true, "description": ""}], "variable": []}, "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "get projects(page2)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['0 projects'] = projects.length === 1;", "", "postman.setEnvironmentVariable(\"project_publish\", projects[0].id);", "postman.setEnvironmentVariable(\"project_a\", projects[0].id);"]}}], "request": {"url": {"raw": "{{host}}/projects?page=2&pageSize=2", "host": ["{{host}}"], "path": ["projects"], "query": [{"key": "page", "value": "2", "equals": true, "description": ""}, {"key": "pageSize", "value": "2", "equals": true, "description": ""}], "variable": []}, "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "publish", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['create project'] = responseCode.code === 200;"]}}], "request": {"url": "{{host}}/projects/publish/{{project_publish}}", "method": "POST", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"project1\",\n  \"description\": \"this is project1\"\n}"}, "description": ""}, "response": []}, {"name": "get projects(3 project)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['0 projects'] = projects.length === 3;"]}}], "request": {"url": "{{host}}/projects", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "get published project (1 project)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get projects'] = responseCode.code === 200;", "", "var projects = JSON.parse(responseBody);", "tests['1 project'] = projects.length === 1;", "", "tests['project name'] = projects[0].projectName == 'project a';"]}}], "request": {"url": "{{host}}/projects/publish", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "get vars (0 vars)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get vars'] = responseCode.code === 200;", "", "var vars = JSON.parse(responseBody);", "tests['0 vars'] = vars.length === 0;", ""]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "put vars (2 new)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['put vars'] = responseCode.code === 200;", ""]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "PUT", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "\n[\n{\n  \"key\": \"var1\",\n  \"value\": \"1024\"\n},\n{\n  \"key\": \"var2\",\n  \"value\": \"4096\"\n}\n]"}, "description": ""}, "response": []}, {"name": "get vars (2 vars)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get vars'] = responseCode.code === 200;", "", "var vars = JSON.parse(responseBody);", "tests['2 vars'] = vars.length === 2;", "", "tests['var2 key'] = vars[0]['key'] === \"var2\";", "tests['var2 value'] = vars[0]['value'] === \"4096\";", "", "tests['var1 key'] = vars[1]['key'] === \"var1\";", "tests['var1 value'] = vars[1]['value'] === \"1024\";", ""]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "put vars (1 new, 1 modify)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['put vars'] = responseCode.code === 200;", ""]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "PUT", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "\n[\n{\n  \"key\": \"var1\",\n  \"value\": \"512\"\n},\n{\n  \"key\": \"var2\",\n  \"value\": \"4096\"\n},\n{\n  \"key\": \"var3\",\n  \"value\": \"999\"\n}\n]"}, "description": ""}, "response": []}, {"name": "get vars (3 vars)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get vars'] = responseCode.code === 200;", "", "var vars = JSON.parse(responseBody);", "tests['3 vars'] = vars.length === 3;", "tests['var3 key'] = vars[0]['key'] === \"var3\";", "tests['var3 value'] = vars[0]['value'] === \"999\";", "", "tests['var2 key'] = vars[1]['key'] === \"var2\";", "tests['var2 value'] = vars[1]['value'] === \"4096\";", "", "tests['var1 key'] = vars[2]['key'] === \"var1\";", "tests['var1 value'] = vars[2]['value'] === \"512\";"]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}, {"name": "delete vars", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['put vars'] = responseCode.code === 200;", ""]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "DELETE", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}, {"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "\n[\n{\n  \"key\": \"var2\",\n  \"value\": \"4096\"\n},\n{\n  \"key\": \"var3\",\n  \"value\": \"999\"\n}\n]"}, "description": ""}, "response": []}, {"name": "get vars (1 var)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["tests['get vars'] = responseCode.code === 200;", "", "var vars = JSON.parse(responseBody);", "tests['1 vars'] = vars.length === 1;", "", "", "tests['var1 key'] = vars[0]['key'] === \"var1\";", "tests['var1 value'] = vars[0]['value'] === \"512\";"]}}], "request": {"url": "{{host}}/projects/{{project_a}}/variables", "method": "GET", "header": [{"key": "Authorization", "value": "{{token}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"qunhao1\",\n  \"password\": \"123456\"\n}"}, "description": ""}, "response": []}]}