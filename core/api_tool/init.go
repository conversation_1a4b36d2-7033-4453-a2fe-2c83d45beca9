package api_tool

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/aws/smithy-go/ptr"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/google/uuid"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/dao"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
	"transwarp.io/applied-ai/applet-backend/pkg/models/generated"
)

var (
	ToolManager  IAPITool
	BuiltinTools []*api_tools.APIToolCollectionDO
)

const (
	ParserTypeYaml     = "yaml"
	ParserTypeJson     = "json"
	CollectionIDPrefix = "builtin-collection-"
	ToolIDPrefix       = "builtin-tool-"
)

func InitTools(ctx context.Context) error {
	ParserMap = make(map[string]APIMetaParser)
	ParserMap[ParserTypeYaml] = YamlParser{}
	ParserMap[ParserTypeJson] = JsonParser{}
	ToolManager = &APITool{}
	if err := InitToolTemplate(ctx); err != nil {
		return err
	}
	if err := InitBuiltinTools(); err != nil {
		return err
	}
	return nil
}

func InitToolTemplate(ctx context.Context) error {
	//if err := dao.APIToolCollectionDemoImpl.Upsert(ctx, &generated.APIToolCollectionDemo{
	//	ID:        uuid.New().String(),
	//	Name:      "宠物接口示例",
	//	MetaType:  "yaml",
	//	MetaInfo:  []byte(ToolDemoPet),
	//	Desc:      "宠物接口测试API",
	//	ProjectID: "",
	//}); err != nil {
	//	return err
	//}
	if err := dao.APIToolCollectionDemoImpl.Upsert(ctx, &generated.APIToolCollectionDemo{
		ID:        uuid.New().String(),
		Name:      "API测试",
		MetaType:  "yaml",
		MetaInfo:  []byte(ToolDemoTest),
		Desc:      "API测试",
		ProjectID: "",
	}); err != nil {
		return err
	}
	return nil
}

func InitBuiltinTools() error {
	if conf.Config.IsSimpleMode {
		return nil
	}
	currentNamespace := k8s.CurrentNamespaceInCluster()
	builtinOpenapiUrl := strings.ReplaceAll(conf.Config.APIToolConfig.BuiltinOpenapi.Url, conf.NamespacePlaceholder, currentNamespace)
	stdlog.Infof("builtin openapi url: %s", builtinOpenapiUrl)

	resp, err := http.Get(builtinOpenapiUrl)
	if err != nil {
		return stderr.Wrap(err, "failed to fetch openapis from URL %s", builtinOpenapiUrl)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return stderr.Error("failed to fetch openapis, status code: %d", resp.StatusCode)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return stderr.Wrap(err, "failed to read response body")
	}

	var openapiList []*openapi3.T
	if err := json.Unmarshal(data, &openapiList); err != nil {
		return stderr.Wrap(err, "failed to unmarshal openapi list")
	}

	BuiltinTools = make([]*api_tools.APIToolCollectionDO, 0, len(openapiList))
	for _, openapi := range openapiList {
		parser := &api_tools.OpenAPI3Parser{Spec: *openapi}
		apis, err := parser.GetAPIs()
		if err != nil {
			// 修改打印的日志，GetAPIs函数报错
			stdlog.Errorf("GetAPIs of openai parse failed: %+v", err)
			continue
		}
		openapiBytes, err := json.MarshalIndent(openapi, "", "  ")
		if err != nil {
			stdlog.Errorf("json marshal openai %+v failed: %+v", openapi, err)
			continue
		}
		// 为每个api生成id
		for _, api := range apis {
			api.ID = genToolID(api.Name)
		}
		avatarUrl := parser.GetAvatarUrl()
		baseUrl := strings.ReplaceAll(parser.GetBaseURL(), conf.NamespacePlaceholder, currentNamespace)
		nowSec := time.Now().Unix()
		toolCollection := &api_tools.APIToolCollectionDO{
			BaseInfo: api_tools.APIToolCollectionBaseDO{
				ID:                 genCollectionID(parser.GetAPIName()),
				Name:               parser.GetAPIName(),
				Desc:               parser.GetDesc(),
				APIToolCnt:         int64(len(apis)),
				Released:           ptr.Bool(true),
				CreateTimeSec:      nowSec,
				UpdateTimeSec:      nowSec,
				LastReleaseTimeSec: time.Now().Unix(),
				Type:               api_tools.APIToolTypeBuiltin,
				LogoUrl:            avatarUrl,
				ServerType:         agent_definition.ServerTypeRest,
			},
			Tools:    apis,
			MetaType: api_tools.APIToolMetaTypeJson,
			MetaInfo: openapiBytes,
			BaseURL:  baseUrl,
		}

		// 构建 AgentToolCollection
		agentTools := make([]agent_definition.APIToolDescriber, 0, len(apis))
		for _, api := range apis {
			agentTools = append(agentTools, agent_definition.APIToolDescriber{
				ID:                api.ID,
				BaseURL:           baseUrl,
				Method:            api.Method,
				CollectionHeaders: make(map[string]string), // 如果有全局header，可以在这里设置
				APIPath:           api.Path,
				Name:              api.Name,
				Desc:              api.Desc,
				Params:            api.ParamValues,
				CollectionId:      toolCollection.BaseInfo.ID,
				CollectionName:    toolCollection.BaseInfo.Name,
			})
		}

		toolCollection.AgentToolCollection = &agent_definition.APIToolCollectionDescriber{
			ID:         toolCollection.BaseInfo.ID,
			Name:       toolCollection.BaseInfo.Name,
			Desc:       toolCollection.BaseInfo.Desc,
			AgentTools: agentTools,
		}

		BuiltinTools = append(BuiltinTools, toolCollection)
	}

	return nil
}

func genCollectionID(name string) string {
	return CollectionIDPrefix + string2ID(name)
}

func genToolID(name string) string {
	return ToolIDPrefix + string2ID(name)
}

func string2ID(s string) string {
	hash := md5.Sum([]byte(s))
	return uuid.NewMD5(uuid.Nil, hash[:]).String()[:16]
}
