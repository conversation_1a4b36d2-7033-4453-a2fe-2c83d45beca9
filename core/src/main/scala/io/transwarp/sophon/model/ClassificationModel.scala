package io.transwarp.sophon.model

import java.io.{ObjectInputStream, ObjectOutputStream}

import io.transwarp.hubble.{DataFrameWrapper, LocalFrame, SparkFrame}
import io.transwarp.hubble.meta.{BasicFieldMetaFactory, DataMetaInfo, StructFieldWrapper}
import io.transwarp.aip.commons.dataset.schema.Prediction
import org.apache.spark.ml.attribute.NominalAttribute
import org.apache.spark.ml.{Model, PipelineStage}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataTypes, Metadata, MetadataBuilder, StructField, StructType}
import io.transwarp.hubble.SparkConverters._
import io.transwarp.aip.hubble.local.{LocalTransformer, ModelConverter}

class ClassificationModel(
      pipeline: PipelineStage,
      override val model: Model[_],
      override val fieldsMeta: Array[<PERSON>ruct<PERSON>ield],
      override val mainModelIndex: Int,
      val target: Option[Array[String]])
  extends ModelWrapper {


  def getPipeline: PipelineStage = pipeline
  private var modelCache: Option[LocalTransformer] = None

  override def transform(df: DataFrameWrapper): DataFrameWrapper = {
    df match {
      case spark: SparkFrame =>
        val res = model.transform(spark.df)
        LabelIndexer.genIndexer(res, target)
      case local: LocalFrame =>
        modelCache match {
          case Some(m) =>
            m.transform(local)
          case None =>
            val m = ModelConverter.convert(model)
            modelCache = Some(m)
            m.transform(local)
        }
    }
  }


  override val metaInfo: ModelMetaInfo = new ClassificationModelMeta(model)
}


class ClassificationModelMeta(pipeline: PipelineStage)
  extends ModelMetaInfo(pipeline){
  override def getFeatureSize(meta: DataMetaInfo): Int = {
    meta.getFeatureCols().size
  }

  override def transformMeta(meta: DataMetaInfo): DataMetaInfo = {
    // because classifier transformSchema will check if it is fitting,
    // we have to mock a `label` column and delete it later
    var labelMetadata: Metadata = null
    var wrappedStruct: DataMetaInfo = meta.get(ColumnNames.Label) match {
      case Some(f) =>
        labelMetadata = meta.get(ColumnNames.Label).get.metadata
        meta.remove(ColumnNames.Label)
      case None => meta
    }
    wrappedStruct = wrappedStruct.add(new StructFieldWrapper(
      StructField(ColumnNames.Label, DataTypes.DoubleType, metadata = labelMetadata)))
    val st = pipeline.transformSchema(wrappedStruct.struct)
    val fields = st.fields.filter(_.name != ColumnNames.IndexedLabel)
    val res = new DataMetaInfo(StructType(fields))
    if (wrappedStruct.fields.length != meta.fields.length) {
      res.remove(ColumnNames.Label)
    } else {
      res
    }
  }
}

object ClassificationModel extends BasicModelIOImpl[ClassificationModel] {

  def apply(pipeline: PipelineStage,
            model: Model[_],
            fieldsMeta: Array[StructField],
            mainModelIndex: Int,
            target: Option[Array[String]]
  ): ClassificationModel = {
    new ClassificationModel(pipeline,
     model,
     fieldsMeta,
     mainModelIndex,
     target
    )
  }

  override protected def saveExtrModelInfo(model: ClassificationModel,
                                           outputStream: ObjectOutputStream): Unit = {
    outputStream.writeObject(model.getPipeline)
    outputStream.writeObject(model.target)
  }

  override def generateModel(basicInfo: BasicInfo,
                             inputStream: ObjectInputStream): ClassificationModel = {
    val pipeline = inputStream.readObject().asInstanceOf[PipelineStage]
    val target = inputStream.readObject().asInstanceOf[Option[Array[String]]]
    this.apply(pipeline,
      basicInfo.model,
      basicInfo.fieldsMeta,
      basicInfo.mainModelIndex,
      target)
  }
}

