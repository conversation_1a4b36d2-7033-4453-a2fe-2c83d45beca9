package io.transwarp.sophon.operator.interactive.string

import io.transwarp.sophon.operator.interactive.CoverInputOperator
import org.apache.spark.sql.Column
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataType, DataTypes}

/**
  * Computes the character length of a given string or number of bytes of a binary string.
  * The length of character strings include the trailing spaces. The length of binary strings
  * includes binary zeros.
  */
class LengthOperator extends CoverInputOperator {
  override val supportedTypes = Array(DataTypes.StringType, DataTypes.BinaryType)
  override val dataType: DataType = DataTypes.IntegerType

  override def coverOutputColumn: Column = length(col($(column)))
}
