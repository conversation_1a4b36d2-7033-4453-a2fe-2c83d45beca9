package io.transwarp.sophon.operator.custom

import java.io.{File, FileOutputStream, FileWriter}

import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.exception.{SophonUserException}
import io.transwarp.sophon.execution.ExecutionContext
import io.transwarp.sophon.util.PythonUtils
import io.transwarp.aip.commons.basic.log.Logging
import org.apache.commons.io.IOUtils
import org.apache.spark.api.java.JavaSparkContext
import org.apache.spark.sql.DataFrame
import py4j.GatewayServer

import scala.collection.JavaConverters._
import scala.util.Try

trait Py4j extends Logging {

  def getInputs(): Array[DataFrame]

  private def getPythonPath(): String = {
    val files = PythonUtils.getPythonZips()

    val pythonPath = sys.env.getOrElse(Py4j.PYTHONPATH, "")
      .split(File.pathSeparator)
      .++(files.map(_.getAbsolutePath))
    pythonPath.mkString(File.pathSeparator)
  }

  def valueToString(any: Any): String = {
    any match {
      case array: Array[_] =>
        array.map(_.toString).mkString("[", ",", "]")
      case _ => any.toString
    }
  }

  def getContext: ExecutionContext

  def getParameters: Map[String, String]

  private def initGatewayEntry(): PythonGatewayEntry = {
    val sc = new JavaSparkContext(getContext.spark.sparkContext)
    val sqlContext = getContext.spark
    new PythonGatewayEntry(sc, sqlContext, getParameters.asJava)
  }


  private def createScript(scripts: Array[String]): String = {
    val file = File.createTempFile("py_script", ".py")
    file.deleteOnExit()

    val base = this.getClass.getResourceAsStream("/init.py")
    IOUtils.copy(base, new FileOutputStream(file))

    val writer = new FileWriter(file, true)
    writer.write("\n")
    scripts.foreach(x => writer.write(x))
    writer.close()

    file.getAbsolutePath
  }

  def runWithScripts(scripts: Array[String]): Try[Seq[Any]] = {

    import Py4j._
    Try {
      val entry = initGatewayEntry()
      getInputs().foreach(df => entry.putInput(df))

      val gatewayServer = new GatewayServer(entry, 0)
      gatewayServer.start()
      val port = gatewayServer.getListeningPort
      logInfo(s"start gateway server at port $port")
      // run python script
      val script = createScript(scripts)
      val cmd = Seq(python, script)
      val builder = new ProcessBuilder(cmd.asJava)
      val env = builder.environment()
      env.put(PYTHONPATH, getPythonPath())
      env.put("GATEWAY_SERVER_PORT", port.toString)
      val process = builder.start()

      val output = IOUtils.toString(process.getInputStream, encoding)
      val error = IOUtils.toString(process.getErrorStream, encoding)
      val exitCode = process.waitFor()
      output.split(System.getProperty("line.separator")).foreach(line => logInfo(line))
      if (exitCode != 0) {
        throw new SophonUserException(MessageKey.SCRIPT_EXITCODE,
          cmd.mkString(" "), exitCode.toString, error.toString)
      }
      gatewayServer.shutdown()
      entry.output.toArray
    }
  }
}

object Py4j {
  val python = "python"
  val encoding = "UTF-8"
  val PYTHONPATH = "PYTHONPATH"
}
