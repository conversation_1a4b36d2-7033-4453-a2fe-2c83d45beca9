package io.transwarp.sophon.operator.process

import java.util.UUID

import io.transwarp.aip.commons.basic.params.{IntParam, MapParam, Param, StringParam}
import io.transwarp.hubble.DataFrameWrapper
import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.dag.result._
import io.transwarp.sophon.exception.SophonUserException
import io.transwarp.sophon.execution.ExecutionContext
import io.transwarp.sophon.operator.{ExecutionUnit, OperatorChain}
import io.transwarp.sophon.port._
import org.apache.spark.sql.DataFrame

/**
  * Created by tianming on 4/15/16.
  */

/**
  * This can be the root operator
  */
class ProcessOperator extends OperatorChain {
  val mainUnit = new ExecutionUnit(this, "Main")
  mainUnit.innerSinks.addGroup("result")

  override val subprocesses: Seq[ExecutionUnit] = Seq(mainUnit)
  val globalKey = new StringParam("key", "global key")
  val globalValue = new StringParam("value", "global value")
  val globalVars = new MapParam(globalKey, globalValue,
    "global_macros",
    "global variables", optional = true)
  val maxRows = new IntParam("max_rows",
    "max result dataset rows",
    1, Int.MaxValue,
    optional = true,
    default = Some(100)
  )

  val timeout = new IntParam("timeout", "timeout limit in minute",
    min = 0, default = Some(0), optional = true)

  override def params: Array[Param[_]] = Array(
    globalVars,
    maxRows,
    timeout)

  override def executeInternal(): Unit = {
    if (isDefined(globalVars)) {
      context.setMacros($(globalVars))
    }
    super.executeInternal()
  }

  override def init(context: ExecutionContext): Unit = {
    super.init(context)
    val resultExtender = new PortPairsExtender(
      "result",
      mainUnit.innerSinks,
      outputPorts)

    addExtender(mainUnit, resultExtender)
  }

  def getResult(): DAGResult = {
    val res = outputPorts.find(_.startsWith("result"))
      .map(kv => (kv._1, prepareData(kv._2, kv._1) ) )
    DAGResult(res)
  }

  private def prepareData(output: OutputPort[Any], port: String): Any = {
    val data: Any = output.data
    if (data == null) {
      throw new SophonUserException(MessageKey.CORE_PARAM_IS_NULL, Array(port).mkString(","))
    }

    data match {
      case df: DataFrameWrapper =>
        if (df.df.isStreaming) {
          val tempQueryName = s"kafkaSample${UUID.randomUUID().toString.replace("-", "")}"
          val sqlQuery = df.df.writeStream.outputMode("append")
            .queryName(tempQueryName)
            .format("memory")
            .option("checkpointLocation", s"/tmp/${UUID.randomUUID().toString}")
            .start()
          sqlQuery.awaitTermination(3000)
          val sampleDf = this.context.spark.sql(s"select * from ${tempQueryName}")
          sqlQuery.stop()
          DataFrameWrapper(sampleDf)
        } else {
          val limit = Math.min($(maxRows), this.context.basicConfig.dataPreviewLimit)
          df.limit(limit)
        }
      case df: DataFrame =>
        if (df.isStreaming) {
          DataFrameWrapper(df)
        } else {
          val limit = Math.min($(maxRows), this.context.basicConfig.dataPreviewLimit)
          DataFrameWrapper(df.limit(limit))
        }

      case _ => data
    }
  }

}
