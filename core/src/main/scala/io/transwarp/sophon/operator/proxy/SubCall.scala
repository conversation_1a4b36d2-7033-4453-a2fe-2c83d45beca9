package io.transwarp.sophon.operator.proxy

import io.transwarp.sophon.api.json.flow.OperatorJson
import java.io.IOException

import io.transwarp.aip.commons.basic.utils.JWTokenUtil
import io.transwarp.sophon.execution.ExecutionContext
import io.transwarp.sophon.api.json.response.JobResultJson
import io.transwarp.sophon.api.json.job.{JobUpdate<PERSON>son, JobInfoJson, SubmitJobJson}
import io.transwarp.aip.commons.basic.roles.SophonAdmin
import io.transwarp.sophon.client.SophonHttpClient
import io.transwarp.sophon.commons.proxy.SubServer

class SubCall(context: ExecutionContext,
              val subServer: SubServer,
              resourceMap: Map[String, String]) {
  private val session_timeout = 1000 * 60 * 10

  private def logInfo(msg: String): Unit = {
    context.logInfo(s"${subServer.name}: ${msg}")
  }

  def run(json: SubmitJob<PERSON>son): JobResultJson = {
    logInfo(s"try to connect")
    val client = new SophonHttpClient(subServer.url)
    // TODO
    client.setToken(JWTokenUtil.generateToken(subServer.username, Set[String](SophonAdmin.name)))
    logInfo(s"login in as ${subServer.username}")

    client.waitSessionReady()

    val pids = client.project.getProjectList(0, 20)
    var pid = ""
    if (pids.size > 0) {
      pid = pids.head.id
    } else {
      throw new Exception("no project in sub server")
    }

    val result = client.runJob(replaceResource(json, pid, resourceMap), pid)

    // change result name for display
    result.items.foreach(item => {
      item.name = s"${subServer.name}_${item.name}"
    })
    result
  }

  private def replaceResource(json: SubmitJobJson,
                              pid: String,
                              resourceMap: Map[String, String]): SubmitJobJson = {
    json.pid = pid

    replaceSource(json.flow.root, resourceMap)
    json
  }

  private def replaceSource(op: OperatorJson, resourceMap: Map[String, String]): Unit = {
    if (op.desc == "dataset") {
      val sourceId = op.params.get("source")
      resourceMap.get(sourceId).foreach(subId => op.params.put("source", subId))
    }

    op.units.foreach(u => u.ops.foreach(replaceSource(_, resourceMap)))
  }
}