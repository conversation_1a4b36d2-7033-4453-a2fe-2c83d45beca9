package io.transwarp.sophon.operator.modeling.regression

import io.transwarp.aip.commons.basic.params.{ColumnParam, ParamType}
import io.transwarp.hubble.DataFrameWrapper
import io.transwarp.hubble.SparkConverters._
import io.transwarp.aip.commons.dataset.schema.Label
import io.transwarp.sophon.execution.{ExecutionContext, SparkParamUtil}
import io.transwarp.sophon.meta.MetaRules
import io.transwarp.sophon.model.{RegressionModel, RegressionModelMeta}
import io.transwarp.sophon.operator.{HasModelOutput, Operator}
import org.apache.spark.ml._
import org.apache.spark.sql.types.{DoubleType, StructField}

trait RegressionOperator extends Operator with HasModelOutput {
  val inputPort = inputPorts.getPort[DataFrameWrapper]("train set")
  protected var modelFeatures: String = "features"
  val weightCol = new ColumnParam("weightCol", "weightCol", optional = true,
    portName = inputPort.name, default = Some(""), paramType = ParamType.IOType)

  // here regressor can be either an [[Estimator]] or [[Predictor]]
  protected def buildRegressor(): Estimator[_]
  protected def buildPipeline(labelCol: StructField,
                              regressor: Estimator[_]): (Pipeline, Int) = {

    regressor match {
      case p: Predictor[_, _, _] =>
        p.setLabelCol(labelCol.name)
        p.setFeaturesCol(modelFeatures)
      case _ =>
        SparkParamUtil.setParam(regressor, "labelCol", labelCol.name)
        SparkParamUtil.setParam(regressor, "featuresCol", modelFeatures)
    }
    val p = new Pipeline().setStages(Array(regressor.asInstanceOf[PipelineStage]))
    (p, 0)
  }

  override def init(context: ExecutionContext): Unit = {
    super.init(context)
  }

  def setModelFeatures(featureCols: String): Unit = {
    modelFeatures = featureCols
  }

  override def transformOutputMeta(): Unit = {
    MetaRules.checkHaveFeature(inputPort)
    MetaRules.checkSingleSpecial(inputPort, Label)

    val input = inputPort.getDataMeta
    val features = input.getFeatureCols()
    val label = input.getLabelCol().name

    modelPort.meta = Some(new RegressionModelMeta(buildRegressor()))
  }

  override def executeInternal(): Unit = {
    val rawInput = inputPort.data
    val featuresCols = rawInput.metaInfo.getFeatureCols()
    val labelCol = rawInput.metaInfo.getLabelCol()

    val merged = preprocess(rawInput, featuresCols, labelCol)
    merged.cache()

    val regressor = buildRegressor()
    val (pipeline, index) = buildPipeline(labelCol, regressor)

    val model = pipeline.fit(merged.df)

    val wrapper = new RegressionModel(regressor, model, featuresCols, index)

    modelPort.data = wrapper
  }

  def preprocess(
                  rawInput: DataFrameWrapper,
                  featuresCols: Array[StructField],
                  labelCol: StructField): DataFrameWrapper = {
    val castDouble = rawInput.withColumn(
      labelCol.name,
      rawInput(labelCol.name).cast(DoubleType))
    castDouble.mergeFeatures(featuresCols, "features")
  }
}
