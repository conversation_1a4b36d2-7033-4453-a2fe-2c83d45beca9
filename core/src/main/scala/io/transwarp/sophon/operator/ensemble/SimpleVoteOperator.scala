package io.transwarp.sophon.operator.ensemble

import io.transwarp.aip.commons.basic.params.Param
import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.exception.SophonUserException
import io.transwarp.sophon.model.{ClassificationEnsembleModel, ClassificationEnsembleModelMeta, ModelWrapper}
import io.transwarp.sophon.operator.modeling.classification.ClassificationOperator
import org.apache.spark.ml.{Model, PredictionModel}
import io.transwarp.sophon.operator.ensemble.SimpleVoteOperator._
import io.transwarp.sophon.response.generator.model.EnsembleGenerator
import ml.dmlc.xgboost4j.scala.spark.XGBoostClassificationModel
import org.apache.spark.ml.classification.{ClassificationModel, ProbabilisticClassificationModel}
import org.apache.spark.ml.ensemble.SimpleVoteModel

import scala.collection.mutable.ArrayBuffer


class SimpleVoteOperator extends EnsembleOperator {
  var labels: Option[Array[String]] = None

  override def params: Array[Param[_]] = Array(numArray)

  override def transformOutputMeta(): Unit = {
    val ops = ensembleUnit.operators.filter(op =>
      !(op.isInstanceOf[ClassificationOperator] || op.isInstanceOf[SimpleVoteOperator]))
    if (ops.length >= 1) {
      throw new SophonUserException(MessageKey.CORE_PARAM_ONLYCLASSIFICATION)
    }

    val clas = ensembleUnit.operators.filter(op =>
      (op.isInstanceOf[ClassificationOperator] || op.isInstanceOf[SimpleVoteOperator]))
    if (clas.length == 0) {
      throw new SophonUserException(MessageKey.CORE_PARAM_ONECLASSIFICATION)
    }

    if (ensembleUnit.operators.length != $(numArray).length) {
      throw new SophonUserException(MessageKey.CORE_PARAM_ARRAYNOCOMPLY)
    }

    super.transformOutputMeta()

    modelPort.meta = Some(new ClassificationEnsembleModelMeta(EnsembleOperator.dummyClassifier()))
  }

  override def executeInternal(): Unit = {
    val rawInput = inputPort.data
    rawInput.df.cache()

    val modelArray = ArrayBuffer[Model[_]]()
    val colNamesArray = ArrayBuffer[Array[Option[String]]]()

    ensembleUnit.operators.zipWithIndex.foreach { case (op, index) =>
      for (_ <- 1 to $(numArray)(index)) {
        val port = op.outputPorts.getPort[ModelWrapper]("model")
        inputData.data = EnsembleOperator.bootstrap(rawInput)
        ensembleUnit.innerSources.deliver()
        op.execute()
        modelArray.append(port.data.model)
        colNamesArray.append(genColumnNames(port.data.model))
      }
      if (labels.isEmpty) {
        op match {
          case operator: ClassificationOperator =>
            labels = operator.labels
          case operator: SimpleVoteOperator =>
            labels = operator.labels
          case _ =>
        }
      }
    }
    val model = new SimpleVoteModel(modelArray.toArray, colNamesArray.toArray)

    modelPort.data = new ClassificationEnsembleModel(model, Array(), labels)
  }
}

object SimpleVoteOperator {
  def genColumnNames(model: Model[_]): Array[Option[String]] = {
    val res = ArrayBuffer[Option[String]]()
    val mainModel = EnsembleGenerator.getMainModel(model)
    mainModel match {
      case classifier: ProbabilisticClassificationModel[_, _] =>
        res.append(Some(classifier.getPredictionCol))
        res.append(Some(classifier.getRawPredictionCol))
        res.append(Some(classifier.getProbabilityCol))
      case classifier: XGBoostClassificationModel =>
        res.append(Some(classifier.getPredictionCol))
        res.append(Some(classifier.getRawPredictionCol))
        res.append(None)
      case classifier: ClassificationModel[_, _] =>
        res.append(Some(classifier.getPredictionCol))
        res.append(Some(classifier.getRawPredictionCol))
        res.append(None)
      case classifier: PredictionModel[_, _] =>
        res.append(Some(classifier.getPredictionCol))
        res.append(None)
        res.append(None)
      case _ =>
    }
    res.toArray
  }
}
