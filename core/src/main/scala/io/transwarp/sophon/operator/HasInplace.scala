package io.transwarp.sophon.operator

import io.transwarp.sophon.param.HasInplaceParam
import org.apache.spark.ml.{Estimator, Model, Pipeline}
import org.apache.spark.ml.preprocess.InplaceTransformer
import org.apache.spark.sql.DataFrame

trait HasInplace extends HasInplaceParam {

  // 用于输出model的算子
  protected def buildInplaceTransformPipeline(
      estimator: Estimator[_ <: Model[_]],
      originalCols: Array[String],
      transformedCols: Array[String]): Estimator[_ <: Model[_]] = {
    if ($(inplace)) {
      val inplaceTransformer = new InplaceTransformer()
        .setOriginalCols(originalCols)
        .setTransformedCols(transformedCols)
      new Pipeline().setStages(Array(estimator, inplaceTransformer))
    } else {
      estimator
    }
  }

  // 用于不输出model的算子
  protected def inplaceTransform(
      df: DataFrame,
      originalCols: Array[String],
      transformedCols: Array[String]): DataFrame = {
    if ($(inplace)) {
      new InplaceTransformer()
        .setOriginalCols(originalCols)
        .setTransformedCols(transformedCols)
        .transform(df)
    } else {
      df
    }
  }
}
