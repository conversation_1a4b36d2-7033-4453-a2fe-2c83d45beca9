package io.transwarp.sophon.operator.deeplearning

import java.io.File
import java.nio.ByteBuffer
import java.nio.file.{FileAlreadyExistsException, Files, Paths}
import java.util.Base64

import io.transwarp.aip.commons.dataset.{ConnectionConfig, ConnectionHandlerFactory}
import io.transwarp.hubble.DataFrameWrapper
import io.transwarp.hubble.SparkConverters._
import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.config.BasicConfig
import io.transwarp.sophon.datasource.connection.HdfsConnectionHandler
import io.transwarp.sophon.exception.SophonUserException
import org.apache.spark.{BarrierTaskContext, TaskContext}
import org.apache.spark.ml.linalg.{DenseVector, SparseVector}
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.Row
import org.apache.spark.storage.StorageLevel
import org.lmdbjava.Env
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import scala.sys.process.{Process, ProcessLogger}

private[deeplearning] object DistributedProcess {

  def train(processedData: DataFrameWrapper,
            pythonTask: DLPythonTask,
            connHandler: HdfsConnectionHandler): String = {
    val inputRDD = processedData.rdd
    val exitCode = trainDistributed(inputRDD, pythonTask)
    if (exitCode != 0) {
      throw new SophonUserException(
        MessageKey.CORE_PROCESS_TRAINPROCESSFAILED, exitCode.toString)
    }
    val sep = File.separator
    val frozenGraph = s"${pythonTask.checkpointDirURL}${sep}" +
      s"export${sep}frozen_graph.pb"
    connHandler.copyToLocalFile(false, frozenGraph, frozenGraph)
    frozenGraph
  }

  def trainDistributed(inputRDD: RDD[Row],
                       pythonTask: DLPythonTask): Int = {
    val sc = inputRDD.sparkContext
    // val CPUS_PER_TASK = sc.getConf.getInt("spark.task.cpus", 1)
    val waitingTimebc = sc.broadcast(pythonTask.waitingTime)

    val indexedData = inputRDD.zipWithUniqueId()
    val processedRDD = indexedData.repartition(pythonTask.coreNum).mapPartitions{x =>
        // todo Jira:spark-24818 solve barrier mode locality problem. keep on eye on it.
      Thread.sleep(1000 * waitingTimebc.value)
      x}.persist(StorageLevel.MEMORY_AND_DISK)
    processedRDD.count()

    val trainProcess = processedRDD.barrier().mapPartitions {
      partitionData =>
        val barrierTask = BarrierTaskContext.get()
        pythonTask.setTaskID(barrierTask.partitionId())
        val taskRunner = new DistributedTaskRunner(pythonTask, barrierTask, partitionData)
        taskRunner.pullOnnxFromMaster()
        taskRunner.saveData()

        // todo  fault tolerance , checkout folder or another mappartition
        barrierTask.barrier()
        if (barrierTask.partitionId() == 0) {
          val command = taskRunner.genMPICommand()
          taskRunner.mpirun(command)
        }
        taskRunner.exitCode
    }
    val exitCode = trainProcess.collect().head
    processedRDD.unpersist()
    exitCode
  }

  def saveData(input: Iterator[(Row, Long)],
               isImage: Boolean,
               pathURL: String,
               bufferSize: Int = 1024000): Int = {

    val path = Paths.get(pathURL)
    if (!Files.exists(path)) {
        Files.createDirectories(path)
    }
    val tmpFolder = path.toFile
    tmpFolder.deleteOnExit()
    val log: Logger = LoggerFactory.getLogger("distirbuted DL")
    log.info(s"lmdb data generated in ${tmpFolder.getAbsolutePath}")

    val env = Env.create()
      .setMapSize(1024*1024*1024*1)
      .setMaxDbs(1)
      .open(tmpFolder)

    val dbName: String = null
    val db = env.openDbi(dbName)
    val key = ByteBuffer.allocateDirect(env.getMaxKeySize)
    val value = ByteBuffer.allocateDirect(bufferSize)
    var featureLength = -1
    // todo: combine Image and not Image, Tensor, shape type
    if (isImage) {
      input.foreach { case (r, index) =>
        key.clear()
        value.clear()
        key.put(index.toString.getBytes).flip()
        val imageBytes = r.get(0).asInstanceOf[Array[Byte]]
        if (featureLength == -1){
          featureLength = imageBytes.length
        }
        val label = r.getDouble(1)
        val lmdbData = Array(Base64.getEncoder.encodeToString(imageBytes), label)
        val data = lmdbData.mkString(",")
        value.put(data.getBytes).flip()
        db.put(key, value)
      }
    } else {
      input.foreach { case (r, index) =>
        key.clear()
        value.clear()
        key.put(index.toString.getBytes()).flip()
        var valueArray = Array[Double]()
        val feature = r.get(0)
        if (feature.isInstanceOf[DenseVector]) {
          valueArray = feature.asInstanceOf[DenseVector].toArray
        } else {
          valueArray = feature.asInstanceOf[SparseVector].toArray
        }
        if (featureLength == -1){
          featureLength = valueArray.length
        }
        val label = r.getDouble(1)
        val data = (valueArray ++ Array(label)).mkString(",")
        value.put(data.getBytes).flip()
        db.put(key, value)
      }
    }
    db.close()
    featureLength
  }
}

class TaskInspector(trainProcess: Process, taskContext: TaskContext) extends Runnable {
  override def run(): Unit = {
      while (!Thread.interrupted()) {
        if (taskContext.isInterrupted()) {
          // isInterrupted check volatile variable
          // @volatile private var reasonIfKilled: Option[String] = None
          trainProcess.destroy()
          Thread.currentThread().interrupt()
        }
        // inspect task status per 500ms
      }
  }
}

class DistributedTaskRunner(pythonTask: DLPythonTask,
                            barrierTask: BarrierTaskContext,
                            partitionData: Iterator[(Row, Long)]) {

  var exitCode: Iterator[Int] = Iterator.empty

  def setExitCode(code: Int): Unit = {
    exitCode = Iterator.single(code)
  }

  val conn = new ConnectionConfig("hdfs-test", "hdfs-test",
    ConnectionConfig.Hdfs, Map("basePath" -> "/"))
  val basicConfig = new BasicConfig
  val connHandler: HdfsConnectionHandler = ConnectionHandlerFactory
    .build(conn.category, conn)
    .asInstanceOf[HdfsConnectionHandler]

  def pullOnnxFromMaster(): Unit = {
    val onnxFileURL = pythonTask.onnxFileURL
    val onnxFilePath = Paths.get(onnxFileURL)
    if (!Files.exists(onnxFilePath)) {
      try {
        connHandler.copyToLocalFile(false, onnxFileURL, onnxFileURL)
      } catch {
        case _: FileAlreadyExistsException =>
      }
    }
  }

  /**
    * This function will be rewritten in Sophon-3.0
    */
  def saveData(bufferSize: Int = 1024000): Unit = {

    val input = partitionData
    val isImage = pythonTask.isImage
    val pathURL = pythonTask.genTaskDataDirURL

    val path = Paths.get(pathURL)
    if (!Files.exists(path)) {
      Files.createDirectories(path)
    }
    val tmpFolder = path.toFile
    tmpFolder.deleteOnExit()
    val log: Logger = LoggerFactory.getLogger("distirbuted DL")
    log.info(s"lmdb data generated in ${tmpFolder.getAbsolutePath}")

    val env = Env.create()
      .setMapSize(1024 * 1024 * 1024 * 1)
      .setMaxDbs(1)
      .open(tmpFolder)

    val dbName: String = null
    val db = env.openDbi(dbName)
    val key = ByteBuffer.allocateDirect(env.getMaxKeySize)
    val value = ByteBuffer.allocateDirect(bufferSize)
    var featureLength = -1
    // todo: combine Image and not Image, Tensor, shape type
    if (isImage) {
      input.foreach { case (r, index) =>
        key.clear()
        value.clear()
        key.put(index.toString.getBytes).flip()
        val imageBytes = r.get(0).asInstanceOf[Array[Byte]]
        if (featureLength == -1) {
          featureLength = imageBytes.length
        }
        val label = r.getDouble(1)
        val lmdbData = Array(Base64.getEncoder.encodeToString(imageBytes), label)
        val data = lmdbData.mkString(",")
        value.put(data.getBytes).flip()
        db.put(key, value)
      }
    } else {
      input.foreach { case (r, index) =>
        key.clear()
        value.clear()
        key.put(index.toString.getBytes()).flip()
        var valueArray = Array[Double]()
        val feature = r.get(0)
        if (feature.isInstanceOf[DenseVector]) {
          valueArray = feature.asInstanceOf[DenseVector].toArray
        } else {
          valueArray = feature.asInstanceOf[SparseVector].toArray
        }
        if (featureLength == -1) {
          featureLength = valueArray.length
        }
        val label = r.getDouble(1)
        val data = (valueArray ++ Array(label)).mkString(",")
        value.put(data.getBytes).flip()
        db.put(key, value)
      }
    }
    db.close()
    pythonTask.setFeatureLength(featureLength)
  }

  def genMPICommand(): Array[String] = {
    val info = barrierTask.getTaskInfos()
    val containerAddress = info.map(_.address.toString.split(":")(0))
    val mpiProcess: Array[String] = containerAddress.zipWithIndex.flatMap {
      case (address, partitionID) =>

        val conjunction: Array[String] =
          if (partitionID < containerAddress.length - 1) {Array(":")}
          else {Array[String]()}

        Array("-np", "1",
          "-host", address,
          pythonTask.python, pythonTask.scriptPath,
          "--partitionID", partitionID.toString,
          "--featureSize", pythonTask.featureLength.toString,
          "--data", pythonTask.dataDirURL + partitionID.toString) ++
          pythonTask.pythonParams ++ conjunction
    }
    Array("mpirun",
      "--allow-run-as-root") ++ mpiProcess
  }

  def mpirun(command: Array[String]): Unit = {
    val trainProcessBuilder = Process(command.toSeq)
    val log: Logger = LoggerFactory.getLogger("Distributed DL")
    val trainProcess = trainProcessBuilder.run(ProcessLogger(log.info(_)))
    val inspectThread = new Thread (new TaskInspector(trainProcess, barrierTask))
    inspectThread.setDaemon(true)
    var processExitCode = -1

    try {
      inspectThread.start()
      processExitCode = trainProcess.exitValue()
      setExitCode(processExitCode)
    }
      finally {
      inspectThread.interrupt()
    }

    if (processExitCode != 0) {
      log.error("MPI Task Failed:" + command.mkString(" "))
    }

    // upload frozen model to hdfs
    val sep = File.separator
    val frozenGraph = pythonTask.checkpointDirURL + sep +
      s"export${sep}frozen_graph.pb"
    connHandler.copyFromLocalFile(false, frozenGraph, frozenGraph)
  }
}