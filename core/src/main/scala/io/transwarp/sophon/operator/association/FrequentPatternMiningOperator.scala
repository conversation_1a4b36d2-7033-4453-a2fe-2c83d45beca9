package io.transwarp.sophon.operator.association

import io.transwarp.hubble.DataFrameWrapper
import io.transwarp.hubble.meta.DataMetaInfo
import io.transwarp.sophon.meta.MetaRules

/**
  * Created by lin<PERSON> on 16-6-15.
  */
trait FrequentPatternMiningOperator extends AssociationOperator {
  override val outputPort = outputPorts.getPort[DataFrameWrapper]("frequent itemset")

  /**
    * just like the `transformSchema` method in spark ml,
    * validate the input meta info and transform meta info to output
    */
  override def transformOutputMeta(): Unit = {
    MetaRules.checkContainColumn(inputPort, ColumnNames.Transaction)
    val transformer = buildTransformer()
    val metaArray = transformer.transformSchema(inputPort.getDataMeta.toStructType())
    val output = DataMetaInfo.fromStructType(metaArray)
    outputPort.meta = Some(output)
  }


}
