package io.transwarp.sophon.operator.transform

import io.transwarp.aip.commons.basic.params.{Param, StringParam}
import io.transwarp.hubble.SparkConverters._
import io.transwarp.hubble.meta.{BasicFieldMetaFactory, DataMetaInfo}
import io.transwarp.aip.commons.dataset.schema.Id
import io.transwarp.hubble.DataFrameWrapper
import io.transwarp.sophon.api.resource.MessageKey
import io.transwarp.sophon.exception.SophonUserException
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataTypes, StructField}

class GenerateIDFeatureOperator extends TransformOperator {

  override val supportStream: Boolean = true

  val generatedColumnName = new StringParam("id column", "id column name")

  override def params: Array[Param[_]] = Array(
    generatedColumnName
  )

  private def idField =
    StructField($(generatedColumnName), DataTypes.StringType)
      .withFieldMeta(BasicFieldMetaFactory.defaultMeta.withRole(Id))

  override def transformOutputMeta(): Unit = {
    inputPort.getDataMeta.add(idField)
    outputPort.meta = Some(DataMetaInfo.fromStructType(inputPort.getDataMeta.add(idField)))
  }

  override def transform(dataset: DataFrameWrapper): DataFrameWrapper = {
    val fields = dataset.schema.fieldNames

    val columnName = $(generatedColumnName)

    if (!fields.contains(columnName)) {
      dataset
        .withColumn(columnName, monotonically_increasing_id())
        .withFieldMeta(columnName, BasicFieldMetaFactory.defaultMeta.withRole(Id))
    } else {
      throw new SophonUserException(MessageKey.CORE_RESOURCE_ALREADY_EXIST, columnName)
    }
  }
}
