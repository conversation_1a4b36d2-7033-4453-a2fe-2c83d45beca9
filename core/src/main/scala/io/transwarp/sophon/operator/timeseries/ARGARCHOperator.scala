package io.transwarp.sophon.operator.timeseries

import io.transwarp.aip.commons.basic.params.{BooleanParam, IntParam, Param, ColumnParam}
import io.transwarp.aip.commons.dataset.schema.Label
import io.transwarp.hubble.SparkConverters._
import io.transwarp.sophon.meta.MetaRules
import io.transwarp.sophon.model.{RegressionModelMeta, RegressionModel}
import io.transwarp.sophon.operator.modeling.regression.RegressionOperator
import org.apache.spark.ml.Model
import org.apache.spark.ml.timeseries.models.ARGARCH
import org.apache.spark.sql.types.DoubleType

class ARGARCHOperator extends RegressionOperator {
  val M = new IntParam("m", "AR lag", 0, 10, false)
  val P = new IntParam("p", "volatility lag", 0, 10, false)
  val Q = new IntParam("q", "residual lag", 0, 5, false)
  val timeColumn = new ColumnParam("time column", "time column", portName = inputPort.name)
  val maxEval = new IntParam("max eval", "Max eval time", 0, Int.MaxValue, default = Some(10000))
  val maxIter = new IntParam("max iter", "Max iter time", 0, Int.MaxValue, default = Some(10000))
  val noIntercept = new BooleanParam("no intercept",
    "using incepter or not", default = Some(false))

  override def params: Array[Param[_]] = Array(
    M,
    P,
    Q,
    timeColumn,
    maxEval,
    maxIter,
    noIntercept
  )

  override def transformOutputMeta(): Unit = {
    MetaRules.checkHaveFeature(inputPort)
    MetaRules.checkSingleSpecial(inputPort, Label)

    modelPort.meta = Some(new RegressionModelMeta(buildRegressor()))
  }

  override protected def buildRegressor() = {
    new ARGARCH()
      .setTimeCol($(timeColumn))
      .setMaxEval(${maxEval})
      .setMaxIter(${maxIter})
      .setM(${M})
      .setP(${P})
      .setQ(${Q})
  }

  override def executeInternal(): Unit = {
    val rawInput = inputPort.data

    val labelCol = rawInput.metaInfo.getLabelCol()

    val casted = rawInput.withColumn(labelCol.name, rawInput(labelCol.name).cast(DoubleType))

    val regressor = buildRegressor()
      .setTimeSeriesCol(labelCol.name)
      .setLabelCol(labelCol.name)

    val out = regressor.fit(casted.df)
    val model = out.asInstanceOf[Model[_]]
    val wrapper = new RegressionModel(regressor, model, Array(), -1)

    modelPort.data = wrapper
  }
}
