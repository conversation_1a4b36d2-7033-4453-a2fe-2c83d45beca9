package io.transwarp.sophon.response.generator.model

import io.transwarp.sophon.api.json.model.{Gaussian<PERSON><PERSON><PERSON><PERSON>, Model<PERSON>son, GaussianMixedModelJson}
import org.apache.spark.ml.clustering.GaussianMixtureModel

/**
  * Created by endy on 16-9-21.
  */
class GaussianMixedGenerator extends ModelGenerator[GaussianMixtureModel]{
  override def model2Json(outputModel: GaussianMixtureModel): ModelJson = {
    val dists = outputModel.gaussians.map(g => {
      val dist = new GaussianDistJson()
      dist.mean = g.mean.toArray
      dist.cov = g.cov.toArray
      dist
    })
    new GaussianMixedModelJson(dists)
  }
}
