package io.transwarp.sophon.response.generator.model

import io.transwarp.sophon.api.json.model.{BoostRegressor<PERSON>son, ModelJson}
import io.transwarp.sophon.operator.XGBoostFeatureImportanceUtil
import ml.dmlc.xgboost4j.scala.spark.ImprovedXGBoostRegressionModel

import scala.collection.JavaConverters._

class XGBoostRegressorGenerator extends ModelGenerator[ImprovedXGBoostRegressionModel]{
  override def model2Json(outputModel: ImprovedXGBoostRegressionModel): ModelJson = {
    val model = new BoostRegressorJson()
    model.numClass = 0
    model.featureScores = outputModel.nativeBooster.getFeatureScore().asJava
    model
  }

  override def calculateImportance(outputModel: ImprovedXGBoostRegressionModel): Array[Double] = {
    val rawMapScores = outputModel.nativeBooster.getFeatureScore()
    rawMapScores.toArray
      .sortWith(XGBoostFeatureImportanceUtil.sortByFeatureIndex)
      .map(_._2.doubleValue())
  }

  override def postGenerateJson(json: ModelJson): ModelJson = {
    val modelJson = json.asInstanceOf[BoostRegressorJson]
    modelJson.featureScores = json.features.zip(
      json.importances.map(i => new Integer(i.intValue()))).toMap.asJava
    val sumImportance = json.importances.sum
    json.importances = json.importances.map(_ / sumImportance)
    json
  }
}
