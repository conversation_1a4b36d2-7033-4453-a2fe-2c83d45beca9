def fit(points: Array[BVLabeledPoint]): LinearRegressionModel = {
  assert(points.head.features.size == featureSize)
  val trainData = if (useBias) {
    points.map(p => BVLabeledPoint(p.label, BDV[Double](p.features.toArray :+ 1d)))
  } else points.map(p => BVLabeledPoint(p.label, BDV[Double](p.features.toArray)))

  val initWeight =
     if (useBias) BDV.rand[Double](featureSize + 1, Rand.gaussian)
     else BDV.rand[Double](featureSize, Rand.gaussian)
  val optimizer = new LBFGS[BV[Double]](maxIter = 100, m = 4)
  val loss = new SquareLossDiffFunction(trainData)
  val lossWithL2 = if (lambdaL2 == 0) loss else DiffFunction.withL2Regularization(loss, lambdaL2)
  val lossRecorder = new mutable.ArrayBuilder.ofDouble

  val states = optimizer.iterations(lossWithL2, initWeight)

  var state: optimizer.State = null
  while (states.hasNext) {
    state = states.next()
    lossRecorder += state.adjustedValue
  }

  val w = state.x
  new LinearRegressionModel(w(0 until featureSize),
  if (useBias) w(-1) else 0d, lossRecorder.result())
}