package annotation

import (
	"archive/zip"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"github.com/iancoleman/strcase"
	"io"
	"os"
	"strings"
	"sync"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/cvat-backend/common"
	"transwarp.io/applied-ai/aiot/cvat-backend/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdname"

	"transwarp.io/applied-ai/aiot/cvat-backend/dao"
	"transwarp.io/applied-ai/aiot/cvat-backend/dao/query"
	"transwarp.io/applied-ai/aiot/cvat-backend/helper"
	"transwarp.io/applied-ai/aiot/cvat-backend/models"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

// 将AnnotationFamily 改名为 AnnotationSet 以符合实际意思 ———— 标注集
// mysql的表名暂时不做修改
// api接口的path名暂时不做修改
type AnnotationSetManager interface {
	// CreateSet 创建标注集
	CreateSet(as *models.AnnotationSet) error
	// DeleteSet 批量删除标注集
	DeleteSet(projectId string, setIds ...int32) error
	GetSet(setId int32) (*models.AnnotationSet, error)
	// UpdateSet 更新标注集
	UpdateSet(as *models.AnnotationSet) error
	// ListAll 获取所有标注集
	ListAll(projectId string) ([]*models.AnnotationSet, error)
	// ListSetsByType 根据数据类型获取标注集
	ListSetsByType(projectId string, dataType ...string) ([]*models.AnnotationSet, error)

	// TestSplit 测试集切分
	TestSplit(setId int32, prop *models.TestSplitProp) ([]int32, error)
	// CalSplitNum 计算测试集切分熟练
	CalSplitNum(setId int32, prop *models.TestSplitProp) (int32, error)
	// CalSplitNumWithoutSet 直接通过数量计算切分数量
	CalSplitNumWithoutSet(prop *models.TestSplitProp) (int32, error)
	// ChangeTestStatus 移入移除测试图片
	ChangeTestStatus(setId int32, objIds []int32, changeType helper.ChangeTestType) error
	// FilterObjsInProcess 获取id的数组
	// 如果status是test，获取测试集的切分列表
	// 如果member为空，获取全部列表
	// 如果member不为空，根据member查询标注列表
	FilterObjsInProcess(setId int32, status, member string, test bool) ([]int32, error)
	// ExportSetsPackage 导出标注集中的标注任务的标注结果（算法调用)
	ExportSetsPackage(sid int32, onlyAnnotation bool, allJpg bool, onlyXml bool, outputFormat string, zos *zip.Writer) error
	// ExportSetPackageByTask 根据标注任务id导出标注
	ExportSetPackageByTask(taskId int32, onlyAnnotation bool, allJpg bool, onlyXml bool, outputFormat string, zos *zip.Writer) error
	// ExportTestPackageBySetId 下载测试集（算法调用）
	ExportTestPackageBySetId(sid int32, zos *zip.Writer) error
	// ExportTestPackageByTaskId TODO 该方法需要先生成标注文件，生成文件未实现
	ExportTestPackageByTaskId(taskId int32, zos *zip.Writer) error
	// ListWithVersion 根据type类型获取所有标注集以及版本
	ListWithVersion(projectId string, t ...string) ([]*models.AnnotationSet, error)
	// GetAnnotationSetCloneName 获取克隆的新标注集的默认名称
	GetAnnotationSetCloneName(projectId string, setId int32, targetProjectId string) (string, error)
	// CloneAnnotationSet 克隆标注集到指定项目
	CloneAnnotationSet(projectId string, setId int32, annoSet *models.AnnotationSet) error
	AddDataToAnnotationSet(ctx context.Context, as *models.AnnotationSet, path string) error
	// GetAnnotationSetStats 获取标注集统计信息
	GetAnnotationSetStats(projectId string) (*models.DatasetStats, error)
	// IncreaseAnnotationSetVisits 增加标注集访问量
	IncreaseAnnotationSetVisits(projectId string, setId int32, operation models.OperationType) error
	// ListAnnotationSetByPage 获取标注集列表，分页
	ListAnnotationSetByPage(req *models.SamplePageReq) (*models.Page, error)
}

var (
	asm     AnnotationSetManager
	asmOnce sync.Once
)

func GetAnnotationSetManager() AnnotationSetManager {
	asmOnce.Do(func() {
		asm = &annotationSetManager{
			Q:       dao.InitQuery(),
			factory: NewAnnotationSetFactory(dao.InitQuery()),
		}
	})
	return asm
}

type annotationSetManager struct {
	Q       *query.Query
	factory *AnnotationSetFactory
}

func (a annotationSetManager) GetSet(setId int32) (*models.AnnotationSet, error) {
	repo := a.Q.AnnotationSet
	return repo.Select(repo.ALL).Where(repo.ID.Eq(setId)).First()
}

func (a annotationSetManager) CreateSet(as *models.AnnotationSet) error {
	var err error
	asq := a.Q.AnnotationSet
	exist, err := asq.Where(asq.Name.Eq(as.Name), asq.ProjectID.Eq(as.ProjectID)).Find()
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager: CreateSet method, query set error")
	}
	if len(exist) > 0 {
		return common.ANNOTATION_FAMILY_NAME_ALREADY_EXIST.SetArgs(as.Name)
	}
	// 用户创建的标注集默认均为内置
	as.AssetType = pb.AssetType_ASSET_EMBEDDED
	// 默认为训练集
	if as.DataUsage == "" {
		as.DataUsage = models.DataUsageTraining
	}
	handler, err := a.factory.GetHandler(as.DataType)
	if err != nil {
		return err
	}
	return handler.CreateAnnotationSet(as)
}

func (a annotationSetManager) AddDataToAnnotationSet(ctx context.Context, as *models.AnnotationSet, path string) error {
	repo := a.Q.AnnotationSet
	existSet, err := repo.Where(repo.ID.Eq(as.ID), repo.ProjectID.Eq(as.ProjectID)).First()
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager: AddDataToAnnotationSet method, query annotation set by id [%d]", as.ID)
	}
	if existSet.AnnotationType != "" && existSet.AnnotationType != as.AnnotationType {
		return stderr.BadRequest.Error("Inconsistent annotation type: %s", as.AnnotationType)
	}
	handler, err := a.factory.GetHandler(as.DataType)
	if err != nil {
		return err
	}
	return handler.AddDataToAnnotationSet(ctx, as, path)
}

func (a annotationSetManager) DeleteSet(projectId string, setIds ...int32) error {
	repo := a.Q.AnnotationSet
	_, err := repo.Where(repo.ProjectID.Eq(projectId), repo.ID.In(setIds...)).Delete()
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager: DeleteSet method, delete error")
	}
	return nil
}

func (a annotationSetManager) UpdateSet(as *models.AnnotationSet) error {
	repo := a.Q.AnnotationSet
	if as.Name != "" {
		all, err := repo.Where(repo.Name.Eq(as.Name), repo.ProjectID.Eq(as.ProjectID)).Find()
		if err != nil {
			return stderr.Wrap(err, "UpdateSet method error")
		}
		if all != nil && len(all) > 0 {
			for _, exist := range all {
				if exist.ID != as.ID {
					return common.ANNOTATION_FAMILY_NAME_ALREADY_EXIST.SetArgs(as.Name)
				}
			}
		}
		_, err = repo.Select(repo.Name, repo.Description, repo.DataUsage, repo.Labels).Where(repo.ID.Eq(as.ID)).Updates(*as)
		if err != nil {
			return stderr.Wrap(err, "AnnotationSetManager: UpdateSet method error")
		}
	}
	return nil
}

func (a annotationSetManager) ListAll(projectId string) ([]*models.AnnotationSet, error) {
	repo := a.Q.AnnotationSet
	ret, err := repo.Where(repo.ProjectID.Eq(projectId)).Select(repo.ALL).Order(repo.CreateTime.Desc()).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "ListAll method error")
	}
	return ret, nil
}

func (a annotationSetManager) ListSetsByType(projectId string, dataType ...string) ([]*models.AnnotationSet, error) {
	repo := a.Q.AnnotationSet
	condition := repo.WithContext(context.Background()).Where(repo.ProjectID.Eq(projectId))
	if len(dataType) > 0 {
		condition = condition.Where(repo.DataType.In(dataType...))
	}
	ret, err := condition.Select(repo.ALL).Order(repo.CreateTime.Desc()).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "ListSetsByType method error")
	}
	return ret, nil
}

func (a annotationSetManager) TestSplit(setId int32, prop *models.TestSplitProp) ([]int32, error) {
	repo := a.Q.AnnotationSet
	ids, err := GetAnnotationSetObjManager().FindNotTestIds(setId)
	if err != nil {
		return nil, stderr.Wrap(err, "AnnotationSetManager: TestSplit method, find not test id error, set id [%d]", setId)
	}
	testIds, _ := models.GetSplitter(prop.SplitType).Split(prop.Params, ids)
	err = GetAnnotationSetObjManager().UpdateObjTestStatus(setId, testIds, true)
	if err != nil {
		return nil, err
	}
	as, _ := repo.Where(repo.ID.Eq(setId)).First()
	testSize := int(as.TestQuantity) + len(testIds)
	_, err = repo.Where(repo.ID.Eq(setId)).Update(repo.TestQuantity, testSize)
	if err != nil {
		return nil, stderr.Wrap(err, "AnnotationSetManager: TestSplit method, update set [%d] error.", setId)
	}
	return testIds, err
}

func (a annotationSetManager) CalSplitNum(setId int32, prop *models.TestSplitProp) (int32, error) {
	repo := a.Q.AnnotationSet
	as, err := repo.Where(repo.ID.Eq(setId)).First()
	if err != nil {
		return 0, stderr.Wrap(err, "CalSplitNum method error")
	}
	return models.GetSplitter(prop.SplitType).Cal(prop.Params, as.Quantity-as.TestQuantity)
}

func (a annotationSetManager) CalSplitNumWithoutSet(prop *models.TestSplitProp) (int32, error) {
	return models.GetSplitter(prop.SplitType).Cal(prop.Params, prop.Size)
}

func (a annotationSetManager) ChangeTestStatus(setId int32, objIds []int32, changeType helper.ChangeTestType) error {
	repo := a.Q.AnnotationSet
	switch changeType {
	case helper.ChangeTestIn:
		err := GetAnnotationSetObjManager().UpdateObjTestStatus(setId, objIds, true)
		if err != nil {
			return stderr.Wrap(err, "AnnotationSetManager: ChangeAnnotationSetTestSet method, change test true error.")
		}
		_, err = repo.Where(repo.ID.Eq(setId)).UpdateSimple(repo.TestQuantity.Add(int32(len(objIds))))
		if err != nil {
			return stderr.Wrap(err, "AnnotationSetManager: ChangeAnnotationSetTestSet method, change set [%d] test size error.")
		}
	case helper.ChangeTestOut:
		err := GetAnnotationSetObjManager().UpdateObjTestStatus(setId, objIds, false)
		if err != nil {
			return stderr.Wrap(err, "AnnotationSetManager: ChangeAnnotationSetTestSet method, change test false error.")
		}
		_, err = repo.Where(repo.ID.Eq(setId)).UpdateSimple(repo.TestQuantity.Sub(int32(len(objIds))))
		if err != nil {
			return stderr.Wrap(err, "AnnotationSetManager: ChangeAnnotationSetTestSet method, change set [%d] test size error.")
		}
	}
	return nil
}

func (a annotationSetManager) FilterObjsInProcess(setId int32, status, member string, test bool) ([]int32, error) {
	if status == "test" {
		objs, err := GetAnnotationObjManager().ListObjBySetIdAndTest(setId, test)
		if err != nil {
			return nil, stderr.Wrap(err, "FilterObjsInProcess method error, setId [%d]", setId)
		}
		ret := make([]int32, len(objs))
		for i, obj := range objs {
			ret[i] = obj.ID
		}
		return ret, nil
	}
	if member == "" {
		ret, err := GetAnnotationJobManager().ListObjIdsBySetId(setId)
		if err != nil {
			return nil, stderr.Wrap(err, "FilterObjsInProcess method error, setId [%d] and annotator [%s].", setId, member)
		}
		return ret, nil
	} else {
		ret, err := GetAnnotationJobManager().ListObjIdsBySetIdAndAnnotator(setId, member)
		if err != nil {
			return nil, stderr.Wrap(err, "FilterObjsInProcess method error, setId [%d] and annotator [%s].", setId, member)
		}
		return ret, nil
	}
}

// Deprecated 根据数据集id（对应annotation_family表中的ID）导出
func (a annotationSetManager) exportSetsPackageByFile(sid int32, onlyAnnotation bool, allJpg bool, onlyXml bool,
	outputFormat string, zos *zip.Writer) error {
	annoSet, err := a.GetSet(sid)
	if err != nil {
		return stderr.Wrap(err, "ExportSetsPackage: a.GetSet error, setId [%d]", sid)
	}
	var annoFiles []*models.FileInfoJson
	if onlyXml {
		annoFiles, err = a.getAnnoFilesByTest(annoSet, false)
	} else {
		annoFiles, err = a.getAllAnnoFiles(annoSet)
	}
	if err != nil {
		return err
	}
	// outputFormat == "" 时为算法调用，目前只实现算法调用的情况，不做格式转换
	if outputFormat == "" {
		f := func(path string) string {
			name := path[strings.LastIndex(path, "/")+1:]
			filePath := fmt.Sprintf("%s/%s", "Annotations", name)
			return filePath
		}
		err = addAnnoFilesToZip(annoFiles, zos, f)
		if err != nil {
			return err
		}
	}
	if !onlyAnnotation {
		var imageFiles map[*models.FileInfoJson]*models.AnnotationObj
		if onlyXml {
			imageFiles, err = a.getImageFilesByTest(annoSet, false)
		} else {
			imageFiles, err = a.getAllImageFiles(annoSet)
		}

		buildPath := func(path string, obj *models.AnnotationObj) string {
			name := path[strings.LastIndex(path, "/")+1:]
			annoIndex := strings.LastIndex(obj.OutputName, ".")
			random := obj.OutputName[annoIndex-3 : annoIndex]
			index := strings.LastIndex(name, ".")
			filePath := fmt.Sprintf("%s/%s-%s%s", "JPEGImages", name[:index], random, name[index:])
			if allJpg {
				filePath = fmt.Sprintf("%s.jpg", filePath[:strings.LastIndex(filePath, ".")])
			}
			return filePath
		}

		err = addImageFilesToZip(imageFiles, zos, buildPath)
		if err != nil {
			return err
		}
	}
	return nil
}

// ExportSetPackageByTask 根据任务id（对应annotation_set表中的ID）导出
func (a annotationSetManager) ExportSetPackageByTask(taskId int32, onlyAnnotation bool, allJpg bool, onlyXml bool,
	outputFormat string, zos *zip.Writer) error {
	aoq := a.Q.AnnotationObj
	annotationObjs, err := aoq.Where(aoq.TaskID.Eq(taskId)).
		Preload(aoq.ImageObj).Preload(aoq.AnnotationTask).
		Preload(aoq.ObjLabels).Preload(aoq.ObjLabels.Label).Find()
	if err != nil {
		return stderr.Wrap(err, "ExportSetPackageByTask: GetAnnotationObj by taskId error, taskId [%d]", taskId)
	}
	annoObjs := make([]*models.AnnotationObj, len(annotationObjs))
	for i, obj := range annotationObjs {
		annoObjs[i] = helper.FillColumns(obj)
	}
	for _, annoObj := range annoObjs {
		annoFilePath := fmt.Sprintf("%s/%s", "Annotations", annoObj.OutputName)
		err = a.writeAnnoResultToZipStream(annoObj, zos, annoFilePath)
		if err != nil {
			return err
		}
		if !onlyAnnotation {
			path := annoObj.ImageObj.Path
			if !helper.ValidateImagePath(path) {
				return stderr.Error("ExportSetPackageByTask method, image path %s is invalid", path)
			}
			name := path[strings.LastIndex(path, "/")+1:]
			index := strings.LastIndex(name, ".")
			imageFilePath := fmt.Sprintf("%s/%s-%s%s", "JPEGImages", name[:helper.AnnotationFilePrefixLength], helper.TrimExtension(annoObj.ImageObj.Name), name[index:])
			if allJpg {
				imageFilePath = fmt.Sprintf("%s.jpg", imageFilePath[:strings.LastIndex(imageFilePath, ".")])
			}
			err = a.writeImagesToZipStream(annoObj, zos, imageFilePath)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func addImageFilesToZip(imageFiles map[*models.FileInfoJson]*models.AnnotationObj, zos *zip.Writer,
	buildPath func(string, *models.AnnotationObj) string) error {
	for fileInfo, annotationObj := range imageFiles {
		buffer := make([]byte, 1024)
		if !fileInfo.IsDir {
			filePath := buildPath(fileInfo.Path, annotationObj)

			zosWriter, err := zos.Create(filePath)
			if err != nil {
				return stderr.Wrap(err, "failed to add file %s to zip", filePath)
			}
			nfsFilePath := fmt.Sprintf("%s/%s/%s", conf.Config.StorageRoot, conf.Config.BaseLocation, fileInfo.Path)
			readFile, err := os.Open(nfsFilePath)

			if err != nil {
				stdlog.WithError(err).Errorf("failed to open file %s", nfsFilePath)
				return stderr.Wrap(err, "failed to open file %s", nfsFilePath)
			}

			_, err = io.CopyBuffer(zosWriter, readFile, buffer)
			if err != nil {
				return stderr.Wrap(err, "failed to copy file %s to stream", nfsFilePath)
			}
			readFile.Close()
		}
	}
	return nil
}

func addAnnoFilesToZip(annoFiles []*models.FileInfoJson, zos *zip.Writer, buildPath func(string) string) error {
	buffer := make([]byte, 1024)
	for _, fileInfo := range annoFiles {
		if !fileInfo.IsDir {
			filePath := buildPath(fileInfo.Path)
			zosWriter, err := zos.Create(filePath)
			if err != nil {
				return stderr.Wrap(err, "failed to add file %s to zip", filePath)
			}
			nfsFilePath := fmt.Sprintf("%s/%s/%s", conf.Config.StorageRoot, conf.Config.BaseLocation, fileInfo.Path)
			readFile, err := os.Open(nfsFilePath)

			if err != nil {
				stdlog.WithError(err).Errorf("failed to open file %s", nfsFilePath)
				return stderr.Wrap(err, "failed to open file %s", nfsFilePath)
			}

			_, err = io.CopyBuffer(zosWriter, readFile, buffer)
			if err != nil {
				return stderr.Wrap(err, "failed to copy file %s to stream", nfsFilePath)
			}
			readFile.Close()
		}
	}
	return nil
}

func (a annotationSetManager) getAllAnnoFiles(annoSet *models.AnnotationSet) ([]*models.FileInfoJson, error) {
	repo := a.Q.AnnotationSetObjInfo
	annoSetObjs, err := repo.Where(repo.AnnotationSetID.Eq(annoSet.ID)).Preload(repo.AnnotationObj).
		Preload(repo.AnnotationObj.ImageObj).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getAllAnnoFiles method error, setId [%d].", annoSet.ID)
	}
	return getFileInfoJsons(annoSet, annoSetObjs), nil
}

func getFileInfoJsons(annoSet *models.AnnotationSet, annoSetObjs []*models.AnnotationSetObjInfo) []*models.FileInfoJson {
	var objs []*models.AnnotationObj
	for _, annoSetObj := range annoSetObjs {
		if annoSetObj.AnnotationObj.Status == models.AnnotationObjStatusAnnotated {
			objs = append(objs, helper.FillColumns(annoSetObj.AnnotationObj))
		}
	}
	atMap := make(map[int32]models.AnnotationTask, len(annoSet.AnnotationTaskJson))
	for _, task := range annoSet.AnnotationTaskJson {
		atMap[task.ID] = task
	}
	var fileInfos []*models.FileInfoJson
	for _, obj := range objs {
		fileInfoJson := models.FileInfoJson{
			Name:             obj.OutputName,
			Owner:            atMap[obj.TaskID].Creator,
			StoreType:        atMap[obj.TaskID].StoreType,
			IsDir:            false,
			Path:             fmt.Sprintf("%s/%s", atMap[obj.TaskID].OutputPath, obj.OutputName),
			ModificationTime: obj.CreateTime.UnixMilli(),
			Length:           0,
			Child:            []*models.FileInfoJson{},
		}
		fileInfos = append(fileInfos, &fileInfoJson)
	}
	return fileInfos
}

func (a annotationSetManager) getAnnoFilesByTest(annoSet *models.AnnotationSet, test bool) ([]*models.FileInfoJson, error) {
	repo := a.Q.AnnotationSetObjInfo
	annoSetObjs, err := repo.Where(repo.Test.Eq(models.BitBool(test)), repo.AnnotationSetID.Eq(annoSet.ID)).
		Preload(repo.AnnotationObj).Preload(repo.AnnotationObj.ImageObj).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getAnnoFilesByTest method error, setId [%d].", annoSet.ID)
	}
	return getFileInfoJsons(annoSet, annoSetObjs), nil
}

func (a annotationSetManager) getAllImageFiles(annoSet *models.AnnotationSet) (map[*models.FileInfoJson]*models.AnnotationObj, error) {
	repo := a.Q.AnnotationSetObjInfo
	annoSetObjs, err := repo.Where(repo.AnnotationSetID.Eq(annoSet.ID)).Preload(repo.AnnotationObj).
		Preload(repo.AnnotationObj.ImageObj).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getAllAnnoFiles method error, setId [%d].", annoSet.ID)
	}
	return getImageFileInfoJsons(annoSet, annoSetObjs), nil
}

func (a annotationSetManager) getImageFilesByTest(annoSet *models.AnnotationSet, test bool) (map[*models.FileInfoJson]*models.AnnotationObj, error) {
	repo := a.Q.AnnotationSetObjInfo
	annoSetObjs, err := repo.Where(repo.Test.Eq(models.BitBool(test)), repo.AnnotationSetID.Eq(annoSet.ID)).
		Preload(repo.AnnotationObj).Preload(repo.AnnotationObj.ImageObj).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getNotTestAnnoFiles method error, setId [%d].", annoSet.ID)
	}
	return getImageFileInfoJsons(annoSet, annoSetObjs), nil
}

func (a annotationSetManager) getAnnoFilesByTaskAndTest(task *models.AnnotationTask, test bool) ([]*models.FileInfoJson, error) {
	repo := a.Q.AnnotationObj
	annotationObjs, err := repo.Where(repo.Test.Eq(models.BitBool(test)), repo.TaskID.Eq(task.ID),
		repo.Status.Eq(string(models.AnnotationObjStatusAnnotated))).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getNotTestAnnoFilesByTask method error, taskId [%d].", task.ID)
	}
	var fileInfos []*models.FileInfoJson
	for _, obj := range annotationObjs {
		fileInfoJson := models.FileInfoJson{
			Name:             obj.OutputName,
			Owner:            task.Creator,
			StoreType:        task.StoreType,
			IsDir:            false,
			Path:             fmt.Sprintf("%s/%s", task.OutputPath, obj.OutputName),
			ModificationTime: obj.CreateTime.UnixMilli(),
			Length:           0,
			Child:            []*models.FileInfoJson{},
		}
		fileInfos = append(fileInfos, &fileInfoJson)
	}
	return fileInfos, nil
}

func (a annotationSetManager) getAllAnnoFilesByTask(task *models.AnnotationTask) ([]*models.FileInfoJson, error) {
	repo := a.Q.AnnotationObj
	annotationObjs, err := repo.Where(repo.TaskID.Eq(task.ID),
		repo.Status.Eq(string(models.AnnotationObjStatusAnnotated))).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getAllAnnoFilesByTask method error, taskId [%d].", task.ID)
	}
	var fileInfos []*models.FileInfoJson
	for _, obj := range annotationObjs {
		fileInfoJson := models.FileInfoJson{
			Name:             obj.OutputName,
			Owner:            task.Creator,
			StoreType:        task.StoreType,
			IsDir:            false,
			Path:             fmt.Sprintf("%s/%s", task.OutputPath, obj.OutputName),
			ModificationTime: obj.CreateTime.UnixMilli(),
			Length:           0,
			Child:            []*models.FileInfoJson{},
		}
		fileInfos = append(fileInfos, &fileInfoJson)
	}
	return fileInfos, nil
}

func (a annotationSetManager) getImageFilesByTaskAndTest(task *models.AnnotationTask,
	test bool) (map[*models.FileInfoJson]*models.AnnotationObj, error) {
	repo := a.Q.AnnotationObj
	annotationObjs, err := repo.Where(repo.Test.Eq(models.BitBool(test)), repo.TaskID.Eq(task.ID)).
		Preload(repo.ImageObj).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getNotTestImageFilesByTask method error, taskId [%d].", task.ID)
	}
	fileInfos := make(map[*models.FileInfoJson]*models.AnnotationObj)
	for _, obj := range annotationObjs {
		// 先根据文件后缀进行过滤
		if !helper.IsImageBySuffix(obj.ImageObj.Path) {
			continue
		}

		fileInfoJson := models.FileInfoJson{
			Name:             obj.ImageObj.Name,
			Owner:            task.Creator,
			StoreType:        task.StoreType,
			IsDir:            false,
			Path:             obj.ImageObj.Path,
			ModificationTime: obj.ImageObj.CreateAt.UnixMilli(),
			Length:           0,
			Child:            []*models.FileInfoJson{},
		}
		fileInfos[&fileInfoJson] = obj
	}
	return fileInfos, nil
}

func (a annotationSetManager) getAllImageFilesByTask(task *models.AnnotationTask) (map[*models.FileInfoJson]*models.AnnotationObj, error) {
	repo := a.Q.AnnotationObj
	annotationObjs, err := repo.Where(repo.TaskID.Eq(task.ID)).Preload(repo.ImageObj).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "getNotTestImageFilesByTask method error, taskId [%d].", task.ID)
	}
	fileInfos := make(map[*models.FileInfoJson]*models.AnnotationObj)
	for _, obj := range annotationObjs {
		// 先根据文件后缀进行过滤
		if !helper.IsImageBySuffix(obj.ImageObj.Path) {
			continue
		}

		fileInfoJson := models.FileInfoJson{
			Name:             obj.ImageObj.Name,
			Owner:            task.Creator,
			StoreType:        task.StoreType,
			IsDir:            false,
			Path:             obj.ImageObj.Path,
			ModificationTime: obj.ImageObj.CreateAt.UnixMilli(),
			Length:           0,
			Child:            []*models.FileInfoJson{},
		}
		fileInfos[&fileInfoJson] = obj
	}
	return fileInfos, nil
}

func getImageFileInfoJsons(annoSet *models.AnnotationSet,
	annoSetObjs []*models.AnnotationSetObjInfo) map[*models.FileInfoJson]*models.AnnotationObj {
	var objs []*models.AnnotationObj
	for _, annoSetObj := range annoSetObjs {
		if annoSetObj.AnnotationObj.Status == models.AnnotationObjStatusAnnotated {
			objs = append(objs, helper.FillColumns(annoSetObj.AnnotationObj))
		}
	}
	atMap := make(map[int32]models.AnnotationTask, len(annoSet.AnnotationTaskJson))
	for _, task := range annoSet.AnnotationTaskJson {
		atMap[task.ID] = task
	}
	fileInfos := make(map[*models.FileInfoJson]*models.AnnotationObj)
	for _, obj := range objs {
		// 先根据文件后缀进行过滤
		if !helper.IsImageBySuffix(obj.ImageObj.Path) {
			continue
		}

		fileInfoJson := models.FileInfoJson{
			Name:             obj.ImageObj.Name,
			Owner:            atMap[obj.TaskID].Creator,
			StoreType:        atMap[obj.TaskID].StoreType,
			IsDir:            false,
			Path:             obj.ImageObj.Path,
			ModificationTime: obj.ImageObj.CreateAt.UnixMilli(),
			Length:           0,
			Child:            []*models.FileInfoJson{},
		}
		fileInfos[&fileInfoJson] = obj
	}
	return fileInfos
}

// Deprecated 根据数据集id（对应annotation_family表中的ID）导出
func (a annotationSetManager) exportTestPackageBySetIdaWithFile(sid int32, zos *zip.Writer) error {
	annoSet, err := a.GetSet(sid)
	if err != nil {
		return stderr.Wrap(err, "ExportSetsPackage: a.GetSet error, setId [%d]", sid)
	}
	var annoFiles []*models.FileInfoJson
	annoFiles, err = a.getAnnoFilesByTest(annoSet, true)
	if err != nil {
		return err
	}
	f := func(path string) string {
		name := path[strings.LastIndex(path, "/")+1:]
		filePath := fmt.Sprintf("%s/%s", "img_xml", name)
		return filePath
	}
	err = addAnnoFilesToZip(annoFiles, zos, f)
	if err != nil {
		return err
	}
	var imageFiles map[*models.FileInfoJson]*models.AnnotationObj
	imageFiles, err = a.getImageFilesByTest(annoSet, true)
	if err != nil {
		return err
	}

	buildPath := func(path string, obj *models.AnnotationObj) string {
		name := path[strings.LastIndex(path, "/")+1:]
		annoIndex := strings.LastIndex(obj.OutputName, ".")
		random := obj.OutputName[annoIndex-3 : annoIndex]
		index := strings.LastIndex(name, ".")
		filePath := fmt.Sprintf("%s/%s-%s%s", "img", name[:index], random, name[index:])
		return filePath
	}

	err = addImageFilesToZip(imageFiles, zos, buildPath)
	if err != nil {
		return err
	}
	return nil
}

// ExportTestPackageByTaskId 根据任务id（对应annotation_set表中的ID）导出
func (a annotationSetManager) ExportTestPackageByTaskId(taskId int32, zos *zip.Writer) error {
	annotationTask, err := GetAnnotationTaskManager().GetAnnotationTask(taskId)
	if err != nil {
		return stderr.Wrap(err, "ExportSetPackageByTask: GetAnnotationTask error, taskId [%d]", taskId)
	}
	annoFiles, err := a.getAnnoFilesByTaskAndTest(annotationTask, true)
	if err != nil {
		return err
	}
	f := func(path string) string {
		name := path[strings.LastIndex(path, "/")+1:]
		filePath := fmt.Sprintf("%s/%s", "img_xml", name)
		return filePath
	}
	err = addAnnoFilesToZip(annoFiles, zos, f)
	if err != nil {
		return err
	}
	imageFiles, err := a.getImageFilesByTaskAndTest(annotationTask, true)

	buildPath := func(path string, obj *models.AnnotationObj) string {
		name := path[strings.LastIndex(path, "/")+1:]
		index := strings.LastIndex(name, ".")
		filePath := fmt.Sprintf("%s/%s-%s%s", "img", name[:helper.AnnotationFilePrefixLength], helper.TrimExtension(obj.ImageObj.Name), name[index:])
		return filePath
	}

	err = addImageFilesToZip(imageFiles, zos, buildPath)
	if err != nil {
		return err
	}
	return nil
}

func (a annotationSetManager) ExportSetsPackage(sid int32, onlyAnnotation bool, allJpg bool, onlyXml bool, outputFormat string, zos *zip.Writer) error {
	annoSet, err := a.GetSet(sid)
	if err != nil {
		return stderr.Wrap(err, "ExportSetsPackage: a.GetSet error, setId [%d]", sid)
	}
	stdlog.Infof("annoSet is : %v", annoSet)
	repo := a.Q.AnnotationSetObjInfo
	var condition query.IAnnotationSetObjInfoDo
	// 算法调用时传的onlyXml=true
	if onlyXml {
		condition = repo.Where(repo.Test.Eq(models.BitBool(false)), repo.AnnotationSetID.Eq(annoSet.ID))
	} else {
		condition = repo.Where(repo.AnnotationSetID.Eq(annoSet.ID))
	}
	objs, err := condition.
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.ImageObj).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.ObjLabels).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.AnnotationTask).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.ObjLabels.Label).
		Find()
	if err != nil {
		return stderr.Wrap(err, "ExportSetsPackage：QueryAnnotationSetObjInfo error, setId [%d].", annoSet.ID)
	}
	annoObjs := make([]*models.AnnotationObj, len(objs))
	for i, obj := range objs {
		annoObjs[i] = helper.FillColumns(obj.AnnotationObj)
	}
	for _, annoObj := range annoObjs {
		annoFilePath := fmt.Sprintf("%s/%s", "Annotations", annoObj.OutputName)
		err = a.writeAnnoResultToZipStream(annoObj, zos, annoFilePath)
		if err != nil {
			return err
		}
		if !onlyAnnotation {
			path := annoObj.ImageObj.Path
			if !helper.ValidateImagePath(path) {
				return stderr.Error("ExportSetPackageByTask method, image path %s is invalid", path)
			}
			name := path[strings.LastIndex(path, "/")+1:]
			index := strings.LastIndex(name, ".")
			imageFilePath := fmt.Sprintf("%s/%s-%s%s", "JPEGImages", name[:helper.AnnotationFilePrefixLength], helper.TrimExtension(annoObj.ImageObj.Name), name[index:])
			if allJpg {
				imageFilePath = fmt.Sprintf("%s.jpg", imageFilePath[:strings.LastIndex(imageFilePath, ".")])
			}
			err = a.writeImagesToZipStream(annoObj, zos, imageFilePath)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (a annotationSetManager) writeImagesToZipStream(obj *models.AnnotationObj, zos *zip.Writer, filePath string) error {
	buffer := make([]byte, 1024)
	zosWriter, err := zos.Create(filePath)
	if err != nil {
		return stderr.Wrap(err, "failed to add file %s to zip", filePath)
	}

	nfsFilePath, err := helper.GetAbsImagePath(obj.ImageObj.Path)
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager:writeImagesToZipStream method")
	}
	readFile, err := os.Open(nfsFilePath)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to open file %s", nfsFilePath)
		return stderr.Wrap(err, "failed to open file %s", nfsFilePath)
	}
	_, err = io.CopyBuffer(zosWriter, readFile, buffer)
	if err != nil {
		return stderr.Wrap(err, "failed to copy file %s to stream", nfsFilePath)
	}
	readFile.Close()
	return nil
}

func (a annotationSetManager) writeAnnoResultToZipStream(annoObj *models.AnnotationObj, zos *zip.Writer, filePath string) error {
	// 图片中没有标注目标时就不导出相应的标注文件，否则分割算法可能会报错
	if len(annoObj.ObjLabels) == 0 {
		stdlog.Infof("the ObjLabels is empty, it won't been export for %s", annoObj.ImageObj.Name)
		return nil
	}
	switch annoObj.AnnotationTask.WriterType {
	case models.WriterTypePascalVoc:
		// 写xml
		xmlSize := &helper.XmlSize{
			Width:  annoObj.ImageObj.Width,
			Height: annoObj.ImageObj.Height,
			Depth:  annoObj.ImageObj.Depth,
		}
		rotate := annoObj.Rotate
		var vocXmlObjects []*helper.XmlObject
		for _, objLabel := range annoObj.ObjLabels {
			points := objLabel.LabelPosition.Points
			if len(points) != 4 {
				return stderr.Internal.Error("Incorrect num of label points! objlabel id : %d", objLabel.ID)
			}
			vocXmlObjects = append(vocXmlObjects, &helper.XmlObject{
				Name:      objLabel.Label.Name,
				Pose:      "not-defined",
				Truncated: helper.BoolToInt32(objLabel.LabelPosition.Truncated),
				Occluded:  helper.BoolToInt32(objLabel.LabelPosition.Occluded),
				Difficult: helper.BoolToInt32(objLabel.LabelPosition.Difficult),
				Bndbox:    helper.PointsToBndbox(points),
				Text:      objLabel.LabelPosition.Content,
			})
		}
		path := annoObj.ImageObj.Path
		if !helper.ValidateImagePath(path) {
			return stderr.Error("writeAnnoResultToZipStream method, the image path %s is invalid", path)
		}
		xmlAnnotation := helper.XmlAnnotation{
			Folder:    path[:strings.LastIndex(path, "/")],
			FileName:  fmt.Sprintf("%s.%s", annoObj.ImageObj.ID, annoObj.ImageObj.Format),
			Path:      path,
			Size:      xmlSize,
			Rotate:    rotate,
			Object:    vocXmlObjects,
			Segmented: 0,
		}
		zosWriter, err := zos.Create(filePath)
		if err != nil {
			return stderr.Wrap(err, "failed to add file %s to zip", filePath)
		}
		encoder := xml.NewEncoder(zosWriter)
		err = encoder.Encode(xmlAnnotation)
		if err != nil {
			return stderr.Wrap(err, "failed to Encode xml file %s", filePath)
		}

	case models.WriterTypeLabelMe:
		// 写json
		labelMe := models.LabelMe{
			ImageWidth:  annoObj.ImageObj.Width,
			ImageHeight: annoObj.ImageObj.Height,
			ImagePath:   fmt.Sprintf("%s.%s", annoObj.ImageObj.ID, annoObj.ImageObj.Format),
			Flags:       make(map[string]interface{}, 0),
		}

		for _, objLabel := range annoObj.ObjLabels {
			labelMeShape := models.LabelMeShape{
				Label: objLabel.Label.Name,
				Flags: make(map[string]interface{}, 0),
			}
			if objLabel.LabelPosition.Shape == models.LabelPositionShapeKeypoint {
				labelMeShape.ShapeType = "point"
			} else {
				labelMeShape.ShapeType = string(objLabel.LabelPosition.Shape)
			}
			for _, point := range objLabel.LabelPosition.Points {
				labelMeShape.Points = append(labelMeShape.Points, []float32{float32(point.X), float32(point.Y)})
			}
			labelMe.Shapes = append(labelMe.Shapes, &labelMeShape)
		}

		zosWriter, err := zos.Create(filePath)
		if err != nil {
			return stderr.Wrap(err, "failed to add file %s to zip", filePath)
		}
		jsonEncoder := json.NewEncoder(zosWriter)
		if err := jsonEncoder.Encode(labelMe); err != nil {
			return stderr.Wrap(err, "failed to Encode json file %s", filePath)
		}
	}
	return nil

}

func (a annotationSetManager) ExportTestPackageBySetId(sid int32, zos *zip.Writer) error {
	annoSet, err := a.GetSet(sid)
	if err != nil {
		return stderr.Wrap(err, "ExportTestPackageBySetId: a.GetSet error, setId [%d]", sid)
	}
	stdlog.Infof("annoSet is : %v", annoSet)
	repo := a.Q.AnnotationSetObjInfo
	objs, err := repo.Where(repo.Test.Eq(models.BitBool(true)), repo.AnnotationSetID.Eq(annoSet.ID)).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.ImageObj).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.ObjLabels).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.AnnotationTask).
		Preload(a.Q.AnnotationSetObjInfo.AnnotationObj.ObjLabels.Label).
		Find()
	if err != nil {
		return stderr.Wrap(err, "ExportTestPackageBySetId：QueryAnnotationSetObjInfo error, setId [%d].", annoSet.ID)
	}
	annoObjs := make([]*models.AnnotationObj, len(objs))
	for i, obj := range objs {
		annoObjs[i] = helper.FillColumns(obj.AnnotationObj)
	}
	for _, annoObj := range annoObjs {
		annoFilePath := fmt.Sprintf("%s/%s", "img_xml", annoObj.OutputName)
		err = a.writeAnnoResultToZipStream(annoObj, zos, annoFilePath)
		if err != nil {
			return err
		}
		path := annoObj.ImageObj.Path
		if !helper.ValidateImagePath(path) {
			return stderr.Error("ExportTestPackageBySetId method, image path %s is invalid", path)
		}
		name := path[strings.LastIndex(path, "/")+1:]
		index := strings.LastIndex(name, ".")
		imageFilePath := fmt.Sprintf("%s/%s-%s%s", "img", name[:helper.AnnotationFilePrefixLength], helper.TrimExtension(annoObj.ImageObj.Name), name[index:])
		err = a.writeImagesToZipStream(annoObj, zos, imageFilePath)
		if err != nil {
			return err
		}
	}

	return nil
}

func (a annotationSetManager) ListWithVersion(projectId string, t ...string) ([]*models.AnnotationSet, error) {
	repo := a.Q.AnnotationSet
	if len(t) == 0 {
		t = append(t, string(models.DatasetTypeText))
	}

	ret, err := func() ([]*models.AnnotationSet, error) {
		if len(t) == 0 {
			return repo.Select(repo.ALL).Where(repo.ProjectID.Eq(projectId)).Preload(repo.AnnotationTextSetVersions).
				Order(repo.CreateTime.Desc()).Find()
		} else {
			return repo.Select(repo.ALL).Where(repo.DataType.In(t...), repo.ProjectID.Eq(projectId)).
				Preload(repo.AnnotationTextSetVersions).Order(repo.CreateTime.Desc()).Find()
		}
	}()
	if err != nil {
		return nil, stderr.Wrap(err, "ListWithVersion method error")
	}
	return ret, nil
}

func (a annotationSetManager) GetAnnotationSetCloneName(projectId string, setId int32, targetProjectId string) (string, error) {
	repo := a.Q.AnnotationSet
	currentAnnoSet, err := repo.Where(repo.ID.Eq(setId), repo.ProjectID.Eq(projectId)).Select(repo.Name).First()
	if err != nil {
		return "", stderr.Wrap(err, "GetAnnotationSetCloneName: get current annotation set [%d] error.", setId)
	}
	existAnnoSets, err := repo.Where(repo.ProjectID.Eq(targetProjectId)).Select(repo.Name).Find()
	if err != nil {
		return "", stderr.Wrap(err, "GetAnnotationSetCloneName: list annotation sets from project id %s error.", targetProjectId)
	}
	var existSets []stdname.Namer
	for _, existAnnoSet := range existAnnoSets {
		existSets = append(existSets, existAnnoSet)
	}
	return stdname.NewCloneName(currentAnnoSet.Name, existSets...), nil
}

func (a annotationSetManager) CloneAnnotationSet(projectId string, setId int32, annoSet *models.AnnotationSet) error {
	repo := a.Q.AnnotationSet
	currentAnnoSet, err := repo.Where(repo.ID.Eq(setId), repo.ProjectID.Eq(projectId)).First()
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager: CloneAnnotationSet method, get current annotation set [%d]", setId)
	}
	exist, err := repo.Where(repo.Name.Eq(annoSet.Name), repo.ProjectID.Eq(annoSet.ProjectID)).Find()
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager: CloneAnnotationSet method, get annotation set by name [%s]", annoSet.Name)
	}
	if len(exist) > 0 {
		return common.ANNOTATION_FAMILY_NAME_ALREADY_EXIST.SetArgs(annoSet.Name)
	}
	annoSet.AnnotationType = currentAnnoSet.AnnotationType
	annoSet.Icon = currentAnnoSet.Icon
	annoSet.TestQuantity = currentAnnoSet.TestQuantity
	annoSet.Quantity = currentAnnoSet.Quantity
	annoSet.DataType = currentAnnoSet.DataType
	annoSet.Size = currentAnnoSet.Size
	annoSet.Labels = currentAnnoSet.Labels
	annoSet.DataUsage = currentAnnoSet.DataUsage

	handler, err := a.factory.GetHandler(currentAnnoSet.DataType)
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager: CloneAnnotationSet method error")
	}
	return handler.CloneAnnotationSet(currentAnnoSet, annoSet)
}

func (a annotationSetManager) GetAnnotationSetStats(projectId string) (*models.DatasetStats, error) {
	repo := a.Q.AnnotationSet
	condition := repo.WithContext(context.Background()).
		Where(repo.ProjectID.Eq(projectId), repo.DataType.Eq(string(models.DatasetTypeText)))
	count, err := condition.Count()
	if err != nil {
		return nil, stderr.Wrap(err, "AnnotationSetManager:GetAnnotationSetStats method, count annotation set by project id %s", projectId)
	}
	var size int64
	if count != 0 {
		err = condition.Select(repo.Size.Sum()).Scan(&size)
		if err != nil {
			return nil, stderr.Wrap(err, "AnnotationSetManager:GetAnnotationSetStats method, sum annotation set size by project id %s", projectId)
		}
	}
	return &models.DatasetStats{
		Count: count,
		Size:  size,
	}, nil
}

func (a annotationSetManager) IncreaseAnnotationSetVisits(projectId string, setId int32, operation models.OperationType) error {
	asq := a.Q.AnnotationSet
	var err error
	annoset, err := asq.Where(asq.ID.Eq(setId), asq.ProjectID.Eq(projectId)).First()
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager:IncreaseAnnotationSetVisits method, query annotation set by id %d", setId)
	}
	switch operation {
	case models.OperationTypeVisit:
		_, err = asq.Where(asq.ID.Eq(setId), asq.ProjectID.Eq(projectId)).
			UpdateSimple(asq.Visits.Value(annoset.Visits + 1))
	case models.OperationTypeDownload:
		_, err = asq.Where(asq.ID.Eq(setId), asq.ProjectID.Eq(projectId)).
			UpdateSimple(asq.Downloads.Value(annoset.Downloads + 1))
	default:
		return stderr.Error("operation type %s is not support!", operation)
	}
	if err != nil {
		return stderr.Wrap(err, "AnnotationSetManager:IncreaseAnnotationSetVisits method, update annotation set by id %d", setId)
	}
	return nil

}

func (a annotationSetManager) ListAnnotationSetByPage(req *models.SamplePageReq) (*models.Page, error) {
	asq := a.Q.AnnotationSet
	var condition = asq.WithContext(context.Background())
	condition = condition.Where(asq.ProjectID.Eq(req.ProjectId))
	if len(req.Types) > 0 {
		condition = condition.Where(asq.DataType.In(req.Types...))
	}
	if len(req.Creators) > 0 {
		condition = condition.Where(asq.Creator.In(req.Creators...))
	}
	if req.Name != "" {
		condition = condition.Where(asq.Name.Like(fmt.Sprintf("%%%s%%", req.Name)))
	}
	if req.OrderBy != "" {
		orderCol, ok := asq.GetFieldByName(strcase.ToSnake(req.OrderBy))
		if !ok {
			return nil, stderr.Internal.Error("AnnotationSetManager: ListAnnotationSetByPage method, order by column [%s] is illegal.",
				req.OrderBy)
		}
		if req.Asc {
			condition = condition.Order(orderCol)
		} else {
			condition = condition.Order(orderCol.Desc())
		}
	}
	total, err := condition.Count()
	if err != nil {
		return nil, stderr.Wrap(err, "ListAnnotationSetByPage: count list total error.")
	}
	ret, err := condition.Limit(req.Size).Offset(req.From).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "ListAnnotationSetByPage: query list error.")
	}
	sets := make([]interface{}, 0)
	for _, set := range ret {
		sets = append(sets, set)
	}
	return helper.BuildPage(req.PageReq, total, sets), nil
}
