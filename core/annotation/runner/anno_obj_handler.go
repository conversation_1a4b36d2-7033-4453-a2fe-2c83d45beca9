package runner

import (
	"sync"
	"transwarp.io/applied-ai/aiot/cvat-backend/dao"
	"transwarp.io/applied-ai/aiot/cvat-backend/dao/query"
	"transwarp.io/applied-ai/aiot/cvat-backend/models"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

var (
	factory *annotationObjFactory
	aofOnce sync.Once
)

func GetAnnotationObjFactory() *annotationObjFactory {
	aofOnce.Do(func() {
		factory = &annotationObjFactory{
			q: dao.InitQuery(),
		}
	})
	return factory
}

type annotationObjFactory struct {
	q *query.Query
}

func (a *annotationObjFactory) GetHandler(t models.DatasetType) (AnnotationObjHandler, error) {
	switch t {
	case models.DatasetTypeImage:
		return &annotationObjImageHandler{
			q: a.q,
		}, nil
	case models.DatasetTypeText:
		return &annotationObjTextHandler{
			q: a.q,
		}, nil
	default:
		return nil, stderr.Internal.Error("Not supported data type.")
	}
}

type AnnotationObjHandler interface {
	ListObjsByJobId(jobId int64) ([]models.AnnoObj, error)
	UpdateStatusById(id int32, status models.AnnotationObjStatus) error
	UpdateObj(annoObj models.AnnoObj) error
	BeginAnnotate(annoObj models.AnnoObj, jobCtx *JobRunnerContext) *models.AnnotationObjResult
	ProcessResult(annoObj models.AnnoObj, jobCtx *JobRunnerContext, result *models.AnnotationObjResult) error
	ProcessFailResult(annoObj models.AnnoObj, result *models.AnnotationObjResult) error
	GetAnnotatedObjsCount(jobId int64) (int64, error)
	GetObjCountsByStatus(taskId int32, status string) (int64, error)
	GetObjCountsByVerifyStatus(taskId int32, verifyStatus string) (int64, error)
	GetObjsTotalCounts(taskId int32) (int64, error)
}
