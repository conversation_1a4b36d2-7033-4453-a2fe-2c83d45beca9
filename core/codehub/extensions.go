package codehub

import (
	"context"
	"fmt"
	"os"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	installTimeout = time.Minute * 5
)

var (
	ExtensionCreateList = []CreateExtensionFunc{
		NewPythonExtension,
		NewDebugpyExtension,
		NewSoCodeExtension,
		NewKeyBindingsExtension,
	}
)

type Extension interface {
	ID() string       // 插件id
	Name() string     // 插件名
	Path() string     // 插件的本地包路径
	Install() error   // 安装插件
	Uninstall() error // 卸载插件
}

type KubeExtension struct {
	instance *KubernetesInstance
	id       string
	name     string
	path     string
}

func (e *KubeExtension) ID() string {
	return e.id
}

func (e *KubeExtension) Name() string {
	return e.name
}

func (e *KubeExtension) Path() string {
	return e.path
}

func (e *KubeExtension) Install() error {
	ctx, cancel := context.WithTimeout(context.Background(), installTimeout)
	defer cancel()
	err := e.instance.CmdExec(e.getInstallCmd(), ctx, os.Stdout, os.Stderr)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to install extension %s", e.Name())
	}
	return err
}

func (e *KubeExtension) Uninstall() error {
	ctx, cancel := context.WithTimeout(context.Background(), installTimeout)
	defer cancel()
	err := e.instance.CmdExec(e.getUninstallCmd(), ctx, os.Stdout, os.Stderr)
	if err != nil {
		stdlog.WithError(err).Errorf("failed to uninstall extension %s", e.Name())
	}
	return err
}

func (e *KubeExtension) getInstallCmd() []string {
	return []string{"bash", "-c", fmt.Sprintf("code-server --install-extension %s && rm -f %s", e.path, e.path)}
}

func (e *KubeExtension) getUninstallCmd() []string {
	return []string{"bash", "-c", fmt.Sprintf("code-server --uninstall-extension %s", e.id)}
}

type CreateExtensionFunc func(instance *KubernetesInstance) *KubeExtension

func NewSoCodeExtension(instance *KubernetesInstance) *KubeExtension {
	return &KubeExtension{
		name:     "SoCode",
		id:       "continue.continue",
		path:     "/workspace/socode.vsix",
		instance: instance,
	}
}

func NewPythonExtension(instance *KubernetesInstance) *KubeExtension {
	return &KubeExtension{
		name:     "Python",
		id:       "ms-python.python",
		path:     "/workspace/python.vsix",
		instance: instance,
	}
}

func NewKeyBindingsExtension(instance *KubernetesInstance) *KubeExtension {
	return &KubeExtension{
		name:     "KeyBindings",
		id:       "k--kato.intellij-idea-keybindings",
		path:     "/workspace/keybindings.vsix",
		instance: instance,
	}
}

func NewDebugpyExtension(instance *KubernetesInstance) *KubeExtension {
	return &KubeExtension{
		name:     "Debugpy",
		id:       "ms-python.debugpy",
		path:     "/workspace/debugpy.vsix",
		instance: instance,
	}
}
