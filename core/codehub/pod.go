package codehub

import (
	"bytes"
	"fmt"
	"gopkg.in/yaml.v3"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8syaml "k8s.io/apimachinery/pkg/util/yaml"
	"transwarp.io/applied-ai/aiot/csm-backend/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	K8sResourceGpu                            = "gpu"
	K8sResourceCpu                            = "cpu"
	K8sResourceMemory                         = "memory"
	KruxResourceGpu                           = "transwarp.io/vgpu-core"
	ResourceNvidiaGpuName corev1.ResourceName = "nvidia.com/gpu"
	ResourceKruxGpuName   corev1.ResourceName = "transwarp.io/vgpu-core"
)

var (
	HostNetwork             = false
	Privileged              = true
	CodeServerPort    int32 = 9999
	JupyterPort       int32 = 8888
	SshdPort          int32 = 22
	CodeServerCommand       = "code-server --bind-addr 0.0.0.0:9999 --disable-workspace-trust --auth none --disable-telemetry . "
	SshdCommand             = "$(which sshd) && sleep infinity"
	JupyterCommand          = "jupyter-lab --ServerApp.password=\"\" --ServerApp.token=\"\" --LabApp.password=\"\" --LabApp.token=\"\" --ServerApp.disable_check_xsrf=True --ip=0.0.0.0 --allow-root --NotebookApp.base_url=%s"
	CheckProcCommand        = "ps -ef |grep -w %s |grep -v grep |wc -l"
	PipInstallCommand       = "sudo -u sophon pip install --prefix %s -r %s"
)

func NewPodBuilder(namespace, tenant string, config *KubeInstanceConfig) *PodBuilder {
	return &PodBuilder{
		Namespace: namespace,
		Tenant:    tenant,
		Config:    config,
	}
}

func NewSlavePodBuilder(namespace, tenant string, config *KubeInstanceConfig) *SlavePodBuilder {
	return &SlavePodBuilder{
		NewPodBuilder(namespace, tenant, config),
	}
}

// SlavePodBuilder instance的slave pod构建
type SlavePodBuilder struct {
	*PodBuilder
}

func (b *SlavePodBuilder) buildPod() *corev1.Pod {
	podSpec := b.buildPodSpec()
	podSpec.Containers = b.buildContainers()
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      b.Config.Name,
			Namespace: b.Tenant,
			Labels:    b.buildLabels(),
			OwnerReferences: []metav1.OwnerReference{
				{
					APIVersion:         "v1",
					Kind:               "Pod",
					Name:               b.Config.OwnerConfig.Name,
					UID:                b.Config.OwnerConfig.Uid,
					BlockOwnerDeletion: func(b bool) *bool { return &b }(true),
					Controller:         func(b bool) *bool { return &b }(true),
				},
			},
		},
		Spec: podSpec,
	}
}

func (b *SlavePodBuilder) buildContainers() []corev1.Container {
	kubeConfig := b.Config
	return []corev1.Container{
		{
			Name:    kubeConfig.Name,
			Image:   kubeConfig.Image,
			Command: []string{"/bin/bash", "-c"},
			Args:    []string{SshdCommand},
			Resources: func() corev1.ResourceRequirements {
				if kubeConfig.PodResource != nil {
					if kubeConfig.PodResource.Container != nil {
						return *kubeConfig.PodResource.Container.Resources
					}
				}
				return corev1.ResourceRequirements{}
			}(),
			Ports: []corev1.ContainerPort{
				{
					Name:          "sshd",
					ContainerPort: SshdPort,
				},
			},
		},
	}
}

func (b *SlavePodBuilder) buildLabels() map[string]string {
	kubeConfig := b.Config
	labels := make(map[string]string)
	if kubeConfig.ExtraConfig != nil {
		if len(kubeConfig.ExtraConfig.AppLabels) > 0 {
			labels = kubeConfig.ExtraConfig.AppLabels
		}
	}
	// 添加instance标签
	labels[CODEHUB_SLAVE] = "true"
	labels[CODEHUB_NAMESPACE] = b.Namespace
	// 添加id标签
	labels[CODEHUB_SLAVE_NAME] = kubeConfig.Name
	return labels
}

// buildResourceRequire
// slave pod不设置cpu和内存限制
// deprecated
func (b *SlavePodBuilder) buildResourceRequire(m map[string]string) corev1.ResourceRequirements {
	return corev1.ResourceRequirements{}
}

// PodBuilder instance的pod构建
type PodBuilder struct {
	Config    *KubeInstanceConfig
	Namespace string // 这个代表是从哪个namespace的pod创建
	Tenant    string // 这个才是代表创建到哪个namespace，实际上是租户的概念
}

func (b *PodBuilder) buildPodSpec() corev1.PodSpec {
	kubeConfig := b.Config
	podSpec := corev1.PodSpec{
		HostNetwork:   HostNetwork,
		Containers:    b.buildContainers(),
		RestartPolicy: corev1.RestartPolicyAlways,
	}
	sizeLimit := resource.MustParse("6Gi")
	volumes := []corev1.Volume{
		{
			Name: "dshm",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					Medium:    "Memory",
					SizeLimit: &sizeLimit,
				},
			},
		},
	}
	volumeMounts := []corev1.VolumeMount{
		{
			Name:      "dshm",
			MountPath: "/dev/shm",
		},
	}
	if kubeConfig.ExtraConfig != nil {
		if len(kubeConfig.ExtraConfig.Envs) > 0 {
			podSpec.Containers[0].Env = envMapToEnvVar(kubeConfig.ExtraConfig.Envs)
		}
		if kubeConfig.ExtraConfig.ImagePullPolicy != "" {
			podSpec.Containers[0].ImagePullPolicy = corev1.PullPolicy(kubeConfig.ExtraConfig.ImagePullPolicy)
		}
		if len(kubeConfig.ExtraConfig.Volumes) > 0 {
			for _, volumeConfig := range kubeConfig.ExtraConfig.Volumes {
				volumeName := volumeConfig.VolumeName
				volumes = append(volumes, corev1.Volume{
					Name: volumeName,
					VolumeSource: corev1.VolumeSource{
						PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
							ClaimName: volumeConfig.PvcName,
						},
					},
				})
			}
		}
		if len(kubeConfig.ExtraConfig.VolumeMounts) > 0 {
			for _, mountConfig := range kubeConfig.ExtraConfig.VolumeMounts {
				volumeMounts = append(volumeMounts, corev1.VolumeMount{
					Name:      mountConfig.VolumeName,
					MountPath: mountConfig.MountPath,
					SubPath:   mountConfig.SubPath,
					ReadOnly:  mountConfig.ReadOnly,
				})
			}
		}
		// 额外的配置
		if kubeConfig.ExtraConfig.NeedDockerSock {
			// 是否需要挂载docker.sock
			t := corev1.HostPathSocket
			name := "docker-sock"
			path := "/var/run/docker.sock"
			volumes = append(volumes, corev1.Volume{
				Name: name,
				VolumeSource: corev1.VolumeSource{
					HostPath: &corev1.HostPathVolumeSource{
						Path: path,
						Type: &t,
					},
				},
			})
			volumeMounts = append(volumeMounts, corev1.VolumeMount{
				Name:      name,
				MountPath: path,
			})
		}
		if kubeConfig.ExtraConfig.NodeName != "" {
			podSpec.NodeName = kubeConfig.ExtraConfig.NodeName
		}
		if kubeConfig.ExtraConfig.ServiceAccount != "" {
			podSpec.ServiceAccountName = kubeConfig.ExtraConfig.ServiceAccount
		}
	}
	podSpec.Volumes = volumes
	podSpec.Containers[0].VolumeMounts = volumeMounts
	// 添加调度器
	podSpec.SchedulerName = conf.Config.SchedulerName
	// 添加affinity
	podSpec.Affinity = b.buildAffinity()
	return podSpec
}

func (b *PodBuilder) buildAffinity() *corev1.Affinity {
	kubeConfig := b.Config
	var affinity corev1.Affinity
	if kubeConfig.ExtraConfig != nil {
		if kubeConfig.ExtraConfig.NodeAffinity != nil {
			var buffer bytes.Buffer
			d, err := yaml.Marshal(kubeConfig.ExtraConfig.NodeAffinity)
			if err != nil {
				stdlog.WithError(err).Warnf("Node affinity marshal error.")
			}
			_, err = buffer.Write(d)
			if err != nil {
				stdlog.WithError(err).Warnf("Node affinity write error.")
			}
			err = k8syaml.NewYAMLOrJSONDecoder(&buffer, 1024).Decode(&affinity)
			if err != nil {
				stdlog.WithError(err).Warnf("Node affinity decode error.")
			}
		}
	}
	if kubeConfig.PodResource != nil && kubeConfig.PodResource.Affinity != nil {
		return mergeAffinity(&affinity, kubeConfig.PodResource.Affinity)
	} else {
		return &affinity
	}
}

// mergeAffinity 合并两个 v1.Affinity 对象
func mergeAffinity(affinity1, affinity2 *corev1.Affinity) *corev1.Affinity {
	mergedAffinity := &corev1.Affinity{}

	// 合并 NodeAffinity
	if affinity1.NodeAffinity != nil && affinity2.NodeAffinity != nil {
		// 合并 NodeAffinity 的规则
		mergedAffinity.NodeAffinity = &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: mergeNodeSelector(affinity1.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution, affinity2.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution),
			PreferredDuringSchedulingIgnoredDuringExecution: append(
				affinity1.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution,
				affinity2.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution...),
		}
	} else if affinity1.NodeAffinity != nil {
		mergedAffinity.NodeAffinity = affinity1.NodeAffinity
	} else if affinity2.NodeAffinity != nil {
		mergedAffinity.NodeAffinity = affinity2.NodeAffinity
	}

	// 合并 PodAffinity
	if affinity1.PodAffinity != nil && affinity2.PodAffinity != nil {
		// 合并 PodAffinity 的规则
		mergedAffinity.PodAffinity = &corev1.PodAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution:  append(affinity1.PodAffinity.RequiredDuringSchedulingIgnoredDuringExecution, affinity2.PodAffinity.RequiredDuringSchedulingIgnoredDuringExecution...),
			PreferredDuringSchedulingIgnoredDuringExecution: append(affinity1.PodAffinity.PreferredDuringSchedulingIgnoredDuringExecution, affinity2.PodAffinity.PreferredDuringSchedulingIgnoredDuringExecution...),
		}
	} else if affinity1.PodAffinity != nil {
		mergedAffinity.PodAffinity = affinity1.PodAffinity
	} else if affinity2.PodAffinity != nil {
		mergedAffinity.PodAffinity = affinity2.PodAffinity
	}

	// 合并 PodAntiAffinity
	if affinity1.PodAntiAffinity != nil && affinity2.PodAntiAffinity != nil {
		// 合并 PodAntiAffinity 的规则
		mergedAffinity.PodAntiAffinity = &corev1.PodAntiAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution:  append(affinity1.PodAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution, affinity2.PodAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution...),
			PreferredDuringSchedulingIgnoredDuringExecution: append(affinity1.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution, affinity2.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution...),
		}
	} else if affinity1.PodAntiAffinity != nil {
		mergedAffinity.PodAntiAffinity = affinity1.PodAntiAffinity
	} else if affinity2.PodAntiAffinity != nil {
		mergedAffinity.PodAntiAffinity = affinity2.PodAntiAffinity
	}

	return mergedAffinity
}

// mergeNodeSelector 合并两个 NodeSelector 对象
func mergeNodeSelector(selector1, selector2 *corev1.NodeSelector) *corev1.NodeSelector {
	if selector1 == nil && selector2 == nil {
		return nil
	}
	mergedSelector := &corev1.NodeSelector{}

	if selector1 != nil && selector2 != nil {
		mergedSelector.NodeSelectorTerms = append(selector1.NodeSelectorTerms, selector2.NodeSelectorTerms...)
	} else if selector1 != nil {
		mergedSelector = selector1
	} else {
		mergedSelector = selector2
	}

	return mergedSelector
}

func (b *PodBuilder) buildContainers() []corev1.Container {
	kubeConfig := b.Config
	return []corev1.Container{
		{
			Name:    kubeConfig.Name,
			Image:   kubeConfig.Image,
			Command: []string{"/bin/bash", "-c"},
			Args:    []string{CodeServerCommand}, // 主进程为codeserver
			//SecurityContext: &corev1.SecurityContext{
			//	Privileged: &Privileged, // 设置为特权容器
			//},
			Resources: func() corev1.ResourceRequirements {
				if kubeConfig.PodResource != nil {
					if kubeConfig.PodResource.Container != nil {
						return *kubeConfig.PodResource.Container.Resources
					}
				}
				return corev1.ResourceRequirements{}
			}(),
			Ports: []corev1.ContainerPort{
				{
					Name:          "jupyter",
					ContainerPort: JupyterPort,
				},
				{
					Name:          "codeserver",
					ContainerPort: CodeServerPort,
				},
				{
					Name:          "http",
					ContainerPort: 80,
				},
			},
		},
	}
}

func (b *PodBuilder) buildLabels() map[string]string {
	kubeConfig := b.Config
	labels := make(map[string]string)
	if kubeConfig.ExtraConfig != nil {
		if len(kubeConfig.ExtraConfig.AppLabels) > 0 {
			labels = kubeConfig.ExtraConfig.AppLabels
		}
	}
	// 添加instance标签
	labels[CODEHUB_INSTANCE] = "true"
	labels[CODEHUB_NAMESPACE] = b.Namespace
	// 添加id标签
	labels[CODEHUB_INSTANCE_NAME] = kubeConfig.Name
	return labels
}

func (b *PodBuilder) buildAnnotations() map[string]string {
	kubeConfig := b.Config
	if kubeConfig.PodResource != nil {
		return kubeConfig.PodResource.Annotations
	} else {
		return nil
	}
}

func (b *PodBuilder) buildPod() *corev1.Pod {
	podSpec := b.buildPodSpec()
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:        b.Config.Name,
			Namespace:   b.Tenant,
			Labels:      b.buildLabels(),
			Annotations: b.buildAnnotations(),
		},
		Spec: podSpec,
	}
}

func envMapToEnvVar(envMap map[string]string) []corev1.EnvVar {
	envVar := make([]corev1.EnvVar, 0, len(envMap))
	for k, v := range envMap {
		envVar = append(envVar, corev1.EnvVar{Name: k, Value: v})
	}
	return envVar
}

// buildResourceRequire
// deprecated
func (b *PodBuilder) buildResourceRequire(m map[string]string) corev1.ResourceRequirements {
	cpuRequest, _ := resource.ParseQuantity(m[K8sResourceCpu])
	memoryRequest, _ := resource.ParseQuantity(m[K8sResourceMemory])
	requests := map[corev1.ResourceName]resource.Quantity{
		corev1.ResourceCPU:    cpuRequest,
		corev1.ResourceMemory: memoryRequest,
	}
	limits := map[corev1.ResourceName]resource.Quantity{
		corev1.ResourceCPU:    cpuRequest,
		corev1.ResourceMemory: memoryRequest,
	}
	// 如果有gpu限制，则限制gpu
	if gpu, ok := m[K8sResourceGpu]; ok {
		gpuRequest, _ := resource.ParseQuantity(gpu)
		requests[ResourceNvidiaGpuName] = gpuRequest
		limits[ResourceNvidiaGpuName] = gpuRequest
	}
	// krux适配
	if gpu, ok := m[KruxResourceGpu]; ok {
		gpuRequest, _ := resource.ParseQuantity(gpu)
		requests[ResourceKruxGpuName] = gpuRequest
		limits[ResourceKruxGpuName] = gpuRequest
	}
	resources := corev1.ResourceRequirements{
		Requests: requests, // 设置资源请求
		Limits:   limits,   // 设置资源限制
	}
	return resources
}

func JupyterPath(path string, tenant string) string {
	return fmt.Sprintf("/llm/%s/tenants/%s%s%s", conf.Config.Namespace, tenant, conf.Config.Proxy.JupyterPrefix, path)
}
