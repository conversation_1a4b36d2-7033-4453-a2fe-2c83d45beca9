package model

import (
	"context"
	"encoding/json"
	"fmt"

	v1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
	servingpb "transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/aip/llmops-common/pkg/serving"
	"transwarp.io/mlops/mlops-std/stdlog"
)

type ResourceRequirements struct {
	Limit        *common.Resource
	Request      *common.Resource
	GpuType      string                    //  算力卡，可选 Ascend/Nvidia
	GpuCards     []string                  //  指定的gpu卡号
	AscendConfig *common.AscendResourceCfg //  gpuType=Ascend时使用: ascend配置
}

func (r *ResourceRequirements) ToK8s(ctx context.Context) (*v1.ResourceRequirements, error) {
	if r.Limit.GpuCore == 0 {
		r.Limit.GpuCore = r.Request.GpuCore
	}
	if r.Limit.GpuMemory == 0 {
		r.Limit.GpuMemory = r.Request.GpuMemory
	}
	if r.Limit.GpuCount == 0 {
		r.Limit.GpuCount = r.Request.GpuCount
	}
	var gpuCoreLimit, gpuMemLimit string
	if r.Limit.GpuCore != 0 {
		gpuCoreLimit = fmt.Sprintf("%d", r.Limit.GpuCore)
		if r.Limit.GpuCount != 0 {
			gpuCoreLimit = fmt.Sprintf("%d", r.Limit.GpuCore*r.Limit.GpuCount)
		}
	}
	if r.Limit.GpuMemory != 0 {
		gpuMemLimit = fmt.Sprintf("%dMi", r.Limit.GpuMemory)
	}
	if r.GpuType == "" && (gpuCoreLimit != "" || gpuMemLimit != "") {
		r.GpuType = serving.GPUTypeNvidia
	}
	return serving.ResourceToSeldon(ctx, &servingpb.Resource{
		CpuRequest:    fmt.Sprintf("%dm", r.Request.Cpu),
		MemoryRequest: fmt.Sprintf("%dMi", r.Request.Memory),
		CpuLimit:      fmt.Sprintf("%dm", r.Limit.Cpu),
		MemoryLimit:   fmt.Sprintf("%dMi", r.Limit.Memory),
		GpuCore:       gpuCoreLimit,
		GpuMemory:     gpuMemLimit,
		// GpuCount: "",
		GpuType: r.GpuType,
		AscendConfig: &servingpb.AscendResourceCfg{
			NpuName:      r.AscendConfig.GetNpuName(),
			TemplateName: r.AscendConfig.GetTemplateName(),
			Cnt:          int32(r.AscendConfig.GetCnt()),
		},
	})
}

func (r *ResourceRequirements) FromK8s(resourceRequirements *v1.ResourceRequirements, src string) *ResourceRequirements {
	err := json.Unmarshal([]byte(src), r)
	if err == nil {
		return r
	} else {
		stdlog.Warn(err)
	}
	var Mi int64 = 1024 * 1024

	requestGpuCnt := resourceRequirements.Requests[serving.ResourceGPUCntHami]
	requestGpuCore := resourceRequirements.Requests[serving.ResourceGpuCoreHami]
	requestGpuMemory := resourceRequirements.Requests[serving.ResourceGpuMemoryHami]

	limitGpuCnt := resourceRequirements.Limits[serving.ResourceGPUCntHami]
	limitGpuCore := resourceRequirements.Limits[serving.ResourceGpuCoreHami]
	limitGpuMemory := resourceRequirements.Limits[serving.ResourceGpuMemoryHami]

	if !requestGpuCnt.IsZero() {
		requestGpuCore.Set(requestGpuCore.Value() * requestGpuCnt.Value())
	}
	if !limitGpuCnt.IsZero() {
		limitGpuCore.Set(limitGpuCore.Value() * limitGpuCnt.Value())
	}
	r.Request = &common.Resource{
		Memory:    int32(resourceRequirements.Requests.Memory().Value() / Mi),
		Cpu:       int32(resourceRequirements.Requests.Cpu().Value() * 1000),
		GpuCore:   int32((&requestGpuCore).Value()),
		GpuMemory: int32((&requestGpuMemory).Value()),
	}
	r.Limit = &common.Resource{
		Memory:    int32(resourceRequirements.Limits.Memory().Value() / Mi),
		Cpu:       int32(resourceRequirements.Limits.Memory().Value() * 1000),
		GpuCore:   int32((&limitGpuCore).Value()),
		GpuMemory: int32((&limitGpuMemory).Value()),
	}
	return r
}

func (r *ResourceRequirements) FromPb(pb *common.ResourceRequirements) {
	r.Limit = pb.ResourceLimit
	r.Request = pb.ResourceRequest
	r.GpuType = pb.GpuType
	r.GpuCards = pb.GpuCards
	r.AscendConfig = pb.AscendConfig
}

func (r *ResourceRequirements) ToPb() *common.ResourceRequirements {
	if r == nil {
		return nil
	}
	return &common.ResourceRequirements{
		ResourceLimit:   r.Limit,
		ResourceRequest: r.Request,
		GpuType:         r.GpuType,
		GpuCards:        r.GpuCards,
		AscendConfig:    r.AscendConfig,
	}
}
