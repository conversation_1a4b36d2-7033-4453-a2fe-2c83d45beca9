package model

import (
	apiv1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/aip/llmops-common/pb/serving"
)

type Node struct {
	Id             string
	TemplateSource *pb.TemplateSource
	Inputs         []*pb.ArtifactConfig
	Outputs        []*pb.ArtifactConfig
	Params         map[string]string
	Name           string
	Container      *Container
	Position       *pb.Position

	// pod

	NodeSelector   map[string]string // Deprecated V2
	Annotations    map[string]string // Deprecated V2, Metadata
	Labels         map[string]string // Deprecated V2, Metadata
	VolumeCfgs     *VolumeCfgs
	AdvancedConfig *ComponentAdvancedConfig
	isPowerRule    bool // Deprecated V2

	UnifyResource *serving.UnifyResource // V2
	Arch          string
}

func (node *Node) FromPb(pb *pb.Node) *Node {
	node.Id = pb.Id
	node.TemplateSource = pb.TemplateSource
	node.Inputs = pb.Inputs
	node.Outputs = pb.Outputs
	node.Params = pb.Params
	node.Name = pb.Name
	node.Position = pb.Position
	if pb.Container != nil {
		node.Container = &Container{}
		node.Container.FromPb(pb.Container)
	}
	node.NodeSelector = pb.NodeSelector
	node.Annotations = pb.Annotations
	node.Labels = pb.Labels
	node.AdvancedConfig = &ComponentAdvancedConfig{}
	node.AdvancedConfig.FromPb(pb.AdvancedConfig)
	node.VolumeCfgs = &VolumeCfgs{}
	*node.VolumeCfgs = pb.Volume
	node.isPowerRule = pb.IsPowerRule
	node.Arch = pb.Arch
	return node
}

func (node *Node) ToPb() *pb.Node {
	if node == nil {
		return nil
	}
	var volume []*common.VolumeCfg
	if node.VolumeCfgs != nil {
		volume = *node.VolumeCfgs
	}
	return &pb.Node{
		Id:             node.Id,
		TemplateSource: node.TemplateSource,
		Inputs:         node.Inputs,
		Outputs:        node.Outputs,
		Params:         node.Params,
		Name:           node.Name,
		Container:      node.Container.ToPb(),
		Position:       node.Position,
		NodeSelector:   node.NodeSelector,
		Annotations:    node.Annotations,
		Labels:         node.Labels,
		AdvancedConfig: node.AdvancedConfig.ToPb(),
		Volume:         volume,
		IsPowerRule:    node.isPowerRule,
		Arch:           node.Arch,
	}
}

func (node *Node) conformComponent() *Node {
	if node.TemplateSource == nil || node.TemplateSource.TemplateType != pb.TaskSourceType_PIPELINE {
		return node
	}
	if node.TemplateSource.SourceId == "" ||
		node.TemplateSource.SourceId == "C00000" ||
		node.TemplateSource.SourceId == "C00005" {
		return node
	}
	component := ComponentMap[node.TemplateSource.SourceId]
	if component == nil {
		return node
	}
	node.TemplateSource.SourceName = component.Name
	node.TemplateSource.SourceInfo = ""

	if node.Name == "" {
		node.Name = component.Name
	}
	if node.Container == nil {
		node.Container = &Container{}
	}
	node.Container.Command = component.Container.Cmds
	node.Container.Image = component.Container.Image
	node.Container.Args = component.Container.Args

	for k, v := range node.Container.Env {
		component.Container.Envs[k] = v
	}
	node.Container.Env = component.Container.Envs

	if node.Container.Resources == nil {
		node.Container.Resources = &ResourceRequirements{}
		node.Container.Resources.FromPb(component.Container.ResourceRequirements)
	}
	switch component.Container.ImagePullPolicy {
	case 1:
		node.Container.PullPolicy = apiv1.PullNever
	case 2:
		node.Container.PullPolicy = apiv1.PullIfNotPresent
	default:
		node.Container.PullPolicy = apiv1.PullAlways
	}
	node.Container.SecurityContext = &SecurityContext{}
	node.Container.SecurityContext.FromPb(component.Container.SecurityContext)
	node.Container.MountPaths = &MountPaths{}
	*node.Container.MountPaths = component.Container.MountPaths

	node.NodeSelector = component.NodeSelector
	node.AdvancedConfig = &ComponentAdvancedConfig{}
	node.AdvancedConfig.FromPb(component.ComponentAdvancedConfig)
	node.VolumeCfgs = &VolumeCfgs{}
	*node.VolumeCfgs = component.Volume
	return node
}

func (node *Node) FromPbV2(pb *pb.NodeV2) *Node {
	if node == nil {
		node = &Node{}
	}
	node.Id = pb.GetId()
	node.TemplateSource = pb.GetTemplateSource()
	node.Inputs = pb.GetInputs()
	node.Outputs = pb.GetOutputs()
	node.Params = pb.GetParams()
	node.Name = pb.GetName()
	node.Position = pb.GetPosition()
	if pb.GetContainer() != nil {
		node.Container = &Container{}
		pb.Container.ResourceRequirements = nil
		node.Container.FromPb(pb.GetContainer())
		node.Container.Resources = nil
	}
	node.AdvancedConfig = &ComponentAdvancedConfig{}
	node.AdvancedConfig.FromPb(pb.GetAdvancedConfig())
	node.VolumeCfgs = &VolumeCfgs{}
	*node.VolumeCfgs = pb.GetVolume()
	node.UnifyResource = pb.GetUnifyResource()
	return node
}

func (node *Node) ToPbV2() *pb.NodeV2 {
	if node == nil {
		return nil
	}
	var volume []*common.VolumeCfg
	if node.VolumeCfgs != nil {
		volume = *node.VolumeCfgs
	}
	return &pb.NodeV2{
		Id:             node.Id,
		TemplateSource: node.TemplateSource,
		Inputs:         node.Inputs,
		Outputs:        node.Outputs,
		Params:         node.Params,
		Name:           node.Name,
		Container:      node.Container.ToPb(),
		Position:       node.Position,
		AdvancedConfig: node.AdvancedConfig.ToPb(),
		Volume:         volume,
		UnifyResource:  node.UnifyResource,
	}
}
