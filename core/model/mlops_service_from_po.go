package model

import (
	"strings"

	"github.com/jinzhu/copier"
	"transwarp.io/aip/llmops-common/pb/common"
	llmpb "transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/serving/dao/models"
)

/*PO转换为DO相关方法*/

func ContainerTypePO2DO(t *int32) llmpb.ContainerType {
	if t == nil {
		return llmpb.ContainerType_CONTAINER_TYPE_MAIN
	}
	return llmpb.ContainerType(*t)
}

// mLOpsSvcContainerPO2DO 不对外暴露，可以通过服务信息获取容器信息
func mLOpsSvcContainerPO2DO(model *models.MlopsServiceContainerInfo) (*MLOpsContainer, error) {
	res := &MLOpsContainer{}
	if err := copier.Copy(&res, &model); err != nil {
		return nil, err
	}
	res.ContainerType = ContainerTypePO2DO(model.ContainerType)
	res.CreateTime = model.CreateAt.Unix()
	res.UpdateTime = model.UpdatedAt.Unix()
	if model.ImageType != nil {
		if _, ok := llmpb.ImageType_name[*model.ImageType]; !ok {
			return nil, stderr.MLOpsServiceModelTransferErr.Error("invalid image type :%v", *model.ImageType)
		}
		res.ImageType = llmpb.ImageType(*model.ImageType)
	}
	if model.ResourceLimit != "" {
		var resource *llmpb.Resource
		util.FromJson(model.ResourceLimit, &resource)
		res.Resource = resource
	}

	// fixme workaround
	if res.Resource != nil {
		if len(res.Resource.CpuRequest) == 0 {
			res.Resource.CpuRequest = res.Resource.Cpu
		}

		if len(res.Resource.MemoryRequest) == 0 {
			res.Resource.MemoryRequest = res.Resource.Memory
		}
		if len(res.Resource.MemoryLimit) == 0 {
			res.Resource.MemoryLimit = res.Resource.MemoryRequest
		}
		if len(res.Resource.CpuLimit) == 0 {
			res.Resource.CpuLimit = res.Resource.CpuRequest
		}
	}
	if model.ResourceGroups != "" {
		var rgs []*llmpb.ResourceGroupInfo
		util.FromJson(model.ResourceGroups, &rgs)
		res.ResourceGroups = rgs
	}
	if model.ReadinessProbe != "" {
		var probe *llmpb.MLOpsProbe
		util.FromJson(model.ReadinessProbe, &probe)
		res.ReadinessProbe = probe
	}
	if model.LivenessProbe != "" {
		var probe *llmpb.MLOpsProbe
		util.FromJson(model.LivenessProbe, &probe)
		res.LivenessProve = probe
	}
	if model.Envs != "" {
		envs := make(map[string]string)
		util.FromJson(model.Envs, &envs)
		res.Envs = envs
	}
	if model.CmDs != "" {
		cmds := make([]string, 0)
		util.FromJson(model.CmDs, &cmds)
		res.Cmds = cmds
	}
	if model.MountPaths != "" {
		var mountCfg []*MountPath
		util.FromJson(model.MountPaths, &mountCfg)
		res.MountPaths = mountCfg
	}
	return res, nil
}

func mLOpsSvcContainerPO2DOBatch(models []*models.MlopsServiceContainerInfo) ([]*MLOpsContainer, error) {
	res := make([]*MLOpsContainer, 0)
	for _, m := range models {
		DO, err := mLOpsSvcContainerPO2DO(m)
		if err != nil {
			return nil, err
		}
		res = append(res, DO)
	}
	return res, nil

}

func ServiceUnlimitedGPUPO2DO(param *int32) bool {
	if param == nil {
		return false
	}
	if *param == MLOpsServiceUnLimitedGPU {
		return true
	}
	return false
}

func ServiceEnableGPUPO2DO(param *int32) bool {
	if param == nil {
		return false
	}
	if *param == MLOpsServiceEnableGPU {
		return true
	}
	return false
}

func ServiceHostNetworkPO2DO(param *int32) bool {
	if param == nil {
		return false
	}
	if *param == MLOpsServiceEnableHostNetwork {
		return true
	}
	return false
}

// mLOpsSvcVersionPO2DO 不对外暴露，可以通过服务信息获取版本信息，直接用此方法会丢失widget、shadow等信息
func mLOpsSvcVersionPO2DO(svcVersion *models.MlopsServiceVersionInfo,
	containers []*models.MlopsServiceContainerInfo) (*MLOpsServiceVersionInfoDO, error) {
	res := &MLOpsServiceVersionInfoDO{}
	if err := copier.Copy(&res, &svcVersion); err != nil {
		return nil, err
	}
	res.EnableGPU = ServiceEnableGPUPO2DO(svcVersion.EnableGpu)
	res.HostNetWork = ServiceHostNetworkPO2DO(svcVersion.HostNetwork)
	res.CreateTime = svcVersion.CreateAt.Unix()
	res.UpdateTime = svcVersion.UpdatedAt.Unix()
	containersDO, err := mLOpsSvcContainerPO2DOBatch(containers)
	if err != nil {
		return nil, err
	}
	res.Containers = containersDO
	if svcVersion.NodeChooseCfg != "" {
		var cfg *llmpb.NodeChooseCfg
		util.FromJson(svcVersion.NodeChooseCfg, &cfg)
		res.NodeChooseCfg = cfg
	}

	if svcVersion.RateLimit != "" {
		var rate *RateLimit
		util.FromJson(svcVersion.RateLimit, &rate)
		res.RateLimit = rate
	}

	if svcVersion.UserRateLimit != "" {
		var rate *RateLimit
		util.FromJson(svcVersion.UserRateLimit, &rate)
		res.UserRateLimit = rate
	}

	if svcVersion.SourceMeta != "" {
		var meta *llmpb.SourceMeta
		util.FromJson(svcVersion.SourceMeta, &meta)
		res.SourceMeta = meta
	}
	if svcVersion.Annotations != "" {
		annotations := make(map[string]string)
		util.FromJson(svcVersion.Annotations, &annotations)
		res.Annotations = annotations
	}
	//if svcVersion.GpuCfg != "" {
	//	var cfg *llmpb.GPUConfig
	//	util.FromJson(svcVersion.GpuCfg, &cfg)
	//	res.GPUCfg = cfg
	//}
	if svcVersion.HpaCfg != "" {
		var cfg *llmpb.HPACfg
		util.FromJson(svcVersion.HpaCfg, &cfg)
		res.HpaCfg = cfg
	}
	if svcVersion.GpuGroup != "" {
		var group []string
		util.FromJson(svcVersion.GpuGroup, &group)
		res.GpuGroup = group
	}
	if svcVersion.VolumeCfg != "" {
		var cfg []*VolumeCfg
		util.FromJson(svcVersion.VolumeCfg, &cfg)
		res.VolumeCfgs = cfg
	}
	if svcVersion.Headers != "" {
		var headers map[string]string
		util.FromJson(svcVersion.Headers, &headers)
		res.Headers = headers
	}
	if svcVersion.QueryParams != "" {
		var query map[string]string
		util.FromJson(svcVersion.QueryParams, &query)
		res.QueryParams = query
	}
	return res, nil
}

func mLOpsSvcVersionPO2DOBatch(svcVersions []*models.MlopsServiceVersionInfo,
	containers []*models.MlopsServiceContainerInfo) ([]*MLOpsServiceVersionInfoDO, error) {
	res := make([]*MLOpsServiceVersionInfoDO, 0)
	m := make(map[string][]*models.MlopsServiceContainerInfo)
	for _, c := range containers {
		if r, ok := m[c.ServiceVersionID]; ok {
			m[c.ServiceVersionID] = append(r, c)
		} else {
			m[c.ServiceVersionID] = []*models.MlopsServiceContainerInfo{c}
		}
	}
	for _, v := range svcVersions {
		containerPOs, ok := m[v.ID]
		if !ok {
			containerPOs = make([]*models.MlopsServiceContainerInfo, 0)
		}
		svcVersionDO, err := mLOpsSvcVersionPO2DO(v, containerPOs)
		if err != nil {
			return nil, err
		}
		res = append(res, svcVersionDO)
	}
	return res, nil
}
func ServiceIsAsyncPO2DO(isAsync *int32) bool {
	if isAsync == nil {
		return false
	}
	if *isAsync == MLOpsServiceSync {
		return false
	}
	return true
}

func IntPtrToBool(ptr *int32) bool {
	if ptr == nil {
		return false
	}
	if *ptr == ZERO {
		return false
	}
	return true
}

func ServiceIsSharePO2DO(isShare *int32) bool {
	if isShare == nil {
		return false
	}
	if *isShare == MLOpsServiceUnShared {
		return false
	}
	return true
}

func MLOpsEndpointsPO2DO(apiStr string) []*common.Endpoint {
	res := make([]*common.Endpoint, 0)
	if apiStr != "" {
		// 新数据
		if strings.Contains(apiStr, "api_attrs") {
			util.FromJson(apiStr, &res)
		} else {
			// 老数据
			oldApis := make([]*llmpb.API, 0)
			util.FromJson(apiStr, &oldApis)
			for _, api := range oldApis {
				apiAttrs := make([]*common.APIAttr, 0)
				for _, a := range api.Url {
					apiAttrs = append(apiAttrs, &common.APIAttr{
						ApiPath:    a,
						ReqExample: api.UrlParamMap[a],
						Method:     common.HttpMethod_HTTP_METHOD_POST,
						ApiType:    common.APIType_API_TYPE_OTHERS,
					})
				}
				res = append(res, &common.Endpoint{
					Port:      api.Port,
					Type:      GetEndpointType(api.Type),
					ApiAttrs:  apiAttrs,
					IsDefault: false,
				})
			}
		}

	}
	return res
}

func MLOpsSvcBaseInfoPO2DO(svc *models.MlopsServiceInfo) (*MLOpsServiceBaseInfoDO, error) {
	res := &MLOpsServiceBaseInfoDO{}
	if err := copier.Copy(&res, &svc); err != nil {
		return nil, err
	}
	res.IsAsync = ServiceIsAsyncPO2DO(svc.Async)
	res.CreateTime = svc.CreateAt.Unix()
	res.UpdateTime = svc.UpdatedAt.Unix()
	res.ShareConfig = &ShareConfig{
		IsShare: ServiceIsSharePO2DO(svc.Share),
	}
	if svc.DeployCfg != "" {
		var cfg *llmpb.DeployCfg
		util.FromJson(svc.DeployCfg, &cfg)
		res.DeployCfg = cfg
	} else {
		res.DeployCfg = nil
	}
	res.GuardrailsConfig = &GuardrailsConfig{
		IsSecurity:   IntPtrToBool(svc.EnableSafetyCfg),
		GuardrailsId: svc.GuardrailsID,
	}
	if svc.SourceMeta != "" {
		var meta *llmpb.SourceMeta
		util.FromJson(svc.SourceMeta, &meta)
		res.SourceMeta = meta
	}
	//if svc.Apis != "" {
	//var apis []*llmpb.API
	//util.FromJson(svc.Apis, &apis)
	//res.APIs = apis
	//}
	if svc.Endpoints.Data() == nil {
		res.Endpoints = MLOpsEndpointsPO2DO(svc.Apis)
	} else {
		res.Endpoints = *svc.Endpoints.Data()
	}
	if svc.Labels != "" {
		var labels map[string][]string
		util.FromJson(svc.Labels, &labels)
		res.Labels = labels
	}
	if svc.ShareUsers != "" {
		var shareUsers []string
		util.FromJson(svc.ShareUsers, &shareUsers)
		res.ShareConfig.ShareUsers = shareUsers
	}
	if svc.ShareGroups != "" {
		var shareGroups []string
		util.FromJson(svc.ShareGroups, &shareGroups)
		res.ShareConfig.ShareGroups = shareGroups
	}
	res.BillingConfig = &BillingConfig{
		Type:                   svc.BillingType,
		PricePerThousandTokens: svc.PricePerThousandTokens,
		PricePerRequest:        svc.PricePerRequest,
	}
	if svc.ApprovalStateInfo != "" {
		var approvalState *ApprovalStateInfo
		util.FromJson(svc.ApprovalStateInfo, &approvalState)
		res.ApprovalState = approvalState
	}

	return res, nil
}

func MLOpsSvcBaseInfoBatchPO2DO(svcs []*models.MlopsServiceInfo) ([]*MLOpsServiceBaseInfoDO, error) {
	res := make([]*MLOpsServiceBaseInfoDO, 0)
	for _, svc := range svcs {
		DO, err := MLOpsSvcBaseInfoPO2DO(svc)
		if err != nil {
			return nil, err
		}
		res = append(res, DO)
	}
	return res, nil
}

func MLOpsSvcPO2DO(svc *models.MlopsServiceInfo, svcVersions []*models.MlopsServiceVersionInfo,
	containers []*models.MlopsServiceContainerInfo) (*MLOpsServiceDO, error) {
	DO, err := MLOpsSvcBaseInfoPO2DO(svc)
	if err != nil {
		return nil, err
	}
	res := &MLOpsServiceDO{
		BaseInfo: *DO,
	}
	svcVersionDOs, err := mLOpsSvcVersionPO2DOBatch(svcVersions, containers)
	if err != nil {
		return nil, err
	}
	// 注入端口
	for _, svc := range svcVersionDOs {
		for _, c := range svc.Containers {
			//c.APIs = res.BaseInfo.APIs
			c.Endpoints = res.BaseInfo.Endpoints
		}
	}
	// 注入灰度发布，影子部署等属性
	deployCfg := res.BaseInfo.DeployCfg
	svcVersionMap := make(map[string]*MLOpsServiceVersionInfoDO)
	for _, v := range svcVersionDOs {
		svcVersionMap[v.ID] = v
	}
	if deployCfg != nil && deployCfg.DeployStrategy != nil {
		switch *deployCfg.DeployStrategy {
		case llmpb.DeployStrategy_DEPLOY_STRATEGY_MAIN_DEPLOY:
			svc, ok := svcVersionMap[deployCfg.MainDeployVersion]
			if !ok {
				return nil, stderr.MLOpsServiceModelTransferErr.Error("no svc version id :%v", deployCfg.MainDeployVersion)
			}
			svc.Widget = 100
		case llmpb.DeployStrategy_DEPLOY_STRATEGY_SHADOW_DEPLOY:
			if len(deployCfg.Shadows) == 0 {
				return nil, err
			}
			champion, ok := svcVersionMap[deployCfg.Champion]
			if !ok {
				return nil, stderr.MLOpsServiceModelTransferErr.Error("no svc version id :%v", deployCfg.Champion)
			}
			shadow, ok := svcVersionMap[deployCfg.Shadows[0]]
			if !ok {
				return nil, stderr.MLOpsServiceModelTransferErr.Error("no svc version id :%v", deployCfg.Champion)
			}
			champion.Widget = 100
			shadow.Widget = 100
			shadow.Shadow = true
		case llmpb.DeployStrategy_DEPLOY_STRATEGY_GRAY_DEPLOY:
			widgets := deployCfg.Widget
			for k, v := range widgets {
				svc, ok := svcVersionMap[k]
				if !ok {
					return nil, stderr.MLOpsServiceModelTransferErr.Error("no svc version id :%v", deployCfg.Champion)
				}
				svc.Widget = v
			}
		}
	}
	res.Versions = svcVersionDOs
	return res, nil
}
