package client

import (
	"context"
	"fmt"
	v1 "k8s.io/api/core/v1"
	"os"
	"time"
)

type CsmClient struct {
	*HTTPClient
}

func (m *CsmClient) Init() *CsmClient {
	url := os.Getenv("CSM_CLIENT_URL")
	if url == "" {
		url = "http://autocv-csm-service:80"
	}
	return &CsmClient{
		HTTPClient: NewHTTPClient(
			WithBaseURL(url),
			<PERSON><PERSON><PERSON><PERSON>("Content-Type", "application/json"),
			With<PERSON>eader("accept", "application/json"),
			WithTimeout(30*time.Second),
		),
	}
}

func (m *CsmClient) PodTemplate(ctx context.Context, projectId, id string) (*v1.Pod, error) {
	requestUrl := fmt.Sprintf("/api/v1/codespace/instances/%s/pod-template?project_id=%s", id, projectId)
	var result *v1.Pod
	method := "GET"
	// err := m.doRequest(ctx, method, requestUrl, record, &result)
	err := m.DoRequest(ctx, method, requestUrl, nil, &result)
	if err != nil {
		return result, err
	}
	return result, nil
}
