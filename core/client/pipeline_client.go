package client

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"strconv"
	"time"

	"github.com/argoproj/argo/pkg/apis/workflow/v1alpha1"
	"github.com/google/uuid"
	"github.com/kubeflow/pipelines/backend/api/go_client"
	"google.golang.org/grpc"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	logger "transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/mlops-std/util"
	config "transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/core/model"
	dao_model "transwarp.io/mlops/pipeline/dao/model"
)

const (
	MAX_MESSAGE_LENGTH            = 256 * 1024 * 1024
	Platform                      = "platform"
	Sophon                        = "sophon"
	NODENAME                      = "node_name"
	COMPONENTID                   = "component_id"
	OWNER                         = "owner"
	ResourceJson                  = "resourceJson"
	RunOnNode                     = "runOnNode"
	K8sHostname                   = "kubernetes.io/hostname"
	SophonNfsHostPath             = "SophonNfsHostPath"
	SophonNfsVolumeName           = "sophonnfs"
	NOTEBOOK_CONTAINER_PRIVILEGED = "NOTEBOOK_CONTAINER_PRIVILEGED"
	JupyterUsrHome                = "JUPYTER_USR_HOME"
)

type PipelineClient struct {
	pipelineClient go_client.PipelineServiceClient
	runClient      go_client.RunServiceClient
	jobClient      go_client.JobServiceClient
}

func NewPipelineClient() (*PipelineClient, error) {
	pipelineConfig := config.PipeineConfig.MLOPS.Pipeline
	conn, err := grpc.Dial(fmt.Sprintf("%s:%d", pipelineConfig.Host, pipelineConfig.Port), grpc.WithInsecure(),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(MAX_MESSAGE_LENGTH)))
	if err != nil {
		logger.Error("did not connect :", err)
		return nil, err
	}
	pipelineClient := go_client.NewPipelineServiceClient(conn)
	runClient := go_client.NewRunServiceClient(conn)
	jobClient := go_client.NewJobServiceClient(conn)

	return &PipelineClient{
		pipelineClient: pipelineClient,
		runClient:      runClient,
		jobClient:      jobClient,
	}, nil
}

func checkTaskFlow(taskFlow *model.TaskFlow) error {
	if taskFlow.Nodes == nil || len(taskFlow.Nodes) == 0 {
		return stderr.PipelineTaskFlowIsNil.Error()
	}
	return nil
}

func (p *PipelineClient) SubmitRun(ctx context.Context, run *model.Run, experiment *dao_model.Experiment, tenantUid string) (string, error) {
	err := checkTaskFlow(run.TaskFlow)
	if err != nil {
		return "", err
	}
	run.CreateUser = util.GetUsername(ctx)
	workflowManifest, err := run.ToWorkflow(ctx, tenantUid)
	if err != nil {
		return "", err
	}
	runName := uuid.New().String()
	createRunRequest := &go_client.CreateRunRequest{
		Run: &go_client.Run{
			Name:        runName,
			Description: run.ToDesc(),
			PipelineSpec: &go_client.PipelineSpec{
				WorkflowManifest: util.ToJson(workflowManifest),
			},
			ServiceAccount: model.PipelineServiceAccount,
			ResourceReferences: []*go_client.ResourceReference{
				{
					Key: &go_client.ResourceKey{
						Type: go_client.ResourceType_EXPERIMENT,
						Id:   experiment.UUID,
					},
					Name:         experiment.Namespace,
					Relationship: go_client.Relationship_OWNER,
				},
			},
		},
	}
	runDetail, err := p.runClient.CreateRun(ctx, createRunRequest)
	if err != nil {
		return "", err
	}
	return runDetail.GetRun().GetId(), nil
}

func (p *PipelineClient) SubmitRunV2(ctx context.Context, run *model.Run, experiment *dao_model.Experiment, tenantUid string) (string, error) {
	return p.SubmitRun(ctx, run, experiment, tenantUid)
}

func (p *PipelineClient) StartPipelineVersion(ctx context.Context, pipeline *model.Pipeline, pipelineVersion *model.PipelineVersion, experiment *dao_model.Experiment, tenantUid string) (string, error) {
	err := checkTaskFlow(pipelineVersion.TaskFlow)
	if err != nil {
		return "", err
	}
	workflowManifest, err := pipelineVersion.ToWorkflow(ctx, pipeline, tenantUid)
	if err != nil {
		return "", err
	}
	createJobRequest := &go_client.CreateJobRequest{
		Job: &go_client.Job{
			Id:          pipelineVersion.Id,
			Name:        pipelineVersion.Id,
			Description: pipelineVersion.Desc,
			PipelineSpec: &go_client.PipelineSpec{
				WorkflowManifest: util.ToJson(workflowManifest),
			},
			ResourceReferences: []*go_client.ResourceReference{
				{
					Key: &go_client.ResourceKey{
						Type: go_client.ResourceType_EXPERIMENT,
						Id:   experiment.UUID,
					},
					Name:         experiment.Namespace,
					Relationship: go_client.Relationship_OWNER,
				},
			},
			ServiceAccount: model.PipelineServiceAccount,
			Trigger: &go_client.Trigger{
				Trigger: &go_client.Trigger_CronSchedule{
					CronSchedule: &go_client.CronSchedule{
						Cron: pipelineVersion.TimingConfig.Cron,
					},
				},
			},
		},
	}
	maxConcurrency := int64(pipelineVersion.TimingConfig.MaximumConcurrent)
	if maxConcurrency == 0 {
		maxConcurrency = int64(config.PipeineConfig.MLOPS.Pipeline.MaxConcurrency)
	}
	if maxConcurrency == 0 {
		maxConcurrency = 10
	}
	createJobRequest.Job.MaxConcurrency = maxConcurrency
	job, err := p.jobClient.CreateJob(ctx, createJobRequest)
	if err != nil {
		return "", err
	}
	_, err = p.jobClient.EnableJob(ctx, &go_client.EnableJobRequest{
		Id: job.Id,
	})
	if err != nil {
		return "", err
	}
	return job.Name, nil
}

func (p *PipelineClient) RetryRun(ctx context.Context, runId string) error {
	_, err := p.runClient.RetryRun(ctx, &go_client.RetryRunRequest{
		RunId: runId,
	})
	return err
}

func (p *PipelineClient) DisablePipelineJob(ctx context.Context, ids []string) error {
	for _, id := range ids {
		_, err := p.jobClient.DisableJob(ctx, &go_client.DisableJobRequest{
			Id: id,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *PipelineClient) DeletePipelineJob(ctx context.Context, ids []string) error {
	for _, id := range ids {
		_, err := p.jobClient.DeleteJob(ctx, &go_client.DeleteJobRequest{
			Id: id,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func toMap(nodes []*pb.Node) map[string]*pb.Node {
	nodeMap := map[string]*pb.Node{}
	for _, node := range nodes {
		nodeMap[node.Id] = node
	}
	return nodeMap
}

func (p *PipelineClient) GetRunState(ctx context.Context, runId string) (pb.Run_Status, error) {
	runDetail, err := p.runClient.GetRun(ctx, &go_client.GetRunRequest{
		RunId: runId,
	})
	if err != nil {
		return pb.Run_Error, nil
	}

	return pb.Run_Status(pb.Run_Status_value[runDetail.Run.Status]), nil
}

func (p *PipelineClient) ListRun(ctx context.Context, runId string) (pb.Run_Status, error) {
	runDetail, err := p.runClient.GetRun(ctx, &go_client.GetRunRequest{
		RunId: runId,
	})
	if err != nil {
		return pb.Run_Error, nil
	}

	return pb.Run_Status(pb.Run_Status_value[runDetail.Run.Status]), nil
}

func (p *PipelineClient) TerminateRun(ctx context.Context, runId string) error {
	_, err := p.runClient.TerminateRun(ctx, &go_client.TerminateRunRequest{
		RunId: runId,
	})
	if err != nil {
		return err
	}
	return nil
}

func (p *PipelineClient) GetRun(ctx context.Context, runId string, pipelineVersion *pb.PipelineVersion) (*pb.Run, error) {
	runDetail, err := p.runClient.GetRun(ctx, &go_client.GetRunRequest{
		RunId: runId,
	})
	if err != nil {
		return nil, err
	}
	run := &pb.Run{
		Id:            runDetail.Run.Id,
		StartTime:     runDetail.Run.CreatedAt.Seconds,
		EndTime:       runDetail.Run.FinishedAt.Seconds,
		Status:        pb.Run_Status(pb.Run_Status_value[runDetail.Run.Status]),
		ScheduledTime: runDetail.Run.ScheduledAt.Seconds,
	}
	var workflow v1alpha1.Workflow
	util.FromJson(runDetail.PipelineRuntime.WorkflowManifest, &workflow)
	var steps []*pb.Step
	nodeMap := toMap(pipelineVersion.TaskFlow.Nodes)
	for _, node := range workflow.Status.Nodes {
		if node.Type == v1alpha1.NodeTypePod {
			var inputs []*pb.Artifact
			var outputs []*pb.Artifact
			params := map[string]string{}
			if node.Inputs != nil {
				for _, param := range node.Inputs.Parameters {
					params[param.Name] = *param.Value
				}
				for _, input := range node.Inputs.Artifacts {
					inputs = append(inputs, &pb.Artifact{
						Name:          input.Name,
						ContainerPath: input.Path,
						S3Path:        fmt.Sprintf("minio://%s/%s", input.S3.Bucket, input.S3.Key),
					})
				}
			}

			if node.Outputs != nil {
				for _, output := range node.Outputs.Artifacts {
					if output.Name == "main-logs" {
						continue
					}
					outputs = append(outputs, &pb.Artifact{
						Name:          output.Name,
						ContainerPath: output.Path,
						S3Path:        fmt.Sprintf("minio://%s/%s", output.S3.Bucket, output.S3.Key),
					})
				}
			}
			runNode := nodeMap[node.TemplateName]
			nodeName := node.TemplateName
			if runNode != nil {
				nodeName = runNode.Name
			}
			step := &pb.Step{
				NodeId:    node.TemplateName,
				Name:      nodeName,
				Phase:     pb.Step_Phase(pb.Step_Phase_value[string(node.Phase)]),
				StartTime: node.StartedAt.Unix(),
				EndTime:   node.FinishedAt.Unix(),
				Outputs:   outputs,
				Inputs:    inputs,
				Params:    params,
			}
			steps = append(steps, step)
		}
	}
	run.Steps = steps
	return run, nil
}

func (p *PipelineClient) GetRunPodNameAndNamespace(ctx context.Context, id string, step string) (string, string, error) {
	runDetail, err := p.runClient.GetRun(ctx, &go_client.GetRunRequest{
		RunId: id,
	})
	if err != nil {
		return "", "", err
	}
	var workflow v1alpha1.Workflow
	util.FromJson(runDetail.PipelineRuntime.WorkflowManifest, &workflow)
	for _, node := range workflow.Status.Nodes {
		if node.TemplateName == step {
			return node.ID, workflow.Namespace, nil
		}
	}
	return "", "", stderr.PipelineStepNotFound.Error()
}

func (p *PipelineClient) GetRunStepLogs(ctx context.Context, id string, step string) (io.Reader, error) {
	reader, _, err := p.GetRunStepArtifact(ctx, id, step, "main-logs")
	return reader, err
}

func (p *PipelineClient) GetRunStepArtifact(ctx context.Context, id string, step string, artifactName string) (io.Reader, string, error) {
	runDetail, err := p.runClient.GetRun(ctx, &go_client.GetRunRequest{
		RunId: id,
	})
	if err != nil {
		return nil, "", err
	}
	var workflow v1alpha1.Workflow
	util.FromJson(runDetail.PipelineRuntime.WorkflowManifest, &workflow)

	serviceCfg := config.PipeineConfig.MLOPS.ServiceConfig
	var limitBytes int64
	if serviceCfg.PodLogLimitBytes != "" {
		limitBytes, err = strconv.ParseInt(serviceCfg.PodLogLimitBytes, 10, 64)
		if err != nil {
			return nil, "", err
		}
	} else {
		limitBytes = int64(1024 * 1024 * 1) // 1MB
	}

	for _, node := range workflow.Status.Nodes {
		if node.TemplateName == step {
			if node.Outputs == nil {
				continue
			}
			for _, output := range node.Outputs.Artifacts {
				if output.Name == artifactName {
					minioClient, err := NewMinioClient(
						config.PipeineConfig.MLOPS.Warehouse.Minio.Endpoint,
						config.PipeineConfig.MLOPS.Warehouse.Minio.AccessKey,
						config.PipeineConfig.MLOPS.Warehouse.Minio.SecretKey,
						!*output.S3.Insecure,
						output.S3.Region,
						6*time.Minute)
					if err != nil {
						return nil, "", err
					}
					artifact, err := minioClient.GetObjectTail(output.S3.Bucket, output.S3.Key, limitBytes)
					if err != nil {
						return nil, "", err
					}
					return artifact, "", nil
				}
			}
		}
	}
	return nil, "", stderr.PipelineStepNotFound.Error()
}

func (p *PipelineClient) DeleteRunById(ctx context.Context, id string) (string, error) {
	_, err := p.runClient.DeleteRun(ctx, &go_client.DeleteRunRequest{
		Id: id,
	})
	res := id
	if err != nil {
		res = ""
	}
	return res, err
}

func toByte(reader io.Reader) []byte {
	buf := new(bytes.Buffer)
	buf.ReadFrom(reader)

	bytes := buf.Bytes()

	return bytes
}
