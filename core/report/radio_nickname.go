package report

var Nickname = map[string]string{
	"special_characters_filter.special_char_ratio":        "特殊字符占比",
	"token_num_filter.num_token":                          "token数量",
	"coherence_evaluator.coherence":                       "一致性得分",
	"gpt_score_evaluator.gpt_score":                       "gpt分数",
	"language_id_score_evaluator.lang":                    "语言",
	"language_id_score_evaluator.lang_score":              "语言得分",
	"perplexity_evaluator.perplexity":                     "困惑度",
	"safety_compliance_evaluator.safety_compliance_state": "安全性",
	"special_characters_evaluator.special_char_ratio":     "特殊字符占比",
	"text_length_evaluator.text_len":                      "文本长度",
	"token_num_evaluator.num_token":                       "token数量",
	"alphanumeric_filter.alpha_token_ratio":               "alpha token占比",
	"alphanumeric_filter.alnum_ratio":                     "alpha占比",
	"average_line_length_filter.avg_line_length":          "平均行长度",
	"coherence_filter.coherence":                          "一致性得分",
	"domain_classifier_filter.domain":                     "领域",
	"educational_value_filter.educational_value":          "教育得分",
	"gpt_score_filter.gpt_score":                          "gpt分数",
	"language_id_score_filter.lang":                       "语言",
	"language_id_score_filter.lang_score":                 "语言得分",
	"perplexity_filter.perplexity":                        "困惑度",
	"rouge_filter.rouge_score":                            "rouge得分",
	"rouge_filter.max_rouge_score":                        "最大rouge得分",
	"safety_compliance_filter.safety_compliance_state":    "安全性",
	"text_length_filter.text_len":                         "文本长度",
	"text_safety_filter.safety_label":                     "安全性",
	"words_num_filter.num_words":                          "单词数量",
}

var RadioTyps = map[string]string{
	"语言":      "string",
	"文本长度":    "int",
	"token数量": "int",
	"平均行长度":   "int",
	"单词数量":    "int",
}
