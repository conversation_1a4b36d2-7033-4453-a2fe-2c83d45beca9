package file_uploader

import (
	"fmt"
	"path/filepath"
	"sync"
	"sync/atomic"
	"transwarp.io/applied-ai/aiot/cvat-backend/clients"
	"transwarp.io/applied-ai/aiot/cvat-backend/dao"
	"transwarp.io/applied-ai/aiot/cvat-backend/helper"
	"transwarp.io/applied-ai/aiot/cvat-backend/models"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type CatalogFileUploader struct {
	BaseTextFileUploader
	fileEntryChan chan string
	format        string
	dstDir        string
	catalogParams *models.CatalogParams
}

func NewCatalogFileUploader(fileUploadParams *models.FileUploadParams) (*CatalogFileUploader, error) {
	catalogFileUploader := &CatalogFileUploader{
		fileEntryChan: make(chan string, fileUploaderMaxSize),
		format:        fileUploadParams.Format,
		catalogParams: fileUploadParams.CatalogParams,
		dstDir:        fileUploadParams.DstDir,
	}
	q := dao.InitQuery()
	catalogFileUploader.BaseTextFileUploader = BaseTextFileUploader{
		uploader:    catalogFileUploader,
		file:        fileUploadParams.Files,
		textManager: NewCatalogTextManager(fileUploadParams.Dataset, fileUploadParams.Version, fileUploadParams.CatalogParams, q),
		dataset:     fileUploadParams.Dataset,
		version:     fileUploadParams.Version,
		dataChan:    make(chan *TextData, fileUploaderMaxSize),
		poolSize:    fileUploadParams.ProcessNum,
		q:           q,
		total:       0,
		StatusMap:   sync.Map{},
	}
	return catalogFileUploader, nil
}

func (c *CatalogFileUploader) PrepareFiles() {
	// 遍历文件，调用tds的文件同步接口将文件存储到指定位置
	defer close(c.fileEntryChan)
	if !helper.ValidateDatasetTextPath(c.version.Path) {
		stdlog.Errorf("CatalogFileUploader: prepareFileByDir, the version file path [%s] is invalid", c.version.Path)
		c.StatusMap.Store("version-file-path", BuildUploadStatusInfo("version-file-path",
			UploadStatusFailed, fmt.Sprintf("the version file path [%s] is invalid", c.version.Path)))
		return
	}
	storageVerDir := helper.GetAbsPath(c.version.Path)
	err := helper.MakeDir(storageVerDir)
	if err != nil {
		stdlog.WithError(err).Errorf("CatalogFileUploader: prepareFileByDir %s failed", storageVerDir)
		c.StatusMap.Store(storageVerDir, BuildUploadStatusInfo(storageVerDir, UploadStatusFailed, err.Error()))
	}
	for _, file := range c.catalogParams.AssociatedFiles {
		if !c.textManager.CheckSuffix(file.Path) {
			stdlog.Errorf(msgIgnoreUnsupportedFormat, file.Path)
			c.StatusMap.Store(file.Path, BuildUploadStatusInfo(file.Path, UploadStatusFailed,
				fmt.Sprintf(msgIgnoreUnsupportedFormat, file.Path)))
			continue
		}
		targetPath := filepath.Join(c.version.Path, c.dstDir, file.Path)
		versionFilePath := filepath.Join(storageVerDir, c.dstDir, file.Path)
		copyReq := &models.FileCrossCopyReq{
			DatasourceUuid: c.catalogParams.DatasourceUuid,
			FilePath:       file.Path,
			TargetPath:     targetPath,
		}
		err = clients.TdsCli.CopyFile(copyReq)
		if err != nil {
			stdlog.WithError(err).Errorf("CatalogFileUploader: prepareFileByDir, copy remote file %s failed", file.Path)
			c.StatusMap.Store(file.Path, BuildUploadStatusInfo(file.Path, UploadStatusFailed, err.Error()))
			continue
		}
		c.fileEntryChan <- versionFilePath
		atomic.AddInt64(&c.total, 1)
		c.StatusMap.Store(versionFilePath, BuildUploadStatusInfo(versionFilePath, UploadStatusWaiting, ""))
	}
	c.SendUploadMessage(c.GetEventId(), c.BuildUploadMessage())
}

func (c *CatalogFileUploader) Upload(wg *sync.WaitGroup) {
	defer wg.Done()
	for file := range c.fileEntryChan {
		textData := c.ProcessTextByFile(file)
		c.dataChan <- textData
	}
}

func (c *CatalogFileUploader) CloseArchive() {
	// do nothing
}

func (c *CatalogFileUploader) ProcessTextByFile(filePath string) *TextData {
	c.StatusMap.Store(filePath, BuildUploadStatusInfo(filePath, UploadStatusInProgress, ""))
	c.SendUploadMessage(c.GetEventId(), c.BuildUploadMessage())
	fileModel, err := c.textManager.ProcessFile(filePath, nil)
	if err != nil {
		c.StatusMap.Store(filePath, BuildUploadStatusInfo(filePath, UploadStatusFailed, err.Error()))
		stdlog.WithError(err).Errorf("ProcessTextByFile: manager process file %s", filePath)
		return c.makeErrorTextRecord(filePath, err)
	}
	return &TextData{
		fileModel: fileModel,
		isError:   false,
	}
}
