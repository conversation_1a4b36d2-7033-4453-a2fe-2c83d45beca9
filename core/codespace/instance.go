package codespace

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"gorm.io/gorm"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"text/template"
	"time"
	"transwarp.io/aip/llmops-common/pkg/client"
	"transwarp.io/aip/llmops-common/pkg/serving"
	"transwarp.io/applied-ai/aiot/csm-backend/clients"
	"transwarp.io/applied-ai/aiot/csm-backend/conf"
	"transwarp.io/applied-ai/aiot/csm-backend/core/codehub"
	"transwarp.io/applied-ai/aiot/csm-backend/core/imagehub"
	"transwarp.io/applied-ai/aiot/csm-backend/core/proxy"
	"transwarp.io/applied-ai/aiot/csm-backend/dao"
	"transwarp.io/applied-ai/aiot/csm-backend/dao/query"
	"transwarp.io/applied-ai/aiot/csm-backend/helper"
	"transwarp.io/applied-ai/aiot/csm-backend/models"
	"transwarp.io/applied-ai/aiot/csm-backend/service/ws"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdmetric"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type InstanceManager interface {
	// 基础的增删改查
	ListInstances(projectID, username string) ([]*models.Instance, error)
	GetInstance(projectID string, id string) (*models.Instance, error)
	GetInstanceWithUsername(projectID, username, id string) (*models.Instance, error)
	UpdateInstance(projectID string, instance *models.Instance) error
	UpdateInstanceInfo(projectID string, instance *models.Instance) error
	CreateInstance(projectID string, instance *models.Instance) error
	DeleteInstance(projectID string, ids ...string) error
	// CommitInstanceImage 固化镜像
	CommitInstanceImage(projectID string, id string) error
	CommitInstanceImageAsync(projectID string, id string) error
	// ExportInstanceData 导出数据
	ExportInstanceData(projectID string, id string, zos *zip.Writer) error
	// BuildImage 构建镜像
	BuildImage(projectID string, id string, image *models.Image) error
	BuildImageAsync(projectID string, id string, image *models.Image) (*models.BuildRecord, error)
	// StartInstance 启动代码实例
	StartInstance(projectID string, id string, username string) error
	StartInstanceAsync(projectID string, id string, username string) error
	// StopInstance 异步方法
	StopInstance(projectID string, id string) error
	GetInstanceLogs(projectID string, id string) (string, error)
	GetInstanceEvents(projectID string, id string) (string, error)
	GetDockerfile(projectID string, id string) (string, error)
	GetInstanceOutputDir(projectID string, id string) (string, error)
	CountInstance(projectID string) (int64, error)
	GetPodInfo(projectID string, id string) (*models.PodInfo, error)
	CreateInstanceService(projectID, id string, infos []*models.NodePortServiceInfo) error
	ListInstanceServices(projectID, id string) ([]*models.NodePortServiceInfo, error)
	DeleteInstanceService(projectID, id, name string) error
	CloneInstance(projectID, targetProjectID, id string, instance *models.Instance) error
}

var (
	tm                    InstanceManager
	tmOnce                sync.Once
	DefaultInstanceCpu    = "2"
	DefaultInstanceMemory = "4Gi"
	hooks                 = []string{
		"apt-get update",
	}
)

func GetInstanceManager() InstanceManager {
	tmOnce.Do(func() {
		im := &instanceManager{
			q:         dao.InitQuery(),
			buildChan: make(chan *models.Image, 20),
			rbac:      cas.NewRBACApi("", conf.Config.Token),
		}
		go im.waitForBuildResult()
		tm = im
	})
	return tm
}

type instanceManager struct {
	q         *query.Query
	buildChan chan *models.Image
	rbac      *cas.RBACApi
}

func (m *instanceManager) CloneInstance(projectID, targetProjectID, id string, ins *models.Instance) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		return stderr.Wrap(err, "Get Instance in clone")
	}
	preIns, err := m.GetInstance(projectID, id)
	if err != nil {
		return stderr.Wrap(err, "Get Instance in clone")
	}
	instance.ID = ""
	instance.Name = ins.Name
	instance.ProjectID = targetProjectID
	instance.Image = nil
	instance.ImageID = ""
	instance.PermissionCfg = &cas.PermissionCfg{
		PermissionMode: cas.PermissionMode_Public,
		PublicType:     cas.PublicType_All,
	}
	err = m.CreateInstance(targetProjectID, instance)
	if err != nil {
		return stderr.Wrap(err, "Create Instance in clone")
	}
	// 还需要复制原本的workspace等路径
	src := filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(preIns), getInstanceDir(preIns.ID))
	dst := filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(instance), getInstanceDir(instance.ID))
	if preIns.Status == "" {
		// 跳过没启动过的实例的拷贝
		return nil
	}
	err = helper.Copy(src, dst)
	if err != nil {
		return stderr.Wrap(err, "Copy Instance file from [%s] to [%s] in clone", preIns.ID, instance.ID)
	}
	return nil
}

func (m *instanceManager) waitForBuildResult() {
	for image := range m.buildChan {
		if image.BuildRecord != nil {
			if image.BuildRecord.Status == models.BuildStatusSuccess {
				// 成功
				info, err := clients.CommitterCli.GetImageInfo(image.BuildRecord.BuilderIp, image.BuildID)
				if err != nil {
					stdlog.WithError(err).Errorf("Get image info by build id [%s] error.", image.BuildID)
				}
				image.Size = info.Size
				image.DockerID = strings.TrimPrefix(info.ID, "sha256:")
				image.Labels[BuildBy] = "csm"
				err = GetImageManager().CreateImage(image.ProjectID, image)
				if err != nil {
					stdlog.WithError(err).Errorf("Update image [%s] error.", image.ID)
				}
				err = GetBuildRecordManager().Success(image.ProjectID, image.BuildID)
				if err != nil {
					stdlog.WithError(err).Errorf("")
				}
				// 发送ws事件
				ws.GetWsServer().BroadcastToAll(ws.BuildSuccess, image.BuildID)
			} else if image.BuildRecord.Status == models.BuildStatusFailed {
				// 失败
				err := GetBuildRecordManager().Fail(image.ProjectID, image.BuildID)
				if err != nil {
					stdlog.WithError(err).Errorf("")
				}
				// 发送ws事件
				ws.GetWsServer().BroadcastToAll(ws.BuildFailed, image.BuildID)
			}
			repo := m.q.Instance
			_, err := repo.Where(repo.ID.Eq(image.InstanceID)).UpdateSimple(repo.Building.Value(models.BitBool(false)))
			if err != nil {
				stdlog.WithError(err).Errorf("update instance [%s] building false error.", image.InstanceID)
			}
		}
	}
}

func (m *instanceManager) ListInstances(projectID, username string) ([]*models.Instance, error) {
	repo := m.q.Instance
	if projectID == "all" {
		ret, err := repo.Preload(repo.Image).Find()
		if err != nil {
			return nil, stderr.Wrap(err, "InstanceManager: ListInstances method, find instances error.")
		}
		return ret, nil
	}
	adminFlag := false
	ids := make(map[string]cas.Act)
	if username != "" {
		rbac, err := m.rbac.ListObject(context.Background(), username, cas.WithProjectId(projectID), cas.WithObjType(cas.ObjType_CodeserverInstance))
		if err != nil {
			return nil, stderr.Wrap(err, "InstanceManager: ListInstances method, get user permission error.")
		}
		if rbac.AccessType == cas.AccessType_Unrestricted {
			// 说明有所有权限
			adminFlag = true
		} else {
			// 有可能是新用户，obj为空
			for _, o := range rbac.Objects {
				ids[o.ObjId] = o.Act
			}
		}
	}
	ret, err := repo.Where(repo.ProjectID.Eq(projectID)).Preload(repo.Image).Find()
	if err != nil {
		return nil, stderr.Wrap(err, "InstanceManager: ListInstances method, find instances error.")
	}
	// 获取act并过滤
	if !adminFlag {
		filtered := make([]*models.Instance, 0)
		for _, r := range ret {
			if r.Creator == username {
				// 创建者无视权限
				r.Action = cas.Act_All
				filtered = append(filtered, r)
				continue
			}
			if r.PermissionCfg == nil {
				// 老数据，认为有所有权限
				r.Action = cas.Act_All
				r.PermissionCfg = &cas.PermissionCfg{
					PermissionMode: cas.PermissionMode_Public,
					PublicType:     cas.PublicType_All,
				}
				filtered = append(filtered, r)
			} else if r.PermissionCfg.PermissionMode == cas.PermissionMode_Public {
				if r.PermissionCfg.PublicType == cas.PublicType_All {
					r.Action = cas.Act_All
				} else if r.PermissionCfg.PublicType == cas.PublicType_Readonly {
					r.Action = cas.Act_ReadOnly
				}
				filtered = append(filtered, r)
			} else if r.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
				if act, ok := ids[r.ID]; ok {
					r.Action = act
					filtered = append(filtered, r)
				}
			}
		}
		return filtered, nil
	} else {
		for _, r := range ret {
			r.Action = cas.Act_All
			if r.PermissionCfg == nil {
				r.PermissionCfg = &cas.PermissionCfg{
					PermissionMode: cas.PermissionMode_Public,
					PublicType:     cas.PublicType_All,
				}
			}
		}
		return ret, nil
	}
}

func (m *instanceManager) GetInstanceWithUsername(projectID, username, id string) (*models.Instance, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return nil, err
	}
	adminFlag := false
	ids := make(map[string]cas.Act)
	if username != "" {
		rbac, err := m.rbac.ListObject(context.Background(), username, cas.WithProjectId(projectID), cas.WithObjType(cas.ObjType_CodeserverInstance))
		if err != nil {
			return nil, stderr.Wrap(err, "InstanceManager: ListInstances method, get user permission error.")
		}
		if rbac.AccessType == cas.AccessType_Unrestricted {
			// 说明有所有权限
			adminFlag = true
		} else {
			// 有可能是新用户，obj为空
			for _, o := range rbac.Objects {
				ids[o.ObjId] = o.Act
			}
		}
	}
	repo := m.q.Instance
	r, err := repo.Where(repo.ID.Eq(id)).Preload(repo.Image).First()
	if err != nil {
		return nil, stderr.Wrap(err, "InstanceManager: GetInstance method, get instance [%s] error.", id)
	}
	if !adminFlag {
		if r.Creator == username {
			// 创建者
			r.Action = cas.Act_All
			return r, nil
		}
		if r.PermissionCfg == nil {
			// 老数据，认为有所有权限
			r.Action = cas.Act_All
			r.PermissionCfg = &cas.PermissionCfg{
				PermissionMode: cas.PermissionMode_Public,
				PublicType:     cas.PublicType_All,
			}
			return r, nil
		} else if r.PermissionCfg.PermissionMode == cas.PermissionMode_Public {
			if r.PermissionCfg.PublicType == cas.PublicType_All {
				r.Action = cas.Act_All
			} else if r.PermissionCfg.PublicType == cas.PublicType_Readonly {
				r.Action = cas.Act_ReadOnly
			}
			return r, nil
		} else if r.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
			if act, ok := ids[r.ID]; ok {
				r.Action = act
				return r, nil
			}
		}
	} else {
		r.Action = cas.Act_All
		return r, nil
	}
	return nil, stderr.Unauthorized.Errorf("forbidden")
}

func (m *instanceManager) GetInstance(projectID, id string) (*models.Instance, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return nil, err
	}
	repo := m.q.Instance
	ret, err := repo.Where(repo.ID.Eq(id)).Preload(repo.Image).First()
	if err != nil {
		return nil, stderr.Wrap(err, "InstanceManager: GetInstance method, get instance [%s] error.", id)
	}
	return ret, nil
}

func (m *instanceManager) UpdateInstance(projectID string, instance *models.Instance) error {
	if err := m.checkProjectIDAndInstanceID(projectID, instance.ID); err != nil {
		return err
	}
	repo := m.q.Instance
	_, err := repo.Where(repo.ID.Eq(instance.ID)).Updates(instance)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: UpdateInstance method, update instance error.")
	}
	return nil
}

func (m *instanceManager) UpdateInstanceInfo(projectID string, instance *models.Instance) error {
	if err := m.checkProjectIDAndInstanceID(projectID, instance.ID); err != nil {
		return err
	}
	repo := m.q.Instance
	_, err := m.GetInstance(projectID, instance.ID)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: UpdateInstance method, get instance error.")
	}
	// 如果需要更新自定义权限
	if instance.PermissionCfg != nil && instance.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
		reqs := make([]*cas.PutObjectReq, 0)
		for _, p := range instance.PermissionCfg.CustomPolicies {
			reqs = append(reqs, p.ToPolicyPutReq(projectID))
		}
		_, err = m.rbac.PutObject(context.Background(), instance.ID, cas.ObjType_CodeserverInstance, reqs)
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: UpdateInstance method, put rbac objects error.")
		}
		// 不保存polices
		instance.PermissionCfg.CustomPolicies = []*cas.PolicyCfg{}
	}
	instance.UpdateAt = time.Now()
	_, err = repo.Where(repo.ID.Eq(instance.ID)).Select(repo.Name, repo.Desc, repo.Resource, repo.DataSource, repo.Keep, repo.DefaultIde, repo.Labels, repo.HostConfig, repo.DistConfigs, repo.UpdateAt, repo.Duration, repo.ResourceId, repo.Env, repo.PodResource, repo.PermissionCfg).Updates(instance)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: UpdateInstance method, update instance error.")
	}
	return nil
}

func (m *instanceManager) checkResourceChanged(prev, now map[string]string) bool {
	return reflect.DeepEqual(prev, now)
}

func (m *instanceManager) CreateInstance(projectID string, instance *models.Instance) error {
	u, err := uuid.NewUUID()
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: CreateInstance method, new uuid error.")
	}
	realDuration := instance.Duration
	instance.ID = u.String()
	instance.ProjectID = projectID
	repo := m.q.Instance
	// 获取项目的tenant_id
	tid, err := stdsrv.GetProjectTenantUid(projectID, conf.Config.Token)
	if err != nil {
		return stderr.Internal.Cause(err, "Get project [%s] info error.", projectID)
	}
	instance.TenantId = tid
	// 镜像
	if instance.ImageConfig == nil {
		return stderr.Internal.Errorf("image config must be set")
	} else {
		if instance.ImageConfig.ImageSource == models.ImageSourceBase {
			if instance.ImageID == "" {
				instance.ImageID = instance.ImageConfig.Image.ID
			}
		} else if instance.ImageConfig.ImageSource == models.ImageSourceRegistry {
			instance.ImageID = EmptyImageID
		}
	}
	// 如果权限是自定义，需要单独存入cas
	if instance.PermissionCfg != nil && instance.PermissionCfg.PermissionMode == cas.PermissionMode_Customize {
		reqs := make([]*cas.PutObjectReq, 0)
		for _, p := range instance.PermissionCfg.CustomPolicies {
			reqs = append(reqs, p.ToPolicyPutReq(projectID))
		}
		_, err = m.rbac.PutObject(context.Background(), instance.ID, cas.ObjType_CodeserverInstance, reqs)
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: CreateInstance method, put rbac objects error.")
		}
		// 不保存polices
		instance.PermissionCfg.CustomPolicies = []*cas.PolicyCfg{}
	}
	err = repo.Select(repo.ALL).Create(instance)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: CreateInstance method, create instance %+v error.", instance)
	}
	// 如果duration是0，则强制update
	if realDuration == 0 {
		_, err = repo.Where(repo.ID.Eq(instance.ID)).Select(repo.Duration).UpdateSimple(repo.Duration.Value(0))
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: CreateInstance method, update instance %+v duration zero error.", instance)
		}
	}
	return nil
}

func (m *instanceManager) DeleteInstance(projectID string, ids ...string) error {
	for _, id := range ids {
		if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
			return err
		}
	}
	repo := m.q.Instance
	_, err := repo.Where(repo.ID.In(ids...)).Delete()
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: DeleteInstance method, delete instances %+v error.", ids)
	}
	// 删除权限
	for _, id := range ids {
		err = m.rbac.DelObject(context.Background(), id, cas.ObjType_CodeserverInstance)
		if err != nil {
			stdlog.WithError(err).Errorf("del instance [%s] permission error.", id)
		}
	}
	go func() {
		for _, id := range ids {
			err := codehub.GetKubeCodehub().StopInstance(codehub.InstanceName(id))
			if err != nil {
				stdlog.WithError(err).Errorf("Delete routine, stop instance [%s] error.", id)
			}
		}
	}()
	return nil
}

func (m *instanceManager) CommitInstanceImage(projectID string, id string) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, get instance [%s] error.", id)
	}
	// 判断是否有此id的image
	imageRepo := m.q.Image
	instanceRepo := m.q.Instance
	_, err = imageRepo.Where(imageRepo.ID.Eq(id)).First()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 此任务还没固化过镜像，此任务使用的镜像还是基础镜像或者来自镜像仓库
		err = m.CopyInstanceImage(instance, instance.Image)
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, create instance [%s] image error.", id)
		}
		_, err = instanceRepo.Where(instanceRepo.ID.Eq(id)).UpdateSimple(instanceRepo.ImageID.Value(id))
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, update instance image id [%s] error.", id)
		}
	} else if err != nil {
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, query image error.")
	}
	resp, err := CommitImage(instance.ID, "", models.ImageTag(instance.ID))
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, commit instance [%s] image error.", id)
	}
	stdlog.Info("instance [%s] commit image succeed. new image id is [%s], repo is [%s]", id, resp.NewID, resp.Repo)
	return nil
}

func (m *instanceManager) CommitInstanceImageAsync(projectID string, id string) error {
	go func() {
		err := m.CommitInstanceImage(projectID, id)
		if err != nil {
			stdlog.WithError(err).Errorf("Async Commit instance [%s] image error.", id)
		}
	}()
	return nil
}

func (m *instanceManager) CopyInstanceImage(instance *models.Instance, image *models.Image) error {
	// 将image复制一个写入表中，image id和task id相同
	image.ID = instance.ID
	image.Name = image.Name
	image.Desc = fmt.Sprintf("%s/%s", instance.Name, instance.Desc)
	image.Repo = models.ImageTag(instance.ID)
	image.CreateAt = time.Now()
	image.UpdateAt = time.Now()
	image.ImageSource = models.ImageSourceTask
	err := GetImageManager().CreateImage(image.ProjectID, image)
	if err != nil {
		return err
	}
	return nil
}

func (m *instanceManager) getInstanceImage(id string) string {
	return fmt.Sprintf("%s/%s", imagehub.GetRegistry().Addr, models.ImageTag(id))
}

func (m *instanceManager) ExportInstanceData(projectID string, id string, zos *zip.Writer) error {
	//TODO implement me
	panic("implement me")
}

func (m *instanceManager) BuildImage(projectID string, id string, image *models.Image) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	// 保存此次dockerfile
	repo := m.q.Instance
	_, err := repo.Where(repo.ID.Eq(id)).UpdateSimple(repo.Dockerfile.Value(image.Dockerfile))
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: BuildImage method, update instance [%s] dockerfile error.", id)
	}
	// 固化镜像
	err = m.CommitInstanceImage(projectID, id)
	if err != nil {
		stdlog.WithError(err).Errorf("Commit instance [%s] image before build error. Instance may not running, or some error may occur when building.", id)
	}
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, get instance [%s] error.", id)
	}
	image.ImageSource = models.ImageSourceService
	u, err := uuid.NewUUID()
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: BuildImage method, new uuid error.")
	}
	tran := m.q.Begin()
	image.ID = u.String()
	err = tran.Image.Create(image)
	if err != nil {
		tran.Rollback()
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, Create image error.")
	}
	_, err = tran.Instance.Where(tran.Instance.ID.Eq(id)).UpdateSimple(tran.Instance.Building.Value(models.BitBool(true)))
	if err != nil {
		tran.Rollback()
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, update instance [%s] building true error.", id)
	}
	tag := models.ImageBuildTag(conf.Config.RepoPrefix, projectID, image.Repo)
	imageLabels, err := m.marshalLabels(image)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, marshal labels error %+v", image.Labels)
	}
	// 请求committer构建镜像
	req := &models.BuildReq{
		Tags:        []string{tag},
		Content:     image.Dockerfile,
		Labels:      imageLabels,
		NoCache:     true,
		CopyOptions: m.buildCopyOption(instance),
		PushOption: &models.PushOption{
			Enable:        true,
			Registry:      imagehub.GetRegistry().Addr,
			RegistryToken: conf.Config.RegistryTokens,
		},
		PullRequest: &models.PullReq{
			Repos: []string{models.ImageRepo(imagehub.GetRegistry().Addr, instance.Image.Repo)},
		},
	}
	resp, err := clients.CommitterCli.Build(id, req)
	if err != nil {
		tran.Rollback()
		return stderr.Wrap(err, "InstanceManager: CommitInstanceImage method, Create image error.")
	}
	image.Size = resp.Info.Size
	image.Repo = models.ImageRepo(imagehub.GetRegistry().Addr, tag)
	image.DockerID = strings.TrimPrefix(resp.Info.ID, "sha256:")
	image.BuildID = resp.BuildID
	image.ProjectID = projectID
	image.InstanceID = id
	_, err = tran.Image.Where(tran.Image.ID.Eq(image.ID)).Updates(image)
	if err != nil {
		tran.Rollback()
		return stderr.Wrap(err, "InstanceManager: BuildImage method, Update image [%s] error.", image.ID)
	}
	_, err = tran.Instance.Where(tran.Instance.ID.Eq(id)).UpdateSimple(tran.Instance.Building.Value(models.BitBool(false)))
	if err != nil {
		tran.Rollback()
		return stderr.Wrap(err, "InstanceManager: BuildImage method, update instance [%s] building false error.", id)
	}
	tran.Commit()
	return nil
}

func (m *instanceManager) BuildImageAsync(projectID string, id string, image *models.Image) (*models.BuildRecord, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return nil, err
	}
	// 保存此次dockerfile
	repo := m.q.Instance
	_, err := repo.Where(repo.ID.Eq(id)).UpdateSimple(repo.Dockerfile.Value(image.Dockerfile))
	if err != nil {
		return nil, stderr.Wrap(err, "InstanceManager: BuildImage method, update instance [%s] dockerfile error.", id)
	}
	// 先创建记录
	record := &models.BuildRecord{
		ID:         uuid.NewString(),
		Name:       image.Repo,
		InstanceID: id,
		BuilderIp:  "",
		Creator:    image.Creator,
		Status:     models.BuildStatusBuilding,
		ProjectID:  projectID,
	}
	err = GetBuildRecordManager().AddRecord(projectID, record)
	if err != nil {
		return nil, stderr.Wrap(err, "InstanceManager: BuildImageAsync method, Create build record error. build id: [%s]", image.ID)
	}
	// 先创建日志文件，防止commit到发送请求时点不开日志详情
	lp := BuildLogPath(record.ID)
	dir := filepath.Dir(lp)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		stdlog.WithError(err).Errorf("fail create log folder %s", dir)
		return nil, stderr.Wrap(err, "InstanceManager: BuildImageAsync method, Create build [%s] log file error.", record.ID)
	}
	// create log file
	file, err := os.OpenFile(lp, os.O_CREATE|os.O_RDWR|os.O_APPEND, os.ModePerm)
	if err != nil {
		return nil, err
	}
	_, err = file.WriteString(fmt.Sprintf("begin to commit image [%s]...\n", image.Repo))
	if err != nil {
		stdlog.WithError(err).Errorf("fail write log to file [%s]", lp)
	}
	file.Close()
	// 异步固化镜像
	go func() {
		err = m.CommitInstanceImage(projectID, id)
		if err != nil {
			stdlog.WithError(err).Errorf("Commit instance [%s] image before build error. Instance may not running, or some error may occur when building.", id)
			GetBuildRecordManager().AppendLogError(projectID, record.ID, err.Error())
			record.Status = models.BuildStatusFailed
			m.buildChan <- m.tempImage(id, record.ID, projectID, record)
		}
		instance, err := m.GetInstance(projectID, id)
		if err != nil {
			stdlog.WithError(err).Errorf("InstanceManager: CommitInstanceImage method, get instance [%s] error.", id)
			GetBuildRecordManager().AppendLogError(projectID, record.ID, err.Error())
			record.Status = models.BuildStatusFailed
			m.buildChan <- m.tempImage(id, record.ID, projectID, record)
		}
		image.ImageSource = models.ImageSourceService
		image.ID = uuid.NewString()
		tag := models.ImageBuildTag(conf.Config.RepoPrefix, projectID, image.Repo)
		imageLabels, err := m.marshalLabels(image)
		if err != nil {
			stdlog.WithError(err).Errorf("InstanceManager: CommitInstanceImage method, marshal labels error %+v", image.Labels)
			GetBuildRecordManager().AppendLogError(projectID, record.ID, err.Error())
			record.Status = models.BuildStatusFailed
			m.buildChan <- m.tempImage(id, record.ID, projectID, record)
		}
		// 请求committer构建镜像
		req := &models.BuildReq{
			Tags:        []string{tag},
			Content:     image.Dockerfile,
			Labels:      imageLabels,
			NoCache:     true,
			CopyOptions: m.buildCopyOption(instance),
			PushOption: &models.PushOption{
				Enable:        true,
				Registry:      imagehub.GetRegistry().Addr,
				RegistryToken: conf.Config.RegistryTokens,
			},
			PullRequest: &models.PullReq{
				Repos: []string{models.ImageRepo(imagehub.GetRegistry().Addr, instance.Image.Repo)},
			},
			BuildID: record.ID,
		}
		instanceName := codehub.InstanceName(instance.ID)
		if k8sInstance, ok := codehub.GetKubeCodehub().Instances.Get(instanceName); !ok {
			stdlog.Errorf("instance is not running!")
			GetBuildRecordManager().AppendLogError(projectID, record.ID, "instance is not running!")
			record.Status = models.BuildStatusFailed
			m.buildChan <- m.tempImage(id, record.ID, projectID, record)
		} else {
			podHostIp, err := k8sInstance.GetHostIp()
			if err != nil {
				stdlog.WithError(err).Errorf("instance [%s] get k8s podIp error.", instance.ID)
				GetBuildRecordManager().AppendLogError(projectID, record.ID, err.Error())
				record.Status = models.BuildStatusFailed
				m.buildChan <- m.tempImage(id, record.ID, projectID, record)
			}
			var builderIp string
			if builder, ok := GetBuilder(podHostIp); !ok {
				stdlog.Errorf("builder on host [%s] is not running! cannot build instance [%s]", podHostIp, instance.ID)
				GetBuildRecordManager().AppendLogError(projectID, record.ID, err.Error())
				record.Status = models.BuildStatusFailed
				m.buildChan <- m.tempImage(id, record.ID, projectID, record)
			} else {
				builderIp = builder.Status.PodIP
			}
			resp, err := clients.CommitterCli.BuildAsync(builderIp, id, req)
			if err != nil {
				stdlog.WithError(err).Errorf("InstanceManager: BuildImageAsync method, call committer build async error.")
				GetBuildRecordManager().AppendLogError(projectID, record.ID, err.Error())
				record.Status = models.BuildStatusFailed
				m.buildChan <- m.tempImage(id, record.ID, projectID, record)
			}
			image.Repo = models.ImageRepo(imagehub.GetRegistry().Addr, tag)
			image.BuildID = resp.BuildID
			image.ProjectID = projectID
			image.InstanceID = id
			record.BuilderIp = builderIp
			image.BuildRecord = record
			go func() {
				// 异步读取日志，直到日志结束
				ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
				defer cancel()
				result, err := CheckLogLastLine(ctx, resp.BuildID)
				if err != nil {
					stdlog.WithError(err).Errorf("Check build [%s] log error.", resp.BuildID)
				}
				if result == SUCCESS {
					// 成功
					image.BuildRecord.Status = models.BuildStatusSuccess
					// 保存权限
					fullRepo := tag[len(conf.Config.RepoPrefix)+1:]
					infoRepo := strings.Split(fullRepo, ":")
					if len(infoRepo) >= 2 {
						r := infoRepo[0]
						t := infoRepo[1]
						err = m.saveImagePermissionCfg(projectID, r, t, image.PermissionCfg)
						if err != nil {
							stdlog.WithError(err).Errorf("SaveImagePermissionCfg method, save permission error.")
						}
					}
					m.buildChan <- image
				} else if result == FAILED {
					// 失败
					image.BuildRecord.Status = models.BuildStatusFailed
					m.buildChan <- image
				}
			}()
			repo := m.q.Instance
			_, err = repo.Where(repo.ID.Eq(id)).UpdateSimple(repo.Building.Value(models.BitBool(true)))
			if err != nil {
				stdlog.WithError(err).Errorf("InstanceManager: BuildImageAsync method, update instance [%s] building true error.", id)
			}
		}
	}()
	return record, nil
}

func (m *instanceManager) saveImagePermissionCfg(projectID, repo, tag string, cfg *cas.PermissionCfg) error {
	// 将权限信息记录在表中
	if cfg != nil {
		// 更新cas
		if cfg.PermissionMode == cas.PermissionMode_Customize {
			// 如果权限是自定义，需要单独存入cas
			reqs := make([]*cas.PutObjectReq, 0)
			for _, p := range cfg.CustomPolicies {
				reqs = append(reqs, p.ToPolicyPutReq(projectID))
			}
			id := base64.StdEncoding.EncodeToString([]byte(helper.MergeRepoTag(repo, tag)))
			_, err := m.rbac.PutObject(context.Background(), id, cas.ObjType_ImageManager, reqs)
			if err != nil {
				return stderr.Wrap(err, "image put rbac objects error.")
			}
		}
		r := m.q.ImagePermission
		res, err := r.Where(r.Repo.Eq(repo), r.Tag.Eq(tag)).First()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在的权限
			imagePermission := &models.ImagePermission{
				Repo:          repo,
				Tag:           tag,
				ProjectID:     projectID,
				PermissionCfg: cfg,
			}
			imagePermission.PermissionCfg.CustomPolicies = []*cas.PolicyCfg{}
			err = r.Save(imagePermission)
			if err != nil {
				return stderr.Wrap(err, "image save permission error.")
			}

		} else if err != nil {
			return stderr.Wrap(err, "image save permission error.")
		} else {
			res.PermissionCfg = cfg
			res.PermissionCfg.CustomPolicies = []*cas.PolicyCfg{}
			err = r.Save(res)
			if err != nil {
				return stderr.Wrap(err, "image put rbac objects error.")
			}
		}
	}
	return nil
}

func (m *instanceManager) tempImage(instanceID, buildID, projectID string, record *models.BuildRecord) *models.Image {
	return &models.Image{
		InstanceID:  instanceID,
		ProjectID:   projectID,
		BuildID:     buildID,
		BuildRecord: record,
	}
}

func (m *instanceManager) marshalLabels(image *models.Image) (map[string]string, error) {
	ret := make(map[string]string)
	if image.Labels == nil {
		return ret, nil
	}
	b, err := json.Marshal(image.Labels)
	if err != nil {
		return nil, err
	}
	ret[models.ImageLabelName] = string(b)
	return ret, nil
}

func (m *instanceManager) StartInstance(projectID string, id string, username string) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	logger, err := NewInstanceLogger(id)
	if err != nil {
		return stderr.Wrap(err, "InstanceManager: StartInstance method, get logger [%s] error.", id)
	}
	logger.Infof("Get instance [%s] meta info...", id)
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		logger.WithError(err).Errorf("Get instance [%s] meta info error.", id)
		return stderr.Wrap(err, "InstanceManager: StartInstance method, get instance [%s] error.", id)
	}
	// 判断是否有启动的pod，且状态正常则跳过启动
	if ins, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(id)); !ok {

		logger.Infof("Build instance [%s] config...", id)
		instance.Scheduler = username
		config, err := m.buildKubeInstanceConfig(instance)
		if err != nil {
			logger.WithError(err).Errorf("Build instance [%s] config error.", id)
			return stderr.Wrap(err, "InstanceManager: StartInstance method, build instance [%s] config error.", id)
		}
		logger.Infof("Begin to start instance [%s]...", id)
		ret, err := codehub.GetKubeCodehub().StartInstance(config)
		if err != nil {
			logger.WithError(err).Errorf("Start instance [%s] error.", id)
			return stderr.Wrap(err, "InstanceManager: StartInstance method, start instance [%s] error.", id)
		}
		// 修改状态为Pending
		instance.Status = models.StatusPending
		logger.Infof("Instance [%s] is pending...", id)
		err = m.UpdateInstance(projectID, instance)
		if err != nil {
			logger.WithError(err).Errorf("Update instance [%s] status error.", id)
			stdlog.WithError(err).Errorf("InstanceManager: StartInstance method, update instance [%s] status to Pending error.", instance.ID)
		}
		// 添加路由
		logger.Infof("Add instance [%s] proxy route...", id)
		err = proxy.GetCsmKubernetesProxy().AddRoute(ret)
		if err != nil {
			logger.WithError(err).Errorf("Add instance [%s] proxy route error.", id)
			return stderr.Wrap(err, "InstanceManager: StartInstance method, add instance [%s] to proxy route error.", id)
		}
		// 等待启动完成
		watcher := ret.Watcher()
		logger.Infof("Waiting for instance [%s] event...", id)
		for event := range watcher.Chan() {
			var content codehub.PodEventContent
			err := json.Unmarshal([]byte(event.Content), &content)
			if err != nil {
				stdlog.WithError(err).Errorf("InstanceManager: StartInstance method, unmarshal content json error. content: [%s]", event.Content)
				continue
			}
			stdlog.Infof("instance: [%s], type: %s, event: %s", id, event.Type, event.Content)
			logger.Infof("[EVENT] - reason: [%s], mesasge: [%s]", content.Reason, content.Message)
			// websocket 推送
			ws.GetWsServer().BroadcastToAll(ws.InstancePodEvent, id, event.Content)
			if content.Reason == codehub.ReasonStarted {
				stdlog.Infof("instance [%s] succeed to start pod with codeserver, then begin to start jupyter.", id)
				logger.Infof("Start pod with code-server succcessfully!")
				break
			} else if content.Reason == codehub.ReasonFailed {
				// 失败了
				// 直接删除该pod
				logger.Infof("Start pod with code-server failed!")
				err := codehub.GetKubeCodehub().StopInstance(codehub.InstanceName(id))
				if err != nil {
					stdlog.WithError(err).Errorf("InstanceManager: StartInstance method, instance [%s] stop error.", instance.ID)
					logger.WithError(err).Errorf("Stop instance [%s] error.", id)
				}
				// 修改状态为Failed
				instance.Status = models.StatusFailed
				logger.Infof("Change instance [%s] status to failed.", id)
				err = m.UpdateInstance(projectID, instance)
				if err != nil {
					stdlog.WithError(err).Errorf("InstanceManager: StartInstance method, update instance [%s] status to Failed error.", instance.ID)
					logger.WithError(err).Errorf("Change instance [%s] status error.", id)
				}
				logger.Infof("Delete instance [%s] proxy route.", id)
				err = proxy.GetCsmKubernetesProxy().DeleteRoute(codehub.InstanceName(instance.ID))
				if err != nil {
					stdlog.WithError(err).Errorf("delete instance [%s] route error.", instance.Name)
					logger.WithError(err).Errorf("Delete instance [%s] proxy route error.", id)
				}
				return stderr.Internal.Error("instance [%s] start failed with message: %s", id, content.Message)
			}
		}
		// 启动jupyter
		logger.Infof("Begin to start jupyter...")
		if kubeInstance, ok := ret.(*codehub.KubernetesInstance); ok {
			if instance.DataType == models.DataTypeUserDefined {
				err := kubeInstance.PreparePyEnv(instance)
				if err != nil {
					logger.WithError(err).Errorf("Prepare python env error.")
					return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] prepare python env error.", id)
				}
			}
			err = kubeInstance.StartJupyter(id)
			if err != nil {
				logger.WithError(err).Errorf("Start jupyter error.")
				return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] start jupyter proc error.", id)
			}
			// 启动hook
			m.execHookCmd(kubeInstance, hooks)
			// 安装extension
			// 姑且改为同步
			m.installExtensions(id, kubeInstance)
		}
	} else {
		// 判断是否已启动jupyter
		exist, err := ins.CheckJupyterProc()
		if err != nil {
			logger.WithError(err).Errorf("Check jupyter proc error.")
			return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] check jupyter proc error.", id)
		}
		if !exist {
			if instance.DataType == models.DataTypeUserDefined {
				err := ins.PreparePyEnv(instance)
				if err != nil {
					logger.WithError(err).Errorf("Prepare python env error.")
					return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] prepare python env error.", id)
				}
			}
			// 启动jupyter
			err = ins.StartJupyter(id)
			if err != nil {
				logger.WithError(err).Errorf("Start jupyter error.")
				return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] start jupyter proc error.", id)
			}
		} else {
			stdlog.Infof("instance [%s] skip starting jupyter.", id)
		}
	}
	stdlog.Infof("instance [%s] succeed to start jupyter.", id)
	logger.Infof("Start jupyter successfully!")
	// 如果是分布式任务，需要启动slave pod
	if instance.PodResource != nil && instance.PodResource.Distributed != nil && instance.PodResource.Distributed.Enabled && instance.PodResource.Distributed.NumWorkers > 1 {
		stdlog.Infof("instance [%s] begin to start workers.", id)
		logger.Infof("Begin to start workers...")
		err := m.startSlavePods(instance, logger)
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] start workers error.", id)
		}
		stdlog.Infof("Succeed to start instance [%s] workers.", id)
		logger.Infof("All workers started successfully.")
	}

	// 推送最终的消息
	ws.GetWsServer().BroadcastToAll(ws.InstancePodSuccess, id)
	// 直接生成固化镜像
	logger.Infof("Begin to commit image...")
	go func() {
		err := m.CommitInstanceImage(projectID, id)
		if err != nil {
			stdlog.WithError(err).Errorf("Commit instance [%s] image error.", id)
			logger.WithError(err).Errorf("Commit image error.")
		}
		stdlog.Infof("Succeed to commit instance [%s] image.", id)
		logger.Infof("Commit image successfully.")
	}()
	return nil
}

func (m *instanceManager) execHookCmd(ins *codehub.KubernetesInstance, cmds []string) {
	for _, cmd := range cmds {
		SubmitHook(ins, cmd)
	}
}

func (m *instanceManager) installExtensions(id string, ins *codehub.KubernetesInstance) {
	for _, f := range codehub.ExtensionCreateList {
		ext := f(ins)
		ws.GetWsServer().BroadcastToAll(ws.InstancePodEvent, id, fmt.Sprintf("begin to install extension [%s].", ext.Name()))
		err := ext.Install()
		if err != nil {
			stdlog.WithError(err).Errorf("instance [%s] install extension [%s] error.", ext.Name())
		}
	}
}

func (m *instanceManager) startSlavePods(instance *models.Instance, logger *InstanceLogger) error {
	if ins, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(instance.ID)); !ok {
		return stderr.Internal.Error("Instance [%s] is not running, cannot start slave pod.", instance.ID)
	} else {
		pod, err := ins.GetPod()
		if err != nil {
			return err
		}
		configs := make([]*codehub.KubeInstanceConfig, 0)
		for i := 0; i < int(instance.PodResource.Distributed.NumWorkers)-1; i++ {
			logger.Infof("Begin to build worker %d config...", i)
			config, err := m.buildSlavePodConfig(instance, i, pod.GetUID())
			if err != nil {
				stdlog.WithError(err).Errorf("Build instance [%s] worker %d config error.", instance.ID, i)
				logger.WithError(err).Errorf("Build worker %d config error.", i)
				return err
			}
			configs = append(configs, config)
		}
		logger.Infof("Begin to start workers...")
		wg := sync.WaitGroup{}
		wg.Add(len(configs))
		errs := make([]string, 0)
		for _, config := range configs {
			go func(config *codehub.KubeInstanceConfig) {
				defer wg.Done()
				err := codehub.GetKubeCodehub().StartSlavePodAndWaiting(config)
				if err != nil {
					stdlog.WithError(err).Errorf("Start worker [%s] error.", config.Name)
					logger.WithError(err).Errorf("Worker [%s] start error.", config.Name)
					errs = append(errs, err.Error())
				}
				logger.Infof("Worker [%s] start successfully.", config.Name)
			}(config)
		}
		wg.Wait()
		if len(errs) > 0 {
			return stderr.Internal.Error("workers start failed with error: %+v", errs)
		}
	}
	return nil
}

func (m *instanceManager) StartInstanceAsync(projectID string, id string, username string) error {
	// 异步的方式启动任务
	go func() {
		err := m.StartInstance(projectID, id, username)
		if err != nil {
			stdlog.WithError(err).Errorf("Aynsc start instance [%s] error.", id)
			// 发送失败的事件
			ws.GetWsServer().BroadcastToAll(ws.InstancePodFailed, id, err.Error())
		}
	}()
	return nil
}

func (m *instanceManager) StopInstance(projectID string, id string) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	if instance, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(id)); !ok {
		// 也可能是pending的pod
		modelIns, err := m.GetInstance(projectID, id)
		if err != nil {
			return stderr.Wrap(err, "Get instance [%s] error.", id)
		}
		if modelIns.Status == models.StatusPending {
			err := codehub.GetKubeCodehub().StopInstance(codehub.InstanceName(id))
			if err != nil {
				return stderr.Wrap(err, "InstanceManager: StartInstance method, instance [%s] stop error.", id)
			}
			modelIns.Status = models.StatusStopped
			err = m.UpdateInstance(projectID, modelIns)
			if err != nil {
				return stderr.Wrap(err, "InstanceManager: StartInstance method, update instance [%s] status to Failed error.", id)
			}
			err = proxy.GetCsmKubernetesProxy().DeleteRoute(codehub.InstanceName(instance.Name))
			if err != nil {
				return stderr.Wrap(err, "delete instance [%s] route error.", instance.Name)
			}
		} else {
			return stderr.Internal.Error("Instance is not running, do not need to stop.")
		}
	} else {
		ins, err := m.GetInstance(projectID, id)
		if err != nil {
			return stderr.Wrap(err, "Get instance [%s] model error.", id)
		}
		// 后面步骤改为异步，防止commit超时
		repo := m.q.Instance
		_, err = repo.Where(repo.ID.Eq(id)).UpdateSimple(repo.Status.Value(models.StatusStopping))
		if err != nil {
			return stderr.Wrap(err, "InstanceManager: StartInstance method, update instance [%s] status to Stopping error.", id)
		}
		go func() {
			if ins.Status == models.StatusRunning {
				// 先固化此镜像
				err = GetInstanceManager().CommitInstanceImage("all", id)
				if err != nil {
					// 删除map中的instance
					stdlog.WithError(err).Errorf("commit instance [%s] image error.", id)
				}
			}
			err = codehub.GetKubeCodehub().StopInstance(instance.Name)
			if err != nil {
				stdlog.WithError(err).Errorf("stop instance [%s] error.", id)
			}
			if ins.Status == models.StatusRunning {
				if len(ins.DistConfigs) > 0 {
					err := codehub.GetKubeCodehub().StopSlaveService(instance.Name, instance.Namespace, len(ins.DistConfigs))
					if err != nil {
						stdlog.WithError(err).Warnf("Delete slave service error.")
					}
				}
				infos, err := m.ListInstanceServices(projectID, id)
				if err != nil {
					stdlog.WithError(err).Warnf("List instance [%s] node port service error.", id)
				}
				if len(infos) > 0 {
					for _, info := range infos {
						err := m.DeleteInstanceService(projectID, id, info.Name)
						if err != nil {
							stdlog.WithError(err).Warnf("Delete node port service [%s] error.", info.Name)
						}
					}
				}
			}
			err = proxy.GetCsmKubernetesProxy().DeleteRoute(instance.Name)
			if err != nil {
				stdlog.WithError(err).Errorf("delete instance [%s] route error.", id)
			}
			_, err = repo.Where(repo.ID.Eq(id)).UpdateSimple(repo.Status.Value(models.StatusStopped))
			if err != nil {
				stdlog.WithError(err).Errorf("Update instance [%s] status error.", id)
			}
		}()
	}
	return nil
}

func (m *instanceManager) buildKubeInstanceConfig(instance *models.Instance) (*codehub.KubeInstanceConfig, error) {
	var (
		podResource *serving.PodResourceSpec
		err         error
	)
	if instance.PodResource != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
		defer cancel()
		ctx = client.SetToken(ctx, conf.Config.Token)
		podResource, err = instance.PodResource.ToPodResourceDO(ctx)
		if err != nil {
			return nil, err
		}
	}
	extraConfig, err := m.buildKubeExtraConfig(instance)
	if err != nil {
		return nil, err
	}
	if instance.TenantId == "" {
		instance.TenantId = conf.Config.Namespace
	}
	config := &codehub.KubeInstanceConfig{
		Name:        codehub.InstanceName(instance.ID),
		Namespace:   instance.TenantId,
		PodResource: podResource,
		Image: func() string {
			if instance.Image == nil {
				// 一般不太会出现这种情
				pyImage, err := GetPythonImage()
				if err != nil {
					return ""
				}
				return pyImage.Repo
			}
			if instance.Image.ImageSource == models.ImageSourceBase {
				return instance.Image.Repo
			} else if instance.Image.ImageSource == models.ImageSourceTask {
				return m.getInstanceImage(instance.ID)
			} else {
				// imageSource为empty
				return instance.ImageConfig.Image.Repo
			}
		}(),
		ExtraConfig: extraConfig,
	}
	config.WithService = true
	return config, nil
}

func (m *instanceManager) buildKubeExtraConfig(instance *models.Instance) (*codehub.KubeInstanceExtraConfig, error) {
	volumeName := "default-pvc"
	// 主要是挂载的配置
	volume := &codehub.VolumeConfig{
		VolumeName: volumeName,
		PvcName:    conf.Config.Pvc.Name,
	}
	mounts := make([]*codehub.MountConfig, 0)
	filePathPrefix := WorkSpace
	if instance.DataType != models.DataTypeUserDefined {
		filePathPrefix = filepath.Join(filePathPrefix, DataDir)
	}
	projectSitePkgsPath := ""
	for _, dataSource := range instance.DataSource {
		readOnly := true
		mountPath := m.trimPvcPrefix(instance, dataSource.Dir, true)
		if instance.DataType == models.DataTypeUserDefined {
			if dataSource.Info[helper.ParamsType] == helper.ParamPyEnv {
				projectSitePkgsPath = dataSource.Dir
				mountPath = dataSource.Prefix
				mounts = append(mounts, &codehub.MountConfig{
					VolumeName: volumeName,
					MountPath:  mountPath,
					SubPath:    m.trimPvcPrefix(instance, dataSource.Prefix, false),
					ReadOnly:   false,
				})
				continue
			}

			mountPath = strings.TrimPrefix(dataSource.Dir, dataSource.Prefix)
			if dataSource.Info[helper.ParamsType] == helper.ParamModel {
				mountPath = filepath.Join(helper.ParamModel, mountPath)
			} else if dataSource.Info[helper.ParamsType] != helper.ParamUdOp {
				continue
			}
			mountPath = filepath.Join(helper.ParamUdOp, mountPath)
			readOnly = false
		}
		mounts = append(mounts, &codehub.MountConfig{
			VolumeName: volumeName,
			MountPath:  filepath.Join(filePathPrefix, mountPath),
			SubPath:    m.trimPvcPrefix(instance, dataSource.Dir, false),
			ReadOnly:   readOnly,
		})
	}
	// ========> 语料清洗特殊处理 <========
	if instance.DataType == models.DataTypeDataCleaning {
		mounts = append(mounts, &codehub.MountConfig{
			VolumeName: volumeName,
			//ReadOnly:   true,
			MountPath: filepath.Join(WorkSpace, DataDir, "resource"),
			SubPath:   filepath.Join(helper.ProjsPath, instance.ProjectID, "corpus"),
		})
	}
	// 挂载模板代码
	templateMount, err := m.prepareTemplateCode(instance, volumeName)
	if err != nil {
		return nil, stderr.Wrap(err, "instance [%s] build k8s extra config error.", instance.ID)
	}
	mounts = append(mounts, templateMount...)
	// 挂载share目录
	mounts = append(mounts, &codehub.MountConfig{
		VolumeName: volumeName,
		MountPath:  filepath.Join(WorkSpace, Share),
		SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, "csm", Share),
		ReadOnly:   false,
	})
	extra := &codehub.KubeInstanceExtraConfig{
		Volumes:      []*codehub.VolumeConfig{volume},
		VolumeMounts: mounts,
	}
	// 默认添加INSTANCE_NAME, IMAGE, WORKSPACE_PVC环境变量
	extra.Envs = map[string]string{
		"WORKSPACE_PVC": conf.Config.Pvc.Name,
		"INSTANCE_NAME": codehub.InstanceName(instance.ID),
		"IMAGE":         m.getInstanceImage(instance.ID),
		"NAMESPACE":     codehub.GetKubeCodehub().Namespace,
		"PROJECT_ID":    instance.ProjectID,
		"NAME":          instance.Name,
		"CREATOR":       instance.Creator,
		"SCHEDULER":     instance.Scheduler,
	}

	if instance.PodResource != nil {
		extra.Envs["RESOURCE_ID"] = fmt.Sprintf("%d", instance.PodResource.ResourceRuleID)
	}

	for k, v := range instance.Env {
		extra.Envs[k] = v
	}

	if projectSitePkgsPath != "" {
		extra.Envs["Project_Py_Env"] = projectSitePkgsPath
	}

	// ========> 语料清洗特殊处理 <========
	if instance.DataType == models.DataTypeDataCleaning {
		extra.Envs["DATA_SUB_PATH"] = filepath.Join(getProjectTenantPrefix(instance), "corpus")
		extra.Envs["PROJECT_ID"] = instance.ProjectID
		extra.Envs["DATA_JUICER_IMAGE"] = conf.Config.DataJuicerImage
	}
	if len(instance.DistConfigs) > 0 {
		names := make([]string, 0)
		for i, _ := range instance.DistConfigs {
			names = append(names, codehub.InstanceSlaveName(instance.ID, i))
		}
		// 添加worker的环境变量
		extra.Envs["WORKERS"] = strings.Join(names, ",")
	}
	// 判断是否需要指定node和env
	//if instance.HostConfig != nil {
	//	if instance.HostConfig.Name != "" {
	//		extra.NodeName = instance.HostConfig.Name
	//		if len(instance.HostConfig.GpuIds) > 0 {
	//			gpuInfos, err := codehub.QueryLatestGpuInfos()
	//			if err != nil {
	//				return nil, err
	//			}
	//			index := make([]string, 0)
	//			for _, gpuId := range instance.HostConfig.GpuIds {
	//				if gpu, ok := gpuInfos[gpuId]; ok {
	//					index = append(index, gpu.GpuIndex)
	//				}
	//			}
	//			if len(index) > 0 {
	//				extra.Envs["NVIDIA_VISIBLE_DEVICES"] = strings.Join(index, ",")
	//			}
	//		}
	//	}
	//}
	if instance.Admin {
		extra.ServiceAccount = conf.Config.ServiceAccount
	}
	if conf.ExtraConfig.NodeAffinity != nil {
		extra.NodeAffinity = conf.ExtraConfig.NodeAffinity
	}
	extra.ImagePullPolicy = "Always"
	// 添加metric标签
	extra.AppLabels, _, _ = stdmetric.WithMonitorKeys(make(map[string]string), nil, stdmetric.ResourceTypeCode, instance.ID, codehub.InstanceName(instance.ID), instance.ProjectID)
	return extra, nil
}

func (m *instanceManager) trimPvcPrefix(instance *models.Instance, path string, trimProj bool) string {
	// 因为各租户下projs前面的路径不可见，去除proj前面的路径
	path = strings.TrimPrefix(path, helper.Slash)
	path = strings.TrimPrefix(path, helper.SophonPath)
	path = strings.TrimPrefix(path, helper.Slash)
	path = strings.TrimPrefix(path, helper.TenantsPath)
	path = strings.TrimPrefix(path, helper.Slash)
	path = strings.TrimPrefix(path, instance.TenantId)
	if trimProj {
		path = strings.TrimPrefix(path, helper.Slash)
		path = strings.TrimPrefix(path, helper.ProjsPath)
		path = strings.TrimPrefix(path, helper.Slash)
		path = strings.TrimPrefix(path, instance.ProjectID)
	}
	path = strings.TrimPrefix(path, helper.Slash)
	return path
}

func (m *instanceManager) buildSlavePodConfig(instance *models.Instance, index int, uid types.UID) (*codehub.KubeInstanceConfig, error) {
	var (
		podResource *serving.PodResourceSpec
		err         error
	)
	if instance.PodResource != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
		defer cancel()
		ctx = client.SetToken(ctx, conf.Config.Token)
		podResource, err = instance.PodResource.ToPodResourceDO(ctx)
		if err != nil {
			return nil, err
		}
	}
	extraConfig, err := m.buildSlaveExtraConfig(instance)
	if err != nil {
		return nil, err
	}
	config := &codehub.KubeInstanceConfig{
		Name:        codehub.InstanceSlaveName(instance.ID, index),
		Namespace:   instance.TenantId,
		PodResource: podResource,
		Image:       instance.Image.Repo,
		ExtraConfig: extraConfig,
		WithService: true,
		OwnerConfig: &codehub.OwnerConfig{
			Name: codehub.InstanceName(instance.ID),
			Uid:  uid,
		},
	}
	return config, nil
}

func (m *instanceManager) buildSlaveExtraConfig(instance *models.Instance) (*codehub.KubeInstanceExtraConfig, error) {
	volumeName := "default-pvc"
	// 主要是挂载的配置
	volume := &codehub.VolumeConfig{
		VolumeName: volumeName,
		PvcName:    conf.Config.Pvc.Name,
	}
	mounts := make([]*codehub.MountConfig, 0)
	filePathPrefix := WorkSpace
	if instance.DataType != models.DataTypeUserDefined {
		filePathPrefix = filepath.Join(filePathPrefix, DataDir)
	}
	for _, dataSource := range instance.DataSource {
		readOnly := true
		mountPath := m.trimPvcPrefix(instance, dataSource.Dir, true)
		if instance.DataType == models.DataTypeUserDefined {
			mountPath = strings.TrimPrefix(dataSource.Dir, dataSource.Prefix)
			if dataSource.Info[helper.ParamsType] == helper.ParamModel {
				mountPath = filepath.Join(helper.ParamModel, mountPath)
			} else if dataSource.Info[helper.ParamsType] != helper.ParamUdOp {
				continue
			}
			mountPath = filepath.Join(helper.ParamUdOp, mountPath)
			readOnly = false
		}
		mounts = append(mounts, &codehub.MountConfig{
			VolumeName: volumeName,
			MountPath:  filepath.Join(filePathPrefix, mountPath),
			SubPath:    m.trimPvcPrefix(instance, dataSource.Dir, false),
			ReadOnly:   readOnly,
		})
	}
	// 挂载模板代码
	templateMount, err := m.prepareTemplateCode(instance, volumeName)
	if err != nil {
		return nil, stderr.Wrap(err, "instance [%s] build k8s extra config error.", instance.ID)
	}
	mounts = append(mounts, templateMount...)
	// 挂载share目录
	mounts = append(mounts, &codehub.MountConfig{
		VolumeName: volumeName,
		MountPath:  filepath.Join(WorkSpace, Share),
		SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, "csm", Share),
		ReadOnly:   false,
	})
	extra := &codehub.KubeInstanceExtraConfig{
		Volumes:      []*codehub.VolumeConfig{volume},
		VolumeMounts: mounts,
	}
	// 默认添加INSTANCE_NAME, IMAGE, WORKSPACE_PVC环境变量
	extra.Envs = map[string]string{
		"WORKSPACE_PVC": conf.Config.Pvc.Name,
		"INSTANCE_NAME": codehub.InstanceName(instance.ID),
		"IMAGE":         m.getInstanceImage(instance.ID),
		"NAMESPACE":     codehub.GetKubeCodehub().Namespace,
		"CREATOR":       instance.Creator,
		"SCHEDULER":     instance.Scheduler,
	}
	if instance.PodResource != nil {
		extra.Envs["RESOURCE_ID"] = fmt.Sprintf("%d", instance.PodResource.ResourceRuleID)
	}
	// 判断是否需要指定node和env
	//hostConfig := distConfig.HostConfig
	//if hostConfig != nil {
	//	if hostConfig.Name != "" {
	//		extra.NodeName = hostConfig.Name
	//		if len(hostConfig.GpuIds) > 0 {
	//			gpuInfos, err := codehub.QueryLatestGpuInfos()
	//			if err != nil {
	//				return nil, err
	//			}
	//			index := make([]string, 0)
	//			for _, gpuId := range hostConfig.GpuIds {
	//				if gpu, ok := gpuInfos[gpuId]; ok {
	//					index = append(index, gpu.GpuIndex)
	//				}
	//			}
	//			if len(index) > 0 {
	//				extra.Envs["NVIDIA_VISIBLE_DEVICES"] = strings.Join(index, ",")
	//			}
	//		}
	//	}
	//}
	extra.ImagePullPolicy = "Always"
	return extra, nil
}

// prepareTemplateCode 准备模板数据以及挂载
// 挂载逻辑：
// 1. 将etc/templates/ 下对应的文件复制到sfs的csm/instance/{instanceID}/workspace/ 目录下
// 2. 将该目录挂载到容器的/workspace目录下，为全部的用户代码
// 3. 将sfs的csm/instance/{instanceID}/output/ 目录挂载到容器的 /workspace/output/ 目录下，为输出代码，导出数据会打包此目录
// 4. 根据配置的python和apt源，替换默认配置文件
// workspace -> csm/instance/{instanceID}/workspace/
// ├── csv2txt.py
// ├── data
// │   └── data -> 挂载的数据
// ├── data_clean.py
// ├── jsonl_to_txt.py
// ├── output -> csm/instance/{instanceID}/output/
// ├── readme.md
// ├── symbol.txt
// └── Untitled.ipynb
func (m *instanceManager) prepareTemplateCode(instance *models.Instance, volumeName string) ([]*codehub.MountConfig, error) {
	// 判断是否已经有数据
	if _, err := os.Stat(filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(instance), getInstanceDir(instance.ID), WorkSpace)); err != nil {
		if os.IsNotExist(err) {
			// 复制模板到对应的sfs目录
			err := helper.Copy(filepath.Join("etc/templates", string(instance.DataType)), filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(instance), getInstanceDir(instance.ID), WorkSpace))
			if err != nil {
				// 删除该目录
				os.RemoveAll(filepath.Join(conf.Config.StorageRoot, getInstanceDir(instance.ID), WorkSpace))
				return nil, stderr.Wrap(err, "instance [%s] copy template error.", instance.ID)
			}
			// 复制vsix
			err = helper.Copy(filepath.Join("etc/extensions"), filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(instance), getInstanceDir(instance.ID), WorkSpace))
			if err != nil {
				stdlog.WithError(err).Errorf("instance [%s] copy vsix error.", instance.ID)
			}
		} else {
			return nil, stderr.Wrap(err, "instance [%s] open sfs dir error.", instance.ID)
		}
	}
	res := []*codehub.MountConfig{
		{ // 挂载workspace代码
			VolumeName: volumeName,
			MountPath:  filepath.Join(WorkSpace),
			SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, getInstanceDir(instance.ID), WorkSpace),
			ReadOnly:   false,
		},
		{ // 挂载output代码
			VolumeName: volumeName,
			MountPath:  filepath.Join(WorkSpace, Output),
			SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, getInstanceDir(instance.ID), Output),
			ReadOnly:   false,
		},
	}
	repoConfigs, err := m.prepareRepoConfig(instance, volumeName)
	if err != nil {
		stdlog.WithError(err).Errorf("instance [%s] make repo config error.", instance.ID)
		return res, nil
	}
	res = append(res, repoConfigs...)
	return res, nil
}

func (m *instanceManager) prepareRepoConfig(instance *models.Instance, volumeName string) ([]*codehub.MountConfig, error) {
	res := make([]*codehub.MountConfig, 0)
	repoConfigs, err := clients.ResourceCli.GetRepoConfig()
	if err != nil {
		return nil, err
	}
	configPath := filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(instance), getInstanceDir(instance.ID), Config)
	err = os.MkdirAll(configPath, 0777)
	if err != nil {
		return nil, err
	}
	// pypi
	pypiFilePath := filepath.Join(configPath, "pip.conf")
	if _, err := os.Stat(pypiFilePath); err != nil {
		if !os.IsNotExist(err) {
			return nil, err
		}
	} else {
		if err := os.Remove(pypiFilePath); err != nil {
			return nil, err
		}
	}
	f, err := os.Create(pypiFilePath)
	if err != nil {
		return nil, err
	}
	if err := m.writePypiConfig(f, repoConfigs.Python); err != nil {
		return nil, err
	}
	res = append(res, &codehub.MountConfig{
		VolumeName: volumeName,
		MountPath:  "/root/.config/pip/pip.conf",
		SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, getInstanceDir(instance.ID), Config, "pip.conf"),
		ReadOnly:   false,
	})
	// apt
	aptMountConfigs, err := m.writeAptConfig(instance, repoConfigs.Apt, volumeName)
	if err != nil {
		return nil, err
	}
	res = append(res, aptMountConfigs...)
	return res, nil
}

func (m *instanceManager) writeAptConfig(instance *models.Instance, repoConfig *models.RepoConfig, volumeName string) ([]*codehub.MountConfig, error) {
	res := make([]*codehub.MountConfig, 0)
	configPath := filepath.Join(conf.Config.StorageRoot, getProjectTenantPrefix(instance), getInstanceDir(instance.ID), Config)
	if repoConfig.DefaultConfigEnable {
		aptFilePath := filepath.Join(configPath, "sources.list")
		if _, err := os.Stat(aptFilePath); err != nil {
			if !os.IsNotExist(err) {
				return nil, err
			}
		} else {
			if err := os.Remove(aptFilePath); err != nil {
				return nil, err
			}
		}
		f, err := os.Create(aptFilePath)
		if err != nil {
			return nil, err
		}
		_, err = f.WriteString(repoConfig.DefaultRepoVal)
		if err != nil {
			return nil, err
		}
		res = append(res, &codehub.MountConfig{
			VolumeName: volumeName,
			MountPath:  "/etc/apt/sources.list",
			SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, getInstanceDir(instance.ID), Config, "sources.list"),
			ReadOnly:   false,
		})
	}
	if len(repoConfig.ExtraSource) > 0 {
		for index, extra := range repoConfig.ExtraSource {
			indexFileName := fmt.Sprintf("sources%d.list", index)
			aptFilePath := filepath.Join(configPath, indexFileName)
			if _, err := os.Stat(aptFilePath); err != nil {
				if !os.IsNotExist(err) {
					return nil, err
				}
			} else {
				if err := os.Remove(aptFilePath); err != nil {
					return nil, err
				}
			}
			f, err := os.Create(aptFilePath)
			if err != nil {
				return nil, err
			}
			_, err = f.WriteString(extra)
			if err != nil {
				return nil, err
			}
			res = append(res, &codehub.MountConfig{
				VolumeName: volumeName,
				MountPath:  fmt.Sprintf("/etc/apt/sources.list.d/%s", indexFileName),
				SubPath:    filepath.Join(helper.ProjsPath, instance.ProjectID, getInstanceDir(instance.ID), Config, indexFileName),
				ReadOnly:   false,
			})
		}
	}
	return res, nil
}

func (m *instanceManager) writePypiConfig(f *os.File, repoConfig *models.RepoConfig) error {
	var err error
	writeString(f, "[global]\n", err)
	if repoConfig.DefaultConfigEnable {

		writeString(f, fmt.Sprintf("index-url = %s/simple\n", m.addHostNamespace(repoConfig.DefaultRepoVal)), err)
		if len(repoConfig.ExtraSource) > 0 {
			writeString(f, fmt.Sprintf("extra-index-url = %s\n", strings.Join(repoConfig.ExtraSource, " ")), err)
		}
		writeString(f, "[install]\n", err)
		writeString(f, fmt.Sprintf("trusted-host = %s\n", strings.Join(m.getRepoConfigHosts(repoConfig), " ")), err)
	} else {
		if len(repoConfig.ExtraSource) == 0 {
			writeString(f, "index-url = https://mirrors.ustc.edu.cn/pypi/web/simple\n", err)
		} else if len(repoConfig.ExtraSource) == 1 {
			writeString(f, fmt.Sprintf("index-url = %s\n", repoConfig.ExtraSource[0]), err)
		} else {
			writeString(f, fmt.Sprintf("index-url = %s\n", repoConfig.ExtraSource[0]), err)
			nc := make([]string, 0)
			for i := 1; i < len(repoConfig.ExtraSource); i++ {
				nc = append(nc, repoConfig.ExtraSource[i])
			}
			writeString(f, fmt.Sprintf("extra-index-url = %s\n", strings.Join(nc, " ")), err)
		}
		writeString(f, "[install]\n", err)
		writeString(f, fmt.Sprintf("trusted-host = %s\n", strings.Join(m.getRepoConfigHosts(repoConfig), " ")), err)
	}
	if err != nil {
		return err
	}
	return nil
}

func (m *instanceManager) addHostNamespace(repo string) string {
	if strings.Contains(repo, fmt.Sprintf(".%s", conf.Config.Namespace)) {
		return repo
	}
	info := strings.Split(repo, ":")
	if len(info) == 3 {
		return fmt.Sprintf("%s:%s.%s:%s", info[0], info[1], conf.Config.Namespace, info[2])
	} else if len(info) == 2 {
		return fmt.Sprintf("%s.%s:%s", info[0], conf.Config.Namespace, info[1])
	} else {
		return repo
	}
}

func (m *instanceManager) getRepoConfigHosts(repoConfig *models.RepoConfig) []string {
	res := make([]string, 0)
	if repoConfig.DefaultConfigEnable {
		withoutSchema := strings.TrimPrefix(repoConfig.DefaultRepoVal, "http://")
		res = append(res, fmt.Sprintf("%s.%s", strings.Split(withoutSchema, ":")[0], conf.Config.Namespace))
	}
	for _, extra := range repoConfig.ExtraSource {
		parts := strings.Split(extra, "/")
		if len(parts) >= 3 {
			res = append(res, fmt.Sprintf("%s.%s", strings.Split(parts[2], ":")[0], conf.Config.Namespace))
		}
	}
	return res
}

func writeString(f *os.File, s string, err error) {
	if err != nil {
		return
	}
	_, err = f.WriteString(s)
}

// buildCopyOption 传递需要imagebuilder 复制的文件配置
// 需要将csm/instance/{instanceID}/workspace/ 下的文件复制到build的目录
// 租户化改造 tenants/{tid}/projs/{project_id}/csm/instance/{instanceID}/workspace/
func (m *instanceManager) buildCopyOption(instance *models.Instance) []*models.CopyOption {
	options := make([]*models.CopyOption, 0)
	options = append(options, &models.CopyOption{
		SfsDir:   filepath.Join(getProjectTenantPrefix(instance), getInstanceDir(instance.ID), WorkSpace),
		ImageDir: WorkSpace,
	})
	return options
}

func (m *instanceManager) GetInstanceOutputDir(projectID string, id string) (string, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return "", err
	}
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		return "", err
	}
	return filepath.Join(getProjectTenantPrefix(instance), getInstanceDir(instance.ID), Output), nil
}

func getInstanceDir(id string) string {
	return filepath.Join("csm", "instance", id)
}

func getProjectTenantPrefix(instance *models.Instance) string {
	return filepath.Join(helper.TenantsPath, instance.TenantId, helper.ProjsPath, instance.ProjectID)
}

func (m *instanceManager) GetInstanceLogs(projectID string, id string) (string, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return "", err
	}
	lines, err := utils.TailFileSync(-1, GetLogFilePath(id))
	if err != nil {
		return "", stderr.Wrap(err, "Get instance [%s] log error.", id)
	}
	return strings.Join(lines, "\n"), nil
}

func (m *instanceManager) GetInstanceEvents(projectID string, id string) (string, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return "", err
	}
	if instance, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(id)); ok {
		return strings.Join(instance.Events(), "\n"), nil
	} else {
		return "", stderr.Internal.Error("instance [%s] is not running!", id)
	}
}

func (m *instanceManager) GetDockerfile(projectID string, id string) (string, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return "", err
	}
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		return "", stderr.Wrap(err, "InstanceManager: GetDockerfile method, get instance [%s] error.", id)
	}
	if instance.Dockerfile != "" {
		return instance.Dockerfile, nil
	}
	buf := new(bytes.Buffer)
	file, err := os.Open(fmt.Sprintf("etc/dockerfiles/%s.Dockerfile", string(instance.DataType)))
	if err != nil {
		return "", stderr.Wrap(err, "InstanceManager: GetDockerfile method, open [%s] dockerfile error.", string(instance.Type))
	}
	_, err = buf.ReadFrom(file)
	if err != nil {
		return "", stderr.Wrap(err, "InstanceManager: GetDockerfile method, read [%s] dockerfile error.", string(instance.Type))
	}
	content := buf.String()
	tmpl, err := template.New("").Parse(content)
	if err != nil {
		return "", stderr.Wrap(err, "InstanceManager: GetDockerfile method, parse template error.")
	}
	toParse := map[string]interface{}{
		"baseImage": instance.Image.Repo,
	}
	result := new(bytes.Buffer)
	err = tmpl.Execute(result, toParse)
	if err != nil {
		return "", stderr.Wrap(err, "InstanceManager: GetDockerfile method, execute [%s] dockerfile template error.", string(instance.Type))
	}
	return result.String(), nil
}

func (m *instanceManager) CountInstance(projectID string) (int64, error) {
	repo := m.q.Instance
	count, err := repo.Where(repo.ProjectID.Eq(projectID)).Count()
	if err != nil {
		return 0, stderr.Wrap(err, "Count instance error.")
	}
	return count, nil
}

func (m *instanceManager) GetPodInfo(projectID string, id string) (*models.PodInfo, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return nil, err
	}
	if instance, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(id)); ok {
		pod, err := instance.GetPod()
		if err != nil {
			return nil, stderr.Wrap(err, "Get instance [%s] pod error.", id)
		}
		return &models.PodInfo{
			PodIp:       pod.Status.PodIP,
			NodeName:    pod.Spec.NodeName,
			ServiceName: codehub.InstanceName(id),
		}, nil
	} else {
		return nil, stderr.Internal.Error("Instance is not running!")
	}
}

func (m *instanceManager) CreateInstanceService(projectID, id string, infos []*models.NodePortServiceInfo) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	exists, err := m.ListInstanceServices(projectID, id)
	if err != nil {
		return err
	}
	if instance, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(id)); ok {
		// 全部删除
		nameSet := make(map[string]struct{})
		for _, info := range exists {
			nameSet[info.Name] = struct{}{}
		}
		for name, _ := range nameSet {
			err := m.DeleteInstanceService(projectID, id, name)
			stdlog.WithError(err).Warnf("Delete service [%s] error.", name)
		}
		configs := make([]*codehub.NodePortConfig, 0)
		for _, info := range infos {
			configs = append(configs, &codehub.NodePortConfig{
				NodePort:  info.NodePort,
				InnerPort: info.InnerPort,
			})
		}
		name := fmt.Sprintf("%s-np", codehub.InstanceName(id))
		_, err := codehub.GetKubeCodehub().StartNodePortService(instance, name, configs)
		if err != nil {
			return stderr.Internal.Cause(err, "Create node port service error")
		}
		return nil
	}
	return stderr.Internal.Errorf("instance [%s] is not running!", id)
}

func (m *instanceManager) ListInstanceServices(projectID, id string) ([]*models.NodePortServiceInfo, error) {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return nil, err
	}
	if instance, ok := codehub.GetKubeCodehub().Instances.Get(codehub.InstanceName(id)); ok {
		services, err := codehub.GetKubeCodehub().ServiceLister.List(labels.SelectorFromSet(map[string]string{
			codehub.CODEHUB_INSTANCE:      "true",
			codehub.CODEHUB_INSTANCE_NAME: instance.Name,
		}))
		if err != nil {
			return nil, stderr.Wrap(err, "List instance [%s] services error.", id)
		}
		ret := make([]*models.NodePortServiceInfo, 0)
		for _, service := range services {
			if service.Spec.Type == v1.ServiceTypeNodePort {
				for _, port := range service.Spec.Ports {
					ret = append(ret, &models.NodePortServiceInfo{
						Name:      service.Name,
						NodePort:  port.NodePort,
						InnerPort: port.Port,
					})
				}
			}
		}
		return ret, nil
	}
	return nil, stderr.Internal.Errorf("instance [%s] is not running!", id)
}

func (m *instanceManager) DeleteInstanceService(projectID, id, name string) error {
	if err := m.checkProjectIDAndInstanceID(projectID, id); err != nil {
		return err
	}
	instance, err := m.GetInstance(projectID, id)
	if err != nil {
		return stderr.Internal.Cause(err, "get instance [%d] error.", id)
	}
	return codehub.GetKubeCodehub().StopNodePortService(name, instance.TenantId)
}

func (m *instanceManager) checkProjectIDAndInstanceID(projectID, id string) error {
	if projectID == "all" {
		return nil
	}
	repo := m.q.Instance
	ret, err := repo.Where(repo.ID.Eq(id)).First()
	if err != nil {
		return err
	}
	if ret.ProjectID != projectID {
		return stderr.Internal.Error("instance id with wrong project id")
	}
	return nil
}
