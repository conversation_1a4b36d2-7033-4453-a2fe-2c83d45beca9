<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.transwarp</groupId>
        <artifactId>sophon-parent</artifactId>
        <version>2.6.0-SNAPSHOT</version>
    </parent>

    <groupId>io.transwarp</groupId>
    <artifactId>sophon-core</artifactId>
    <packaging>jar</packaging>

    <properties>
        <nd4j.version>0.8.0</nd4j.version>
        <dl4j.version>0.8.0</dl4j.version>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/../${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <name>sophon-core</name>
    <description>A distributed backend for machine learning</description>

    <dependencies>
        <dependency>
            <groupId>io.transwarp.aip</groupId>
            <artifactId>aip-commons-basic</artifactId>
            <version>${aip.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp.aip</groupId>
            <artifactId>aip-commons-cache</artifactId>
            <version>${aip.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp.aip</groupId>
            <artifactId>aip-commons-messagebus</artifactId>
            <version>${aip.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp.aip</groupId>
            <artifactId>aip-commons-kubernetes</artifactId>
            <version>${aip.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-commons</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-kg-graph</artifactId>
            <version>${kg.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.esotericsoftware.kryo</groupId>
                    <artifactId>kryo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.transwarp</groupId>
                    <artifactId>sophon-storage</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.transwarp</groupId>
                    <artifactId>stellarjdbc-driver-shaded</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>spark-extensions-graph</artifactId>
            <version>${spark.extension.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.esotericsoftware.kryo</groupId>
                    <artifactId>kryo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>graphframes</artifactId>
            <version>${graphframe.version}</version>
        </dependency>
        <dependency>
            <groupId>org.lmdbjava</groupId>
            <artifactId>lmdbjava</artifactId>
            <version>0.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>spark-extension-ml</artifactId>
            <version>${spark.extension.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>spark-extension-timeseries</artifactId>
            <version>${spark.extension.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>spark-extension-statistic</artifactId>
            <version>${spark.extension.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>spark-extension-autoai</artifactId>
            <version>${spark.extension.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-mllib_${scala.binary.version}</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-yarn_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-hive_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.clapper</groupId>
            <artifactId>classutil_${scala.binary.version}</artifactId>
            <version>1.1.2</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-dataset</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql-kafka-0-10_${scala.binary.version} </artifactId>
            <version>${spark.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>net.jpountz.lz4</groupId>
                    <artifactId>lz4</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-dataset</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>sophon-code</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>hubble</artifactId>
            <version>${spark.extension.version}</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>model-json</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp</groupId>
            <artifactId>transwarp-nlp</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.transwarp.aip</groupId>
            <artifactId>apimanager-client</artifactId>
            <version>${api.manager.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.transwarp</groupId>
                    <artifactId>sophon-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>5.1.3.RELEASE</version>
        </dependency>
    </dependencies>
</project>
