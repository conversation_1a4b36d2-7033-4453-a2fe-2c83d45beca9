annotations: null
component_advanced_config:
  enable_cached: false
  parallelism: 0
  retry_strategy: null
  service_account_name: ""
component_source:
  source_id: ""
  source_info: ""
  source_name: ""
  template_type: 1
container:
  cmds:
    - python3
  args:
    - '{{params.code-file}}'
  container_type: 0
  envs: null
  image: ""
  image_pull_policy: 0
  lifecycle: null
  livenessProbe: null
  mount_paths: null
  name: ""
  ports: null
  readinessProbe: null
  resource_id: null
  resource_requirements: null
  security_context:
    privileged: false
    run_as_user: 0
desc:
desc_en: batch prediction
host_network: true
id: C00005
inputs:
  data: /artifact/input0
name: 代码实例
name_en: code instance
outputs:
  dataset-id: /artifact/output0
  storage-dir: /artifact/output1
labels: null
node_selector: null
params:
  code_id: ''
  code_py: ''
use_defined: true
volume: null
