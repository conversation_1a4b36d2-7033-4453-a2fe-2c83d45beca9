package knowledge_base

import (
	"context"
	"encoding/json"
	"strconv"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models"
	docEngine "transwarp.io/applied-ai/doc-engine-go/v1"
)

type DocEngineHandler struct {
	*HandlerBase
	Cli        *docEngine.DocEngineClient
	GroupId    string
	ProjectId  string
	TenantId   string
	IsExternal bool
}

func NewDocEngineHandler(ctx context.Context, kb *models.KnowledgeBase) (s StoreHandler, err error) {
	h := &DocEngineHandler{
		HandlerBase: &HandlerBase{
			KnowledgeBase: kb,
			RetrieveStrategies: []pb.KnowledgeBaseRetrieveStrategy{
				pb.KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE,
			},
		},
		Cli:        nil, // lazy init
		GroupId:    kb.Id,
		ProjectId:  kb.ProjectId,
		TenantId:   "",
		IsExternal: false,
	}

	h.Cli, err = clients.GetDocEngineCli(ctx)
	if err != nil {
		return nil, err
	}
	return h, nil
}

func (h *DocEngineHandler) CreateKnowledgeBase(ctx context.Context, req *docEngine.CreateKnowledgeBaseReq) (*docEngine.CreateKnowledgeBaseRsp, error) {
	kb, err := h.Cli.CreateKnowledgeBase(ctx, req)
	return kb, err
}

func (h *DocEngineHandler) GetKnowledgeBase(ctx context.Context) (*docEngine.GetKnowledgeBaseRsp, error) {
	kb, err := h.Cli.GetKnowledgeBase(ctx, h.GroupId)
	return kb, err
}

func (h *DocEngineHandler) ListDocuments(ctx context.Context) (*pb.ListDocumentsRsp, error) {
	docs, err := h.Cli.GetDocs(ctx, h.GroupId)
	if err != nil {
		return nil, err
	}
	layout := "2006-01-02 15:04:05"
	result := make([]*pb.DocumentInfo, 0)
	if len(docs) != 0 {
		for _, doc := range docs[0].FurDocInfos {
			t, er := time.Parse(layout, doc.FurFileTime)
			if er != nil {
				return nil, stderr.Wrap(er, "DocEngineHandler.ListDocuments")
			}
			d := &pb.Document{
				DocId:              doc.FurDocId,
				DocName:            doc.OrgFileName,
				FileSizeBytes:      int32(doc.OrgFileSize),
				UploadTimeMills:    t.UnixMilli(),
				KnowledgeBaseId:    h.GroupId,
				DocumentFileSource: pb.DocumentFileSource_LOCAL_FILE,
			}
			percentage, finished := 0, false
			if doc.FurFileState == "Success" {
				percentage, finished = 100, true
			}
			dpp := &pb.DocumentProcessingProgress{
				Document:   d,
				Percentage: float32(percentage),
				Finished:   finished,
				Stage:      pb.DocumentTaskStage_DONE,
			}
			result = append(result, &pb.DocumentInfo{
				Doc:            d,
				Prog:           dpp,
				StrategyOrigin: pb.StrategyOrigin_DOC_ENGINE,
			})
		}
	}
	return &pb.ListDocumentsRsp{Result: result}, err
}

func (h *DocEngineHandler) ListDocumentChunks(ctx context.Context, docId string, pageReq *pb.PageReq) (*pb.ListDocumentChunksRsp, error) {
	return h.RetrieveChunks(ctx, docId, pageReq, "")
}

func (h *DocEngineHandler) RetrieveChunks(ctx context.Context, docId string, pageReq *pb.PageReq, keyword string) (*pb.ListDocumentChunksRsp, error) {
	req := &docEngine.RetrieveChunksReq{
		GroupName: h.GroupId,
		DocId:     docId,
		PageNum:   int(pageReq.Page),
		PageSize:  int(pageReq.PageSize),
	}
	if keyword != "" {
		req.Keyword = keyword
	}
	chunks, err := h.Cli.RetrieveChunks(ctx, req)
	if err != nil {
		return nil, err
	}
	result := make([]*pb.ChunkInfo, 0, len(chunks.Nodes))
	for _, c := range chunks.Nodes {
		ch := &pb.Chunk{
			Id:         strconv.Itoa(c.Id),
			Content:    c.Content,
			SourceType: pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
			Edited:     false,
		}
		result = append(result, &pb.ChunkInfo{
			Chunk: ch,
		})
	}
	rsp := &pb.ListDocumentChunksRsp{
		Result: result,
		Total:  chunks.Total,
	}
	return rsp, err
}

func (h *DocEngineHandler) ListDocumentChunksByReq(ctx context.Context, req *pb.ListDocumentChunksReq) (*pb.ListDocumentChunksRsp, error) {
	return h.RetrieveChunks(ctx, req.DocId, req.PageReq, req.SearchContent)
}

func (h *DocEngineHandler) UploadFiles(ctx context.Context, filePaths []string) ([]*pb.DocumentProcessingProgress, error) {
	res, err := h.Cli.UploadFiles(ctx, h.GroupId, filePaths)
	if err != nil {
		return nil, err
	}
	docs := res[0].FurDocInfos
	layout := "2006-01-02 15:04:05"
	rsp := make([]*pb.DocumentProcessingProgress, 0, len(docs))
	docIds := make([]string, 0, len(docs))
	for _, doc := range docs {
		t, er := time.Parse(layout, doc.FurFileTime)
		if er != nil {
			return nil, stderr.Wrap(er, "DocEngineHandler.UploadFiles")
		}
		d := &pb.Document{
			DocId:              doc.FurDocId,
			DocName:            doc.OrgFileName,
			FileSizeBytes:      int32(doc.OrgFileSize),
			UploadTimeMills:    t.UnixMilli(),
			KnowledgeBaseId:    h.GroupId,
			DocumentFileSource: pb.DocumentFileSource_LOCAL_FILE,
		}
		percentage, finished := 0, false
		if doc.FurFileState == "Success" {
			percentage, finished = 100, true
		}
		dpp := &pb.DocumentProcessingProgress{
			Document:   d,
			Percentage: float32(percentage),
			Finished:   finished,
			Stage:      pb.DocumentTaskStage_DONE,
		}
		rsp = append(rsp, dpp)
		docIds = append(docIds, d.DocId)
	}
	indexReq := &docEngine.DocReq{
		DocIds: docIds,
	}
	indexRsp, err := h.CreateIndex(ctx, indexReq)
	if err != nil {
		return nil, stderr.Wrap(err, indexRsp.Result)
	}
	return rsp, err
}

func (h *DocEngineHandler) CreateIndex(ctx context.Context, req *docEngine.DocReq) (*docEngine.StandardRsp, error) {
	rsp, err := h.Cli.CreateIndex(ctx, h.GroupId, req)
	return rsp, err
}

func (h *DocEngineHandler) Recall(ctx context.Context, req *pb.RetrieveKnowledgeBaseReq) (*pb.RetrieveKnowledgeBaseRsp, error) {
	docReq := &docEngine.RetrieveSingleKnowledgeReq{
		DocNames:  req.DocRange,
		Query:     req.Query,
		GroupName: h.GroupId,
		Type:      docEngine.GraphPyCode,
	}
	res, err := h.Cli.RetrieveSingleKnowledge(ctx, docReq)
	if err != nil {
		return nil, err
	}
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(res.Result))
	for _, c := range res.Result {
		stdlog.Infof("node is %s", c.Node)
		ch := &pb.Chunk{
			Id:         strconv.Itoa(c.Node.Id),
			Content:    c.Node.Content,
			SourceType: pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
			Edited:     false,
		}
		chunks = append(chunks, &pb.ChunkRetrieveResult{
			Chunk:           ch,
			KnowledgeBaseId: c.GroupName,
			DocId:           c.DocId,
			DocName:         c.DocName,
		})
		stdlog.Infof("chunk is %s", ch)
	}
	return &pb.RetrieveKnowledgeBaseRsp{
		Request: req,
		Result:  chunks,
	}, nil
}

func (h *DocEngineHandler) CrossRecall(ctx context.Context, req *pb.RetrieveCrossKnowledgeBaseReq) (*pb.RetrieveCrossKnowledgeBaseRsp, error) {
	ranges := make([]docEngine.RetrieveDoc, 0)
	stdsrv.SyncBatchCallGeneric(req.Ranges, func(rr *pb.RetrieveRange) error {
		group := docEngine.RetrieveDoc{
			DocNames:  rr.DocRange,
			GroupName: rr.KnowledgeBaseId,
		}
		ranges = append(ranges, group)
		return nil
	})
	docReq := &docEngine.RetrieveMultiKnowledgeReq{
		Ranges:   ranges,
		Query:    req.Query,
		ConfType: docEngine.GraphPyCode,
	}
	res, err := h.Cli.RetrieveMultiKnowledge(ctx, docReq)
	if err != nil {
		return nil, err
	}
	chunks := make([]*pb.ChunkRetrieveResult, 0, len(res.Result))
	for _, c := range res.Result {
		ch := &pb.Chunk{
			Id:         strconv.Itoa(c.Node.Id),
			Content:    c.Node.Content,
			SourceType: pb.ChunkSourceType_SOURCE_TYPE_GENERATED,
			Edited:     false,
		}
		chunks = append(chunks, &pb.ChunkRetrieveResult{
			Chunk:           ch,
			KnowledgeBaseId: c.GroupName,
			DocId:           c.DocId,
			DocName:         c.DocName,
		})
	}
	return &pb.RetrieveCrossKnowledgeBaseRsp{
		Request: req,
		Result:  chunks,
	}, nil
}

func (h *DocEngineHandler) AsyncSubmitFileToKnowledgeBase(filePath string) {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) GetDocumentTree(ctx context.Context) (*pb.GetDocumentTreeRsp, error) {
	root := new(pb.DocumentTree)
	root.Node = &pb.DocumentNode{
		Category: pb.DocumentNodeCategory_DIR,
		Id:       h.KnowledgeBase.Id,
		Name:     h.KnowledgeBase.Name,
	}

	docs, err := h.ListDocuments(ctx)
	if err != nil {
		return nil, err
	}
	for _, doc := range docs.Result {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       doc.Doc.DocId,
				Name:     doc.Doc.DocName,
			},
		}
		root.Children = append(root.Children, subTree)
	}
	if len(docs.Result) == 0 {
		subTree := &pb.DocumentTree{
			Node: &pb.DocumentNode{
				Category: pb.DocumentNodeCategory_FILE,
				Id:       "全量文本",
				Name:     "全量文本",
			},
		}
		root.Children = append(root.Children, subTree)
	}
	return &pb.GetDocumentTreeRsp{Tree: root}, nil
}

func (h *DocEngineHandler) CountDocuments(ctx context.Context) (int32, error) {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) RemoveFileFromKnowledgeBase(ctx context.Context, req *pb.RemoveFileFromKnowledgeBaseReq) error {
	delDocReq := &docEngine.DocReq{
		DocIds: []string{req.DocId},
	}
	rsp, err := h.Cli.DeleteDocs(ctx, h.GroupId, delDocReq)
	if err != nil {
		stderr.Wrap(err, rsp.Result)
	}
	return err
}

func (h *DocEngineHandler) SupportedRetrieveStrategies() []pb.KnowledgeBaseRetrieveStrategy {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) SubmitChunks(ctx context.Context, chunks []*models.ChunkForIndexing) error {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) DeleteChunksById(ctx context.Context, chunkIds []string) error {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) DeleteChunksByOriId(ctx context.Context, oriChunkIds []string) error {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) DeleteChunksByDocId(ctx context.Context, docId string) error {
	panic("not implemented") // TODO: Implement
}

func (h *DocEngineHandler) Drop(ctx context.Context) error {
	rsp, err := h.Cli.DeleteKnowledgeBase(ctx, h.GroupId)
	if err != nil {
		stderr.Wrap(err, "Drop kb of doc engine: "+rsp.Result)
	}
	return err
}

func (h *DocEngineHandler) UpdateKnowledgeBase(ctx context.Context) error {
	docSchema := new(docEngine.EntitySchemaConf)
	err := json.Unmarshal([]byte(h.KnowledgeBase.DocProcessingConfig.DocLoadConfig.EntitySchemaConf), &docSchema)
	if err != nil {
		return stderr.Wrap(err, "transfer the schema of doc engine failed!")
	}
	req := &docEngine.UpdateKnowledgeBaseReq{
		IndexBuildConf: docEngine.IndexBuildConfReq{
			IndexTypeArr: docEngine.DefaultIndexType(),
		},
		EntitySchemaConf: *docSchema,
	}
	rsp, err := h.Cli.UpdateKnowledgeBase(ctx, h.GroupId, req)
	if err != nil {
		stderr.Wrap(err, "Update kb of doc engine: "+rsp.Result)
	}
	return err
}
